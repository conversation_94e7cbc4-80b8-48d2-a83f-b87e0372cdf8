# console-csm

服务网格公有云控制台

## 开发者须知

在开始开发前，请确保你阅读了 [JavaScript-React 项目实践指南](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/ee-fe/javascript-react-cookbook)，如有任何问题，可通过如流搜索 1508631 加入群聊。

## 开发

1. 确保 `node` 和 `yarn` 都是最新版本。
2. 修改本地 host，localhost.qasandbox.bcetest.baidu.com 解析到 127.0.0.1
3. ```shell
    yarn
    yarn start
    ```
4. 访问 https://localhost.qasandbox.bcetest.baidu.com:8889/
5. 如果跳转登录使用 faas_qa  123qwe 登录后，重新访问上面 url 即可
6. 如果跳转后没有登录框，https://passport.qatest.baidu.com/passApi/js/wrapper.js?cdnversion=1649998005693&_=1649998004734   https://wappass.qatest.baidu.com/static/waplib/moonshad.js?tt=1649998876172 这两个文件打开，允许https


如果希望使用 canary 环境

1. 修改本地 host，localhost.console.bce.baidu.com 解析到 127.0.0.1
2. ```shell
    yarn start
    ```
3. 访问 https://localhost.console.bce.baidu.com:8889/
4. 如果跳转登录，重新使用 http://ecadfe8100b34909b34fb89164862968.login.bce.baidu.com 的内网登录
5. 重新访问 https://localhost.console.bce.baidu.com:8889/
## 开发环境

* npm start 用的沙盒接口
* npm run start:mock 用的 yapi 接口
* npm run start:canary 用的线上账号和线上灰度环境的流量

## 代码检查

```shell
yarn lint
```

设置有提交前的 Git Hook，会自动进行检查，如检查不通过无法提交代码。

## 构建

```shell
yarn build
```

## 图标库地址

http://yunshe.baidu-int.com/v2/components

## 线上灰度访问

http://ecadfe8100b34909b34fb89164862968.login.bce.baidu.com
访问地址，选择内网账号登录


灰度插件下载 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/_60NPvWC5M/hw83Hobq-kCfe6


打开 https://console.bce.baidu.com/csm/


打开插件，填写对应版本号，选择产品 csm ，选择对应环境，打开开关


添加权限联系 liuchao15