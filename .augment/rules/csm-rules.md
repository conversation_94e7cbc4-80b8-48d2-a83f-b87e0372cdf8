---
type: "always_apply"
---

在代码生成时请将任务拆分成以下两步：

Step1: 实现主体结构与样式
1. 注意使用数据驱动渲染，先不要实现具体的数据获取逻辑。
2. 理解代码的意图，合理使用acud组件库代替基于原生标签的实现
 - 读取acud组件库速查指南: .augment/rules/acud-use-doc.mdc。分析哪些结构可以使用组件库，积极的使用acud组件库代替基于原生实现。
 - 读取acud组件的详细属性: .augment/rules/acud-component.mdc，严格按照文档中的指南的使用acud组件，不要随意增加不存在的属性。
 - 读取acud-icon支持的图标库: .augment/rules/acud-icon.mdc。合理引入图标。
3. 图片资源保持原有的url不要修改。
Step2: 实现数据请求接口
1. 参照接口文档，实现数据请求逻辑。参考文档如下：
   - 读取api规范：api-use-doc.mdc
   - 读取api文档目录：接口文档/

注意事项：
1. 实现的组件放到src/pages下。
2. 实现的数据请求放到src/apis下。
3. 确保组件可复用性，将通用逻辑抽象为独立组件。
4. 遵循React最佳实践，避免不必要的渲染和副作用。
5. 严格按照设计稿内容去实现组件，不要扩展其他内容。