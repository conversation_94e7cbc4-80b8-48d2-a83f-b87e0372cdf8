{"name": "console-csm", "version": "0.0.1", "description": "服务网格公有云控制台", "scripts": {"test": "jest", "start": "skr dev", "start:mock": "DEV_PROXY=mock skr dev", "start:canary": "DEV_PROXY=canary skr dev", "build": "skr build --clean", "lint": "skr lint", "lint-staged": "npm run lint -- --staged", "push:cr": "git push origin HEAD:refs/for/$(git symbolic-ref --short -q HEAD)"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/bce-console/console-csm"}, "author": "w<PERSON><PERSON><PERSON>", "license": "UNLICENSED", "dependencies": {"@baidu/acud-icon": "^0.0.18", "@baidu/app-space-tools": "^1.0.0", "@baidu/ee-icon": "^9.27.0", "@baidu/icloud-generator": "0.2.6", "@baidu/icloud-ui-action-button": "^5.0.0", "@baidu/icloud-ui-activation-page": "^5.0.1", "@baidu/icloud-ui-card": "^5.0.1", "@baidu/icloud-ui-date-time": "^5.0.0", "@baidu/icloud-ui-dropdown-select": "^5.0.0", "@baidu/icloud-ui-ellipsis": "^5.0.0", "@baidu/icloud-ui-error": "^5.0.0", "@baidu/icloud-ui-form": "5.1.2", "@baidu/icloud-ui-group-input": "^5.0.0", "@baidu/icloud-ui-page-layout": "^5.2.3", "@baidu/icloud-ui-pro-table": "^5.0.4", "@baidu/icloud-ui-state-tag": "^5.0.0", "@baidu/icloud-ui-text": "^5.0.0", "@baidu/lsqm-icon": "^7.7.1", "@baidu/weirwood-sdk": "^1.3.6", "@huse/router": "^0.11.1", "@monaco-editor/react": "^4.4.1", "@osui/icloud-theme": "^2.1.15", "@osui/icons": "2.1.9", "@osui/icons-icloud": "2.1.9", "@osui/ui": "2.1.18", "@types/js-cookie": "^3.0.1", "@types/lodash": "^4.14.181", "@types/styled-components": "^5.1.24", "acud": "^1.4.38", "antd": "^4.23.2", "axios-interface": "^1.4.5", "constate": "^3.3.0", "echarts": "^5.3.2", "echarts-for-react": "^3.0.2", "huse": "^2.0.3", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.29.1", "monaco-editor": "^0.33.0", "postcss": "^8.4.12", "postcss-less": "^6.0.0", "query-string": "^7.1.1", "react": "^17.0.2", "react-copy-to-clipboard": "^5.0.4", "react-dom": "^17.0.2", "react-monaco-editor": "^0.47.0", "react-omni-link": "^2.1.1", "react-router-dom": "5.3.0", "react-suspense-boundary": "^2.1.0", "styled-components": "^5.3.5", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@baidu/reskript-plugins": "^5.0.2", "@reskript/cli": "5.2.0", "@reskript/cli-build": "5.2.0", "@reskript/cli-dev": "5.2.0", "@reskript/cli-lint": "5.2.0", "@reskript/config-lint": "5.2.0", "@reskript/settings": "5.2.0", "@types/jest": "^29.2.5", "@types/react": "^17.0.16", "@types/react-dom": "^17.0.9", "@types/uuid": "^9.0.8", "babel-jest": "^29.3.1", "core-js": "^3.21.1", "eslint": "^8.11.0", "husky": "^4.3.8", "jest": "^29.3.1", "stylelint": "^14.5.3", "ts-jest": "^29.0.5", "typescript": "^4.9.4", "webpack": "^5.70.0"}, "husky": {"hooks": {"pre-commit": "npm run lint-staged"}}}