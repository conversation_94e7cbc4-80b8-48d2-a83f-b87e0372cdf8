Global:
  version: 2.0

Default:
  profile: [app]

Profiles:
  - profile:
    name: app
    mode: AGENT
    environment:
      image: DECK_CENTOS7U5_K3
      tools:
              - nodejs: 18.3.0
    check:
      - reuse: TASK
        enable: true
    build:
      command: sh scripts/build.sh --build-target=$BUILD_TARGET --cache-dir=build-cache/webpack
    cache:
      enable: true
      paths:
        - build-cache
    artifacts:
      release: true
