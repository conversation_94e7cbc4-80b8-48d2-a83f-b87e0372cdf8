import {createRequire} from 'module';
import crypto from 'crypto';
import {configure} from '@reskript/settings';
import {osui} from '@baidu/reskript-plugins';
import {autoDeploy} from '@baidu/app-space-tools/reskript';

const proxyConfig = process.env.DEV_PROXY === 'mock'
    ? [{
        context: [
            '/api/csm',
        ],
        changeOrigin: true,
        secure: false,
        target: 'https://yapi.baidu-int.com/mock/27106',
    },
    {
        context: [
            '/api',
        ],
        changeOrigin: true,
        secure: false,
        target: 'https://qasandbox.bcetest.baidu.com',
    }]
    : process.env.DEV_PROXY === 'canary'
        ? [{
            context: [
                '/api',
            ],
            target: 'https://console.bce.baidu.com/',
            changeOrigin: true,
            secure: true,
            onProxyReq(proxyReq: any) {
                const hash = crypto.randomBytes(12).toString('hex').slice(0, 8);
                proxyReq.setHeader('x-bce-request-id', `${hash}-773f-gray-csm0-bcecanarytag`);
                // 某些接口会验证origin
                proxyReq.setHeader('origin', 'https://console.bce.baidu.com');
            },
        }]
        : [{
            context: [
                '/api',
            ],
            changeOrigin: true,
            secure: false,
            target: 'https://qasandbox.bcetest.baidu.com',
        }];

const require = createRequire(import.meta.url);
export default configure(
    'webpack',
    {
        build: {
            appTitle: 'console-csm',
            style: {
                // 以下内容是需要添加配置的
                lessVariables: {'ant-prefix': 'ant'}, // antd css class的前缀
                resources: [
                    // 对antd变量的覆盖
                    require.resolve('@osui/icloud-theme/dist/antd-vars-patch.less'),
                    // 使用less-functions-overrides覆盖less函数，让css variables通过less编译
                    require.resolve('@osui/icloud-theme/dist/less-functions-overrides.less'),
                ],
            },
            finalize: config => {
                // icloud主题icon的替换
                config.resolve.alias['@osui/icons'] = '@osui/icons-icloud';
                return config;
            },
        },
        devServer: {
            port: 8889,
            https: {
                client: true,
            },
            finalize: devServerConfig => {
                devServerConfig.proxy = proxyConfig;
                return devServerConfig;
            },
        },
        plugins: [
            osui(),
            autoDeploy(),
        ],
    }
);
