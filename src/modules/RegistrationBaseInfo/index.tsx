/* eslint-disable */
import React from 'react';
import Card from '@baidu/icloud-ui-card';
import StateTag from '@baidu/icloud-ui-state-tag';
import {Row, Col, Table} from '@osui/ui';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {useFramework} from '@/hooks';
import PageLoading from '@/components/PageLoading';
import {useCurrentRegistrationInstance} from '@/modules/RegistrationInstance';
import {RegistractionStatusDict} from '@/dicts/registration';
import {ChangeEsg} from './registrationChangeEsg';

const getColumns = (instanceId: string) => {
    return [
        {
            title: '私有网络',
            key: 'vpcId',
            dataIndex: 'vpcId',
            render(value: string, record: any) {
                return (
                    <a href="https://console.bce.baidu.com/network/#/vpc/instance/list" target="_blank" rel="noreferrer">{`${record.vpcName || '-'}(${value})`}</a>
                );
            },
        },
        {
            title: '子网',
            key: 'subnetId',
            dataIndex: 'subnetId',
            render(value: string, record: any) {
                return (
                    <a href="https://console.bce.baidu.com/network/#/vpc/subnet/list" target="_blank" rel="noreferrer">{`${record.subnetName || '-'}(${value})`}</a>
                );
            },
        },
        {
            title: '内网地址',
            key: 'ip',
            dataIndex: 'ip',
        },
        {
            title: '访问控制',
            render(_: any, record: any) {
                const {enterpriseSecurityGroupName, enterpriseSecurityGroupId, id} = record;
                return (
                    <>
                        {enterpriseSecurityGroupId
                            && <ExternalLink
                                href={urls.external.enterpriseSecurityList.fill({})}
                                value={`${enterpriseSecurityGroupName || '-'}(${enterpriseSecurityGroupId || '-'})`}
                            />}
                        <ChangeEsg esgId={enterpriseSecurityGroupId} id={id} instanceId={instanceId} />
                    </>
                );
            },
        },
    ];
};

export default function RegistrationInstanceDetail() {
    const data = useCurrentRegistrationInstance();
    const {region: {regionList}} = useFramework();

    if (!data.name) {
        return <PageLoading />;
    }
    const statusItem = RegistractionStatusDict.get(data.status);

    const getServerPort = (data: any) => {
        // 将字符串转换为数组
        const ports = data?.serverPort.split(',');
        const protocols = data?.serverProtocol.split(',');
        if (!ports || !protocols) {
            return '-';
        }
        // 生成所需的格式
        return ports.map((port: string, index: number) => `${port} (${protocols[index]})`).join(' 、 ');
    };

    return (
        <>
            <Card
                title="基本信息"
            >
                <Row gutter={[48, 16]}>
                    <Col span={8}>
                        <Card.Field title="Id">
                            {data.id || '-'}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="地域">
                            {regionList[data.region] || '-'}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="状态">
                            {
                                statusItem ? <StateTag type={statusItem.value}>{statusItem.text}</StateTag> : '-'
                            }
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="名称">
                            {data.name}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="创建时间">
                            {data.createTime}
                        </Card.Field>
                    </Col>
                </Row>
            </Card>
            <Card
                title="Server访问地址"
            >
                <Row gutter={[48, 16]}>
                    <Col span={24}>
                        <Card.Field title="协议兼容端口">
                            {getServerPort(data)}
                        </Card.Field>
                    </Col>
                    <Col span={24}>
                        <Card.Field title="内网负载均衡">
                            <Table
                                dataSource={data.loadBalanceList || []}
                                columns={getColumns(data.id)}
                                pagination={false}
                            />
                        </Card.Field>
                    </Col>
                </Row>
            </Card>
        </>
    );
}
