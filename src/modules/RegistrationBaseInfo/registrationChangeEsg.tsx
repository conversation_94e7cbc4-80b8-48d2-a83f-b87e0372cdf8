import React, {useCallback, useState, useMemo, useContext} from 'react';
import {Modal, Form, Popover, Select} from 'acud';
import {QuestionCircleOutlined, EditTwoTone} from '@ant-design/icons';
import {useRequestCallback} from 'huse';
import {getEsgInstanceListApi, putRegistrationInstanceApi} from '@/api/registration';
import {CurrentRegistrationInstanceContext} from '@/modules/RegistrationInstance';

import c from './index.less';


export function ChangeEsg(props: {
    esgId: string;
    id: string; // 服务网卡ID
    instanceId: string; // 实例ID
}) {
    const {esgId, id, instanceId} = props;
    const {getRequest} = useContext(CurrentRegistrationInstanceContext);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [formDataInstante] = Form.useForm();
    const [getInstanceList, {pending: esgLoading, data: _esgInstanceList}] = useRequestCallback(getEsgInstanceListApi, {
        pageNo: 1,
        pageSize: 1000,
    });

    const esgOptions = useMemo(
        () => {
            return _esgInstanceList?.result.map(({esgId, name}: { name: string, esgId: string }) => (
                {
                    label: `${name}(${esgId})`,
                    value: esgId,
                }
            )) ?? [];
        },
        [_esgInstanceList]
    );

    const onSubmit = useCallback(
        async () => {
            const esgValue = formDataInstante.getFieldValue('esgId');
            const payload = {
                registrationhInstanceId: instanceId,
                endpoints: [{
                    id,
                    enterpriseSecurityGroupId: esgValue,
                }],
            };
            await putRegistrationInstanceApi(payload).then(() => {
                setIsModalOpen(false);
            }).then(() => {getRequest();});
        },
        [instanceId, id, formDataInstante, getRequest]
    );

    const getEsgInstanceList = useCallback(
        async () => {
            await getInstanceList();
            formDataInstante.setFieldsValue(esgId);
            setIsModalOpen(true);
        },
        [getInstanceList, formDataInstante, esgId]
    );

    return (
        <>
            <EditTwoTone className={c['edit-icon']} onClick={getEsgInstanceList} />
            {isModalOpen && (
                <Modal
                    title="编辑访问策略"
                    visible
                    // eslint-disable-next-line react/jsx-no-bind
                    onCancel={() => setIsModalOpen(false)}
                    onOk={onSubmit}
                    width={610}
                >
                    <Form form={formDataInstante}>
                        <Form.Item
                            name="esgId"
                            label={
                                <>
                                    访问策略：{
                                        <Popover content={'选择需要绑定的企业安全组'}>
                                            <QuestionCircleOutlined />
                                        </Popover>
                                    }
                                </>
                            }
                            colon={false}
                        >
                            <Select
                                disabled={esgLoading}
                                loading={esgLoading}
                                defaultValue={esgId}
                                className={c['selectEsg']}
                                options={esgOptions}
                                placeholder="请选择"
                            />
                        </Form.Item>
                    </Form>
                </Modal>
            )}
        </>
    );
}
