import React, {ReactNode} from 'react';
import styled from 'styled-components';
import {R4} from '@/styles/radius';

interface ChartCardProps{
    title: ReactNode;
    children: ReactNode;
}

const Card = styled.div`
    background-color: white;
    border-radius: ${R4};
    padding: 24px;
    position: relative;
    min-height: 268px;
`;
const Title = styled.div`
    position: absolute;
    left: 24px;
    right: 24px;
    z-index: 100;
    font-size: var(--font-size-large);
`;

export default function ChartCard({children, title}: ChartCardProps) {
    return (
        <Card>
            <Title>{title}</Title>
            {children}
        </Card>
    );
}
