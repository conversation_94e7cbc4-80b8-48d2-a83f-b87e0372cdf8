import {useCallback, useEffect, useState} from 'react';
import {Button, Radio} from '@osui/ui';
import {OutlinedRefresh} from '@baidu/acud-icon';
import styled from 'styled-components';
import {useRequestCallback} from 'huse';
import {useFramework} from '@/hooks';
import {getInstancesOverviewApi, getServiceMeshOverviewListApi, getSidecarsOverviewApi} from '@/api/overview';
import ChartArea from './ChartArea';
import ServiceMeshInstanceList from './ServiceMeshInstanceList';

const ContainerStyled = styled.div`
    padding: 16px;
`;
const TitleContainerStyled = styled.div`
    display: flex;
    align-items: center;
    margin-bottom: 16px;
`;
const TitleStyled = styled.span`
    margin-right: 8px;
`;

const useApiWithLocalRegion = (api: (...params: any) => Promise<any>, region: string) => {
    const currentRegion = region;
    return useCallback(
        (...params) => {
            return api(...params, currentRegion);
        },
        [api, currentRegion]
    );
};

export default function Dashboard() {
    // 这里的切换就不要和全局的region联动了，因为如果选到全局，用户在其他页就没法切换地域了
    // @todo region 在接口上以一个参数的形式进去吧
    const {region: {regionList}} = useFramework();
    const [region, setRegion] = useState('bj');
    const options = Object.entries(regionList).map(([value, label]) => ({value, label}))
        .map(v => ({...v, disabled: ['fwh', 'hkg'].includes(v.value)}));

    const [getInstancesOverview, {data: regionData, pending: regionDataLoading}] = useRequestCallback(
        useApiWithLocalRegion(getInstancesOverviewApi, region),
        {_config: {region}}
    );
    const [getSidecarsOverview, {data: sidecarData, pending: sidecarDataLoading}] = useRequestCallback(
        useApiWithLocalRegion(getSidecarsOverviewApi, region),
        {_config: {region}}
    );
    const [getServiceMeshOverviewList, {data: instanceData, pending: instanceDataLoading}] = useRequestCallback(
        useApiWithLocalRegion(getServiceMeshOverviewListApi, region),
        {_config: {region}}
    );
    const getDashboardAllData = useCallback(
        () => {
            getInstancesOverview();
            getSidecarsOverview();
            getServiceMeshOverviewList();
        },
        [getInstancesOverview, getServiceMeshOverviewList, getSidecarsOverview]
    );

    // 地域单选切换事件
    const onRegionChange = useCallback(
        e => setRegion(e.target.value), // 设置单选的地域,
        []
    );
    // 刷新按钮事件
    const onRefresh = useCallback(
        () => {
            getDashboardAllData();
        },
        [getDashboardAllData]
    );
    useEffect(
        () => {
            getDashboardAllData();
        },
        [getDashboardAllData]
    );

    return (
        <ContainerStyled>
            <TitleContainerStyled>
                <TitleStyled>地域:</TitleStyled>
                <Radio.Group
                    value={region}
                    onChange={onRegionChange}
                    options={options}
                    optionType="button"
                />
                <div className="flex-auto"></div>
                <Button icon={<OutlinedRefresh />} onClick={onRefresh} />
            </TitleContainerStyled>
            <ChartArea
                regionData={regionData}
                sidecarData={sidecarData}
                regionDataLoading={regionDataLoading}
                sidecarDataLoading={sidecarDataLoading}
            />
            <ServiceMeshInstanceList instanceData={instanceData} loading={instanceDataLoading} />
        </ContainerStyled>
    );
}
