import React from 'react';
import styled from 'styled-components';
import {Spin, Tooltip} from '@osui/ui';
import {OutlinedQuestionCircle} from '@baidu/acud-icon';
import {GetInstancesOverviewResponse, GetSidecarsOverviewResponse} from '@/api/overview';
import RegionChart from './RegionChart';
import ChartCard from './ChartCard';
import RunningChart from './RunningChart';
import SidecarChart from './SidecarChart';

const LayoutStyled = styled.div`
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    grid-gap: 16px;
`;


const RunningTitle = (
    <div className="flex items-center">
        <span className="margin-right-8">运行状态</span>
        <Tooltip
            placement="top"
            title="进行中包含部署中和删除中状态；异常包含运行异常和部署失败的状态"
        >
            <OutlinedQuestionCircle />
        </Tooltip>
    </div>
);
const SpinContainer = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
`;
interface ChartAreaProps{
    regionData?: GetInstancesOverviewResponse;
    sidecarData?: GetSidecarsOverviewResponse;
    regionDataLoading: boolean;
    sidecarDataLoading: boolean;
}
export default function ChartArea({
    regionData,
    sidecarData = [],
    regionDataLoading,
    sidecarDataLoading,
}: ChartAreaProps) {
    return (
        <LayoutStyled>
            <ChartCard title="地域分布">
                {regionDataLoading
                    ? (<SpinContainer><Spin /></SpinContainer>)
                    : <RegionChart data={regionData?.groupByRegion ?? {}} />}
            </ChartCard>
            <ChartCard title={RunningTitle}>
                {regionDataLoading
                    ? (<SpinContainer><Spin /></SpinContainer>)
                    : <RunningChart data={regionData?.groupByStatus ?? {}} />}
            </ChartCard>
            <ChartCard title="网格规模">
                {sidecarDataLoading
                    ? (<SpinContainer><Spin /></SpinContainer>)
                    : <SidecarChart data={sidecarData} />
                }
            </ChartCard>
        </LayoutStyled>
    );
}
