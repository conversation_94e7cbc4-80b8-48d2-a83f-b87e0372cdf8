import StateTag from '@baidu/icloud-ui-state-tag';
import {floor, isNaN} from 'lodash';
import React from 'react';
import styled from 'styled-components';
interface RunningChartProps{
    data: {
        changing: number;
        exception: number;
        running: number;
    };
}
const Circle = styled.div`
    height: 100px;
    width: 100px;
    font-size: 34px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #F5F5F5;
    border-radius: 100%;
    margin-top: 63px;
`;
const CircleContainer = styled.div`
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-right: 24px;
    width: 40%;
`;
const CircleTitle = styled.div`
    color: #666666;
    margin-top: 10px;
`;
const StatusItem = styled.div`
    display: flex;
    justify-content: space-between;
    margin-bottom: 28px;
`;
const StatusContainer = styled.div`
    margin-top: 67px;
    flex: auto;
`;

export default function RunningChart({data}: RunningChartProps) {
    const {running, changing, exception} = data;
    const total = running + changing + exception;
    const health = floor(running / total * 100, 0);
    return (
        <div className="flex">
            <CircleContainer>
                <Circle>{isNaN(health) ? '--' : `${health}%`}</Circle>
                <CircleTitle>健康度</CircleTitle>
            </CircleContainer>
            <StatusContainer>
                <StatusItem>
                    <StateTag type="success">运行中</StateTag>
                    <div>{running ?? '--'}个</div>
                </StatusItem>
                <StatusItem>
                    <StateTag type="pending">变更中</StateTag>
                    <div>{changing ?? '--'}个</div>
                </StatusItem>
                <StatusItem>
                    <StateTag type="warning">异常</StateTag>
                    <div>{exception ?? '--'}个</div>
                </StatusItem>
            </StatusContainer>
        </div>
    );
}
