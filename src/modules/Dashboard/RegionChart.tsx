import React from 'react';
import ReactECharts from 'echarts-for-react';
import styled from 'styled-components';
import {useFramework} from '@/hooks';

interface RegionChartProps{
    data: Record<string, number>;
}
const ContainerStyled = styled.div`
    position: relative;
`;
const NumberStyled = styled.div`
    position: absolute;
    right: 0;
    top: 18px;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;

    div {
        margin-bottom: 17px;
    }
`;
export default function RegionChart({data}: RegionChartProps) {
    const {region: {regionList}} = useFramework();
    const list = Object.entries(data).map(([key, value]) => ({value, name: regionList[key]}));
    const option = {
        tooltip: {
            trigger: 'item',
        },
        color: ['#2468F2', '#A5E693', '#4CA7FF', '#99E2FF', '#6983BA'],
        legend: {
            left: '50%',
            top: 'center',
            right: 0,
            orient: 'vertical',
            itemGap: 20,
            icon: 'circle',
            formatter(name: string) {
                return name;
            },
        },
        series: [
            {
                name: '实例数量',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center',
                },
                labelLine: {
                    show: false,
                },
                right: '50%',
                data: list,
            },
        ],
    };
    return (
        <ContainerStyled>
            <ReactECharts style={{height: '220px'}} option={option} />
            <NumberStyled>
                {list.map(v => (<div key={v.name}>{v.value}</div>))}
            </NumberStyled>
        </ContainerStyled>

    );
}
