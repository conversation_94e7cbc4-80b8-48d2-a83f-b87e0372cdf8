.overview-mesh-instance-list-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    margin-top: 16px;
    min-height: 154px;

    .overview-mesh-instance-list {
        width: 100vw;
        font-size: var(--font-size-medium);
        font-family: var(--font-family-PingFangScRegular);
        color: var(--color-gray-9);
    }

    .overview-mesh-instance-item {
        background-color: #fff;
        margin: 16px 0;
        padding: 24px;
        border-radius: var(--border-radius-4);

        :global {
            .ant-list-item-meta-title,
            .ant-list-item-meta {
                margin-bottom: 0;
            }

            .icloud-ui-state-tag {
                font-size: var(--font-size-small);
                font-family: var(--font-family-PingFangScRegular);
            }

            .ant-card {
                padding: 16px 24px;
                font-size: var(--font-size-medium);
                font-family: var(--font-family-PingFangScRegular);
                height: 100%;
            }

            .ant-card-head,
            .ant-card-head-title,
            .ant-card-body {
                border: none;
                padding: 0;
                min-height: 0;
            }

            .ant-card-head-title {
                font-family: var(--font-family-PingFangScMedium);
                margin-bottom: 8px;
                display: flex;
                align-items: center;
            }

            .service-tip {
                font-size: var(--font-size-medium);
                color: var(--color-gray-7);
                margin-left: 8px;
                transition: color .3s;

                &:hover {
                    color: var(--color-brand-6);
                }
            }
        }

        // 网格实例名称和状态
        .instance-title {
            justify-content: space-between;
            font-size: var(--font-size-small);
            margin-bottom: 8px;

            .instance-name {
                opacity: .95;
                font-size: var(--font-size-large);
                font-family: var(--font-family-PingFangScMedium);

                a {
                    color: var(--color-brand-6);
                }
            }
        }

        // 网格实例的 sidecar 服务 集群
        .card-body {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            flex-wrap: wrap;
        }

        .card-value {
            height: 25px;
            line-height: 27px;

            .card-sidecar-value,
            .no-data,
            a {
                font-size: var(--font-size-extra-large);
            }

            a {
                color: var(--color-brand-6);
            }

            .unit {
                vertical-align: top;
                font-size: var(--font-size-medium);
            }
        }
    }
}
