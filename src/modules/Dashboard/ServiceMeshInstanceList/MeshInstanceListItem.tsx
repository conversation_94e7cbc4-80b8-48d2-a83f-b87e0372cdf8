import {useMemo} from 'react';
import {Link} from 'react-router-dom';
import {Card, Col, Row, List} from '@osui/ui';
import StateTag from '@baidu/icloud-ui-state-tag';
import Ellipsis from '@baidu/icloud-ui-ellipsis';
import {GetServiceMeshOverviewListResponse, ClustersOverview} from '@/api/overview';
import {SERVICE_MESH_INSTANCE_INFO_PATH, SERVICE_MESH_CLUSTER_LIST_PATH} from '@/links';
import {ServiceMeshStatusDict} from '@/dicts/serviceMesh';
import {RegionList, useFramework} from '@/hooks';
import {toThousands} from '@/utils/numberToThousandth';
import {REGION_GLOBAL} from '@/dicts';
import {NoData} from './constants';
import c from './index.less';

export default function MeshInstanceListItem({item}: {item: GetServiceMeshOverviewListResponse}) {
    const {region: {regionList}} = useFramework();
    const regionData = useMemo<RegionList>(
        () => ({...regionList, [REGION_GLOBAL]: '全局'}),
        [regionList]
    );
    const titleStatus = ServiceMeshStatusDict[item.status]; // 状态
    const instanceId = {serviceMeshInstanceId: item.instanceId}; // 跳转页面所需要的网格id
    /* const services = useMemo<ServicesOverview|null>( // 服务状态 只要对象中缺失一个字段，就直接返回 null
        () => {
            const val = item.servicesOverview || {};
            return Reflect.has(val, 'allOf') && Reflect.has(val, 'noneOf') && Reflect.has(val, 'anyOf')
                ? val : null;
        },
        [item.servicesOverview]
    ); */
    const clusters = useMemo<ClustersOverview|null>( // 集群纳管 只要对象中缺失一个字段，就直接返回 null
        () => {
            const val = item.clustersOverview || {};
            return Reflect.has(val, 'runningNum') && Reflect.has(val, 'total')
                ? val : null;
        },
        [item.clustersOverview]
    );
    return (
        <List.Item className={c['overview-mesh-instance-item']}>
            <List.Item.Meta
                title={
                    // 列表标题
                    <div className={`flex items-center ${c['instance-title']}`}>
                        <div className={`flex items-center ${c['instance-name']}`}>
                            <div className="margin-right-8">
                                <Link to={SERVICE_MESH_INSTANCE_INFO_PATH.fill(instanceId)}>
                                    {item.instanceName}
                                </Link> / {item.instanceId}
                            </div>
                            {titleStatus ? <StateTag type={titleStatus.value}>{titleStatus.text}</StateTag> : null}
                        </div>
                        <div>{regionData[item.region]}</div>
                    </div>
                }
            />
            <Row gutter={16}>
                <Col span={12}>
                    <Card title="Sidecar数量">
                        <div className={c['card-body']}>
                            <Ellipsis className={c['card-label-text']}>当前接入Sidecar数</Ellipsis>
                            <Ellipsis className={`${c['card-value']}`}>
                                {item.sidecarNum === undefined
                                    ? NoData : (
                                        <span className={c['card-sidecar-value']}>{toThousands(item.sidecarNum)}
                                        </span>
                                    )
                                }
                                <span className={c['unit']}>个</span>
                            </Ellipsis>
                        </div>
                    </Card>
                </Col>
                {/* <Col span={8}>
                    <Card title={
                        <>服务状态
                            <Popover className="service-tip" content={SERVICE_TIP}><QuestionCircleOutlined /></Popover>
                        </>
                    }
                    >
                        <div className={c['card-body']}>
                            <Ellipsis className={c['card-label-text']}>全量接入/接入中/未使用</Ellipsis>
                            <Ellipsis className={`${c['card-value']}`}>
                                {services
                                    ? (
                                        <>
                                            <Link to={SERVICE_MESH_SERVICE_LIST_PATH.fill(instanceId)}>
                                                {toThousands(services.allOf)}/
                                                {toThousands(services.anyOf)}/
                                                {toThousands(services.noneOf)}
                                            </Link>
                                            <span className={c['unit']}>个</span>
                                        </>
                                    ) : NoData}
                            </Ellipsis>
                        </div>
                    </Card>
                </Col> */}
                <Col span={12}>
                    <Card title="集群纳管">
                        <div className={c['card-body']}>
                            <Ellipsis className={c['card-label-text']}>运行中/总数</Ellipsis>
                            <Ellipsis className={`${c['card-value']}`}>
                                {clusters
                                    ? (
                                        <>
                                            <Link to={SERVICE_MESH_CLUSTER_LIST_PATH.fill(instanceId)}>
                                                {toThousands(clusters.runningNum)}/{toThousands(clusters.total)}
                                            </Link>
                                            <span className={c['unit']}>个</span>
                                        </>
                                    ) : NoData}
                            </Ellipsis>
                        </div>
                    </Card>
                </Col>
            </Row>
        </List.Item>
    );
}
