import {ReactNode, useCallback} from 'react';
import {List} from '@osui/ui';
import {GetServiceMeshOverviewListResponse} from '@/api/overview';
import MeshInstanceListItem from './MeshInstanceListItem';
import c from './index.less';

interface Props {
    instanceData: GetServiceMeshOverviewListResponse[];
    loading: boolean;
}
export default function ServiceMeshInstanceList({instanceData, loading}: Props) {
    const listRenderItem = useCallback(
        (item: GetServiceMeshOverviewListResponse): ReactNode => {
            return Object.keys(item || {}).length && <MeshInstanceListItem item={item} />;
        },
        []
    );

    return (
        <div className={c['overview-mesh-instance-list-wrapper']}>
            <List
                className={c['overview-mesh-instance-list']}
                itemLayout="vertical"
                dataSource={instanceData}
                // TODO：待OSUI组件修复了，loading传入布尔值（目前需传对象，效果才符合预期）。
                loading={{spinning: loading}}
                renderItem={listRenderItem}
            />
        </div>
    );
}
