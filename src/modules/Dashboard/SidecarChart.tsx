import React from 'react';
import ReactECharts from 'echarts-for-react';
import {GetSidecarsOverviewResponse} from '@/api/overview';

interface SidecarChartProps{
    data: GetSidecarsOverviewResponse;
}
export default function SidecarChart({data}: SidecarChartProps) {
    const option = {
        color: ['#4175FA'],
        grid: {
            top: 43,
            bottom: 0,
            left: 0,
            containLabel: true,
        },
        xAxis: {
            type: 'value',
            show: false,
        },
        yAxis: {
            type: 'category',
            data: data.map(v => v.instanceName),
        },
        legend: {
            data: ['当前接入的Sidecar规模'],
            right: 0,
            itemWidth: 6,
            itemHeight: 6,
        },
        series: [
            {
                name: '当前接入的Sidecar规模',
                data: data.map(v => v.num),
                type: 'bar',
                barWidth: 16,
                label: {
                    show: true,
                    position: 'right',
                },
                // barWidth: 16,
                barGap: 24,
                barCategoryGap: 24,
            },
        ],
    };
    return (
        <ReactECharts style={{height: '220px'}} option={option} />
    );
}
