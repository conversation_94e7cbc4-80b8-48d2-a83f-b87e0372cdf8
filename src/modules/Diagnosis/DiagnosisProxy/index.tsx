import {useCallback, useMemo, useState} from 'react';
import ProTable from '@baidu/icloud-ui-pro-table';
import {Tabs} from '@osui/ui';
import {
    getDiagnosisProxyConfigList,
} from '@/api/diagnosis';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import TableColumnTitleWithTooltip from '@/components/TableColumnTitleWithTooltip';
import {typeToColumnsObjMap} from './constant';

interface IProps {
    proxyName: string;
    clusterName: string;
}
export default function DiagnosisProxy({
    proxyName,
    clusterName,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const [type, setType] = useState('cluster');

    const getDiagnosisProxyConfigListCallback = useCallback(
        params => {
            if (!proxyName || !clusterName || !type) {
                return Promise.resolve({result: []});
            }
            delete params.namespace;
            return getDiagnosisProxyConfigList({
                ...params,
                serviceMeshInstanceId,
                proxyName,
                clusterName,
                type,
            });
        },
        [proxyName, clusterName, type, serviceMeshInstanceId]
    );

    const getRowKey = useCallback(
        record => {
            return record.dataIndex;
        },
        []
    );

    const items = useMemo(
        () => {
            const result = [];
            for (const [typeInMap, {columns, tooltip}] of typeToColumnsObjMap) {
                result.push({
                    key: typeInMap,
                    label: <TableColumnTitleWithTooltip title={`${typeInMap}配置`} tooltip={tooltip} />,
                    children: typeInMap === type
                        ? (
                            <ProTable
                                rowKey={getRowKey}
                                columns={columns}
                                request={getDiagnosisProxyConfigListCallback}
                            />
                        )
                        : null
                    ,
                });
            }
            return result;
        },
        [getDiagnosisProxyConfigListCallback, getRowKey, type]
    );

    return (
        <Tabs
            items={items}
            onChange={setType}
        />
    );
}
