import {IColumns} from '@baidu/icloud-ui-pro-table';
import StateTag from '@baidu/icloud-ui-state-tag';
import MonacoEditorComponent from '@/components/MonacoEditorComponent';
import {endpointStatusMap, secretIsValidMap, secretStatusMap, telemetryResultMap} from './constant';

export const clusterColumns: IColumns = [
    {
        title: '服务完全限定域名',
        dataIndex: 'domain',
        key: 'domain',
    },
    {
        title: '端口',
        dataIndex: 'port',
        key: 'port',
        width: 100,
    },
    {
        title: '子网',
        dataIndex: 'subnet',
        key: 'subnet',
        width: 100,
    },
    {
        title: '流量方向',
        dataIndex: 'trafficDirection',
        key: 'trafficDirection',
        width: 100,
    },
    {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        width: 100,
    },
    {
        title: '目的地规则',
        dataIndex: 'destinationRule',
        key: 'destinationRule',
        width: 100,
    },
];

export const bootstrapColumns: IColumns = [
    {
        title: 'json内容',
        dataIndex: 'bootstrap',
        key: 'bootstrap',
        width: 980,
        render: value => {
            return (
                <MonacoEditorComponent
                    language="json"
                    width="980px"
                    height="400px"
                    value={JSON.stringify(value, null, 4)}
                    options={{readOnly: true}}
                />
            );
        },
    },
];

export const listenerColumns: IColumns = [
    {
        title: '监听地址',
        dataIndex: 'address',
        key: 'address',
        width: 120,
    },
    {
        title: '端口',
        dataIndex: 'port',
        key: 'port',
        width: 60,
    },
    {
        title: '匹配规则',
        dataIndex: 'matchRule',
        key: 'matchRule',
        width: 130,
    },
    {
        title: '目的地',
        dataIndex: 'destination',
        key: 'destination',
    },
];

export const routeColumns: IColumns = [
    {
        title: '路由名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: 'vhost名称',
        dataIndex: 'vhostName',
        key: 'vhostName',
        width: 100,
    },
    {
        title: '路由域名',
        dataIndex: 'routeDomain',
        key: 'routeDomain',
    },
    {
        title: '匹配规则',
        dataIndex: 'matchRule',
        key: 'matchRule',
        width: 120,
    },
    {
        title: 'Virtual Service',
        dataIndex: 'virtualService',
        key: 'virtualService',
        width: 120,
    },
];

export const endpointColumns: IColumns = [
    {
        title: 'endpoint',
        dataIndex: 'endpoint',
        key: 'endpoint',
    },
    {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        render(value) {
            return (
                <StateTag type={endpointStatusMap.get(value) ?? 'info'}>
                    {value}
                </StateTag>
            );
        },
    },
    {
        title: '遥测检查结果',
        dataIndex: 'telemetryResult',
        key: 'telemetryResult',
        width: 100,
        render(value) {
            return (
                <StateTag type={telemetryResultMap.get(value) ?? 'info'}>
                    {value}
                </StateTag>
            );
        },
    },
    {
        title: '出入站配置',
        dataIndex: 'config',
        key: 'config',
    },
];

export const secretColumns: IColumns = [
    {
        title: '资源名称',
        dataIndex: 'resourceName',
        key: 'resourceName',
        width: 100,
    },
    {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        width: 100,
    },
    {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        render(value) {
            return (
                <StateTag type={secretStatusMap.get(value) ?? 'info'}>
                    {value}
                </StateTag>
            );
        },
    },
    {
        title: '是否有效认证',
        dataIndex: 'isValid',
        key: 'isValid',
        width: 100,
        render(value) {
            return (
                <StateTag type={secretIsValidMap.get(value) ?? 'info'}>
                    {value ? 'TRUE' : 'FALSE'}
                </StateTag>
            );
        },
    },
    {
        title: 'Serial Number',
        dataIndex: 'serialNumber',
        key: 'serialNumber',
    },
    {
        title: '失效日期',
        dataIndex: 'expiryDate',
        key: 'expiryDate',
        width: 94,
    },
    {
        title: '生效日期',
        dataIndex: 'effectiveDate',
        key: 'effectiveDate',
        width: 94,
    },
];

export const configDumpColumns: IColumns = [
    {
        title: 'json内容',
        dataIndex: 'configs',
        key: 'configs',
        width: 980,
        render: (value, record) => {
            return (
                <MonacoEditorComponent
                    language="json"
                    width="980px"
                    height="400px"
                    value={JSON.stringify(record, null, 4)}
                    options={{readOnly: true}}
                />
            );
        },
    },
];
