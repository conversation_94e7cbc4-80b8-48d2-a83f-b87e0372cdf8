import {IColumns} from '@baidu/icloud-ui-pro-table';
import {TStateTagType} from '@/types/component';
import {
    bootstrapColumns,
    clusterColumns,
    listenerColumns,
    routeColumns,
    endpointColumns,
    secretColumns,
    configDumpColumns,
} from './columns';

interface IColumnsObj {
    columns: IColumns;
    tooltip: string;
}
export const typeToColumnsObjMap = new Map<string, IColumnsObj>([
    [
        'cluster',
        {
            columns: clusterColumns,
            tooltip: 'Pod 中 Envoy 代理的集群配置，包括 Envoy 如何连接到其他集群中的服务等信息',
        },
    ],
    [
        'bootstrap',
        {
            columns: bootstrapColumns,
            tooltip: 'Pod 中 Envoy 代理的引导（bootstrap）配置，包含 Envoy 启动所需的基本配置信息',
        },
    ],
    [
        'listener',
        {
            columns: listenerColumns,
            tooltip: 'Pod 中 Envoy 代理的监听器（Listener）配置，包括 Envoy 如何监听传入连接的配置',
        },
    ],
    [
        'route',
        {
            columns: routeColumns,
            tooltip: 'Pod 中 Envoy 代理的路由配置，包括请求应该如何路由到不同服务的信息',
        },
    ],
    [
        'endpoint',
        {
            columns: endpointColumns,
            tooltip: 'Pod 中 Envoy 代理的端点（Endpoint）配置，包含可访问的后端服务的地址和端口',
        },
    ],
    [
        'secret',
        {
            columns: secretColumns,
            tooltip: 'Pod 中 Envoy 代理的密钥配置，包括 Envoy 使用的加密密钥等信息',
        },
    ],
    [
        'configDump',
        {
            columns: configDumpColumns,
            tooltip: '包括 Envoy 的运行时配置的详细信息，例如路由、监听器、集群、过滤器等',
        },
    ],
]);

export const endpointStatusMap = new Map<string, TStateTagType>([
    ['HEALTHY', 'success'],
    ['UNHEALTHY', 'error'],
    ['UNKNOWN', 'warning'],
]);

export const telemetryResultMap = new Map<string, TStateTagType>([
    ['OK', 'success'],
    ['FAILED', 'error'],
]);

export const secretStatusMap = new Map<string, TStateTagType>([
    ['ACTIVE', 'success'],
    ['ERROR', 'error'],
    ['NOT FOUND', 'error'],
    ['STALE', 'warning'],
]);

export const secretIsValidMap = new Map<boolean, TStateTagType>([
    [true, 'success'],
    [false, 'error'],
]);
