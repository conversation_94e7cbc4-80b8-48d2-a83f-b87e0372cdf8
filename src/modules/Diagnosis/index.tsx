import Card from '@baidu/icloud-ui-card';
import {Select} from '@baidu/icloud-ui-form';
import ProTable from '@baidu/icloud-ui-pro-table';
import {Tabs} from '@osui/ui';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {DefaultOptionType} from 'antd/lib/select';
import {useBoolean} from 'huse';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getDiagnosisExceptionList, getDiagnosisNamespaceList, getDiagnosisProxyStatusList} from '@/api/diagnosis';
import {getUrlSearchByUpdateQuery, useQuery} from '@/hooks/useUrl';
import {exceptionColumns, proxyColumns} from './columns';
import {tools} from './tools';
import {filters} from './filters';
import HelpWrapper from './HelpWrapper';
import c from './index.less';
import ProxyConfigDrawer from './ProxyConfigDrawer';

enum TabType {
    Exception = 'exception',
    Proxy = 'proxy',
}
export default function Diagnosis() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const {namespace: namespaceFromQuery} = useQuery<{ namespace: string }>();
    const [namespace, setNamespace] = useState<any>(namespaceFromQuery);
    const [namespaceOptions, setNamespaceOptions] = useState<DefaultOptionType[]>([]);
    const [isNamespaceLoading, {on: onNamespaceLoading, off: offNamespaceLoading}] = useBoolean(false);

    const {type: typeFromQuery} = useQuery<{ type: TabType }>();
    const [type, setType] = useState(typeFromQuery);

    const [proxyName, setProxyName] = useState<string>('');
    const [clusterName, setClusterName] = useState<string>('');

    const changeNamespace = useCallback(
        v => {
            setNamespace(v);
            const urlSearch = getUrlSearchByUpdateQuery('namespace', v);
            window.history.replaceState(null, '', urlSearch);
        },
        []
    );

    const changeType = useCallback(
        v => {
            setType(v);
            const urlSearch = getUrlSearchByUpdateQuery('type', v);
            window.history.replaceState(null, '', urlSearch);
        },
        []
    );

    useEffect(
        () => {
            if (![TabType.Exception, TabType.Proxy].includes(type)) {
                changeType(TabType.Exception);
            }
        },
        [changeType, type]
    );

    useEffect(
        () => {
            (async () => {
                onNamespaceLoading();
                const rsp = await getDiagnosisNamespaceList({serviceMeshInstanceId});
                offNamespaceLoading();
                const namespaceList = rsp?.result ?? [];
                setNamespaceOptions(namespaceList.map(({name}) => ({label: name, value: name})));
            })();
        },
        [serviceMeshInstanceId, namespace, changeNamespace, offNamespaceLoading, onNamespaceLoading]
    );

    const getRowKey = useCallback(
        record => {
            return record.dataIndex;
        },
        []
    );

    const getDiagnosisExceptionListCallback = useCallback(
        params => {
            if (!namespace) {
                return Promise.resolve({result: []});
            }
            return getDiagnosisExceptionList({
                ...params,
                serviceMeshInstanceId,
                namespace,
            });
        },
        [serviceMeshInstanceId, namespace]
    );

    const getDiagnosisProxyStatusListCallback = useCallback(
        params => {
            if (!namespace) {
                return Promise.resolve({result: []});
            }
            return getDiagnosisProxyStatusList({
                ...params,
                serviceMeshInstanceId,
                namespace,
            });
        },
        [serviceMeshInstanceId, namespace]
    );

    const onRow = useCallback(
        record => {
            return {
                onClick: (e: any) => {
                    if (e?.target?.id === 'proxyName') {
                        setProxyName(record?.proxyName ?? '');
                        setClusterName(record?.clusterName ?? '');
                    }
                },
            };
        },
        []
    );

    const offVisible = useCallback(
        () => {
            setProxyName('');
            setClusterName('');
        },
        []
    );

    const items = useMemo(
        () => {
            return [
                {
                    key: TabType.Exception,
                    label: '异常分析',
                    children: type === TabType.Exception
                        ? (
                            <ProTable
                                rowKey={getRowKey}
                                columns={exceptionColumns}
                                tools={tools}
                                request={getDiagnosisExceptionListCallback}
                            />
                        )
                        : null
                    ,
                },
                {
                    key: TabType.Proxy,
                    label: '代理状态',
                    children: type === TabType.Proxy
                        ? (
                            <div className={c['proxyTableWrapper']}>
                                <ProTable
                                    onRow={onRow}
                                    rowKey={getRowKey}
                                    columns={proxyColumns}
                                    tools={tools}
                                    filters={filters}
                                    request={getDiagnosisProxyStatusListCallback}
                                />
                            </div>
                        )
                        : null
                    ,
                },
            ];
        },
        [type, onRow, getRowKey, getDiagnosisExceptionListCallback, getDiagnosisProxyStatusListCallback]
    );

    return (
        <Card title="诊断工具">
            <div className={c['namespaceWrapper']}>
                命名空间：
                <Select
                    style={{width: 360}}
                    showSearch
                    placeholder="请选择命名空间"
                    loading={isNamespaceLoading}
                    options={namespaceOptions}
                    value={namespace}
                    onChange={changeNamespace}
                />
            </div>

            {
                namespace
                    ? (
                        <Tabs
                            defaultActiveKey={typeFromQuery}
                            items={items}
                            onChange={changeType}
                        />
                    )
                    : (
                        <HelpWrapper />
                    )
            }

            <ProxyConfigDrawer
                proxyName={proxyName}
                clusterName={clusterName}
                offVisible={offVisible}
            />
        </Card>
    );
}
