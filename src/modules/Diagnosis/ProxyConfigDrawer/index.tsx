import {<PERSON><PERSON>, Drawer} from '@osui/ui';
import {useBoolean} from 'huse';
import {useCallback, useEffect} from 'react';
import {OutlinedDownload} from '@baidu/acud-icon';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getDiagnosisProxyConfigList} from '@/api/diagnosis';
import DiagnosisProxy from '../DiagnosisProxy';
import c from './index.less';

interface IProps {
    proxyName: string;
    clusterName: string;
    offVisible: () => void;
}
export default function ProxyConfigDrawer({
    proxyName,
    clusterName,
    offVisible: offVisibleFromProps,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const [isVisible, {on: onVisible, off: offVisible}] = useBoolean(false);

    useEffect(
        () => {
            if (proxyName && clusterName) {
                onVisible();
            }

            return () => {
                offVisible();
            };
        },
        [clusterName, proxyName, offVisible, onVisible]
    );

    const downloadConfigDump = useCallback(
        async () => {
            try {
                const rsp = await getDiagnosisProxyConfigList({
                    serviceMeshInstanceId,
                    proxyName,
                    clusterName,
                    type: 'configDump',
                });
                const textFileAsBlob = new Blob([JSON.stringify(rsp?.result[0], null, 4) ?? ''], {type: 'text/plain'});
                const downloadLink = document.createElement('a');
                downloadLink.download = 'config_dump.json';
                downloadLink.href = window.URL.createObjectURL(textFileAsBlob);
                downloadLink.click();
            } catch (error) {
                console.error(error);
            }
        },
        [serviceMeshInstanceId, proxyName, clusterName]
    );

    const onClose = useCallback(
        () => {
            offVisible();
            offVisibleFromProps();
        },
        [offVisible, offVisibleFromProps]
    );


    if (!proxyName || !clusterName || !isVisible) {
        return null;
    }

    return (
        <Drawer
            className={c['proxyConfigDrawer']}
            title={proxyName}
            extra={
                <Button
                    icon={<OutlinedDownload />}
                    disabledReason="请选择命名空间和pod名称"
                    type="text"
                    disabled={!proxyName || !clusterName}
                    onClick={downloadConfigDump}
                >
                    下载配置
                </Button>
            }
            placement="right"
            width={1030}
            onClose={onClose}
            visible={isVisible}
        >
            <DiagnosisProxy proxyName={proxyName} clusterName={clusterName} />
        </Drawer>
    );
}
