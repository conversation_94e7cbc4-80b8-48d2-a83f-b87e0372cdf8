import {Image} from '@osui/ui';
import selectNamespacePng from '@/assets/diagnosis/selectNamespace.png';
import c from './index.less';

interface IHelpCardProps {
    title: string;
    content: string;
}
function HelpCard({
    title,
    content,
}: IHelpCardProps) {
    return (
        <div className={c['helpCard']}>
            <div className={c['title']}>{title}</div>
            <div className={c['helpText']}>{content}</div>
        </div>
    );
}

export default function HelpWrapper() {
    return (
        <div className={c['helpWrapper']}>
            <Image src={selectNamespacePng} width={140} height={72} preview={false} />
            <div className={c['firstStep']}>第一步：选择一个命名空间</div>
            <div className={c['helpText']}>让诊断工具更快更准地帮你定位 Istio 资源问题，它可以：</div>
            <div className={c['cardList']}>
                <HelpCard title="分析 Istio 异常" content="自动检索命名空间的异常状态" />
                <HelpCard title="获取代理配置" content="快速帮助检索空间里pod中的配置信息" />
            </div>
        </div>
    );
}
