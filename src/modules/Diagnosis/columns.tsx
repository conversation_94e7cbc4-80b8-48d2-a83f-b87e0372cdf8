import {IColumns} from '@baidu/icloud-ui-pro-table';
import StateTag from '@baidu/icloud-ui-state-tag';
import {Link} from 'react-router-dom';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import TableColumnTitleWithTooltip from '@/components/TableColumnTitleWithTooltip';

export const exceptionColumns: IColumns = [
    {
        title: '异常名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '异常等级',
        dataIndex: 'level',
        key: 'level',
        width: 100,
        render(value) {
            const valueLowerCase = value.toLowerCase();
            return (
                valueLowerCase === 'error'
                    ? <StateTag type="error">Error</StateTag>
                    : valueLowerCase === 'warning'
                        ? <StateTag type="warning">Warning</StateTag>
                        : <StateTag type="pending">INFO</StateTag>
            );
        },
    },
    {
        title: '异常编码',
        dataIndex: 'code',
        key: 'code',
        width: 100,
        render(value, {version, code}) {
            const versionMatch = version.match(/\d+\.\d+/);
            let versionValue = 'latest';
            if (Array.isArray(versionMatch) && versionMatch.length === 1) {
                versionValue = `v${versionMatch[0]}`;
            }

            // 将 '[IST0101]' 转换为 'ist0101'
            const codeValue = code.slice(1, code.length - 1).toLowerCase();

            const blackList = [
                'ist0126', 'ist0138', 'ist0139', 'ist0140', 'ist0141', 'ist0142', 'ist0145',
                'ist0146', 'ist0147', 'ist0148', 'ist0149', 'ist0151',
            ];

            return (
                <ExternalLink
                    href={
                        blackList.includes(codeValue)
                            ? urls.external.istio.extraordinary.fill()
                            : urls.external.istio.extraordinaryCode.fill({version: versionValue, code: codeValue})
                    }
                    value={value}
                />
            );
        },
    },
    {
        title: '异常描述',
        dataIndex: 'description',
        key: 'description',
    },
];

export const proxyColumns: IColumns = [
    {
        title: '代理名称',
        dataIndex: 'proxyName',
        key: 'proxyName',
        render(proxyName) {
            return (
                <Link id="proxyName" to={location.search}>
                    {proxyName}
                </Link>
            );
        },
    },
    {
        title: '集群名称',
        dataIndex: 'clusterName',
        key: 'clusterName',
        width: 140,
    },
    {
        title: (
            <TableColumnTitleWithTooltip
                title="CDS"
                tooltip="Cluster Discovery Service（CDS）状态，表明集群发现服务是否与所有配置同步"
            />
        ),
        dataIndex: 'CDS',
        key: 'CDS',
        width: 100,
    },
    {
        title: (
            <TableColumnTitleWithTooltip
                title="LDS"
                tooltip="Listener Discovery Service（LDS）状态，表示监听器发现服务是否与所有配置同步"
            />
        ),
        dataIndex: 'LDS',
        key: 'LDS',
        width: 100,
    },
    {
        title: (
            <TableColumnTitleWithTooltip
                title="EDS"
                tooltip="Endpoint Discovery Service（EDS）状态，表示端点发现服务是否与所有配置同步"
            />
        ),
        dataIndex: 'EDS',
        key: 'EDS',
        width: 100,
    },
    {
        title: (
            <TableColumnTitleWithTooltip
                title="RDS"
                tooltip="Route Discovery Service（RDS）状态，表示路由发现服务是否与所有配置同步"
            />
        ),
        dataIndex: 'RDS',
        key: 'RDS',
        width: 100,
    },
    {
        title: (
            <TableColumnTitleWithTooltip
                title="ECDS"
                tooltip="Endpoint Configuration Discovery Service（ECDS）状态，表示端点配置发现服务是否与所有配置同步"
            />
        ),
        dataIndex: 'ECDS',
        key: 'ECDS',
        width: 100,
    },
    {
        title: 'ISTIOD名称',
        dataIndex: 'pilotName',
        key: 'pilotName',
        width: 180,
    },
    {
        title: '版本',
        dataIndex: 'version',
        key: 'version',
        width: 80,
    },
];
