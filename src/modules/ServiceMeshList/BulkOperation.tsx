import React, {useCallback} from 'react';
import {Button, message, Modal} from '@osui/ui';
import {deleteServiceMeshInstanceApi} from '@/api/serviceMesh';
import {ServiceMeshInstanceId} from '@/interface/serviceMesh';
interface BulkOperationProps {
    selectedRowKeys: ServiceMeshInstanceId[];
    refresh: () => void;
    setSelectedRowKeys: (selectedRowKeys: ServiceMeshInstanceId[]) => void;
}

export function BulkOperation({selectedRowKeys, refresh, setSelectedRowKeys}: BulkOperationProps) {
    const handleClick = useCallback(
        () => {
            Modal.confirm({
                title: '确定删除服务网格?',
                content: (
                    <div>
                        <div>
                            选中的网格和CCE集群关联，删除后相关负载和Sidecar注入规则将同时从CCE集群中移除；
                            此操作在工作负载重新启动后生效，生效前将继续使用当前配置劫持、转发流量。
                        </div>
                        <div>
                            {`确定要删除${selectedRowKeys.length}个服务网格吗？`}
                        </div>
                    </div>
                ),
                async onOk() {
                    try {
                        await Promise.all(
                            selectedRowKeys.map(id => deleteServiceMeshInstanceApi({serviceMeshInstanceId: id}))
                        );
                        message.success(`${selectedRowKeys.length}个网格删除成功。`);
                    } catch (error) {
                        message.error(`${selectedRowKeys.length}个网格删除失败，请重新操作。`);
                    } finally {
                        setSelectedRowKeys([]);
                        refresh();
                    }
                },
                closable: true,
            });
        },
        [refresh, selectedRowKeys, setSelectedRowKeys]
    );

    return (
        <Button
            disabled={selectedRowKeys.length === 0}
            onClick={handleClick}
        >
            删除
        </Button>
    );
}
