import React, {useCallback} from 'react';
import styled from 'styled-components';
import ProTable, {IFilters, IOperations, ITools} from '@baidu/icloud-ui-pro-table';
import {Link} from 'react-router-dom';
import ProductDocLink from '@/components/ProductDocLink';
import {getServiceMeshListApi} from '@/api/serviceMesh';
import * as Layout from '@/components/Layout';
import {useListTotal} from '@/hooks/useList';
import urls from '@/urls';
import AddButton from '@/components/AddButton';
import {LayoutHeaderWrap} from '@/components/HeaderWrap';
import {formatMultiValueQuery} from '@/utils';
import {useApiWithRegion} from '../../hooks/useApiWithRegion';
import baseColumns from './baseColumns';

const ListHeaderWrap = styled(LayoutHeaderWrap)`
    .ant-page-header.osui-page-header {
        padding: 12px 16px;
    }
`;

const tools: ITools = [
    {
        renderType: 'refresh',
    },
];
// 筛选区
const filters: IFilters = [
    {
        name: ['keywordType', 'keyword'],
        renderType: 'groupSearch',
        onFilter: (value, record) => {
            const [field, inputValue] = value || [];
            return !inputValue || (field && record[field].includes(inputValue));
        },
        defaultValue: ['instanceName', ''],
        props: {
            options: [
                {
                    label: '网格名称',
                    value: 'instanceName',
                },
                {
                    label: '网格ID',
                    value: 'instanceId',
                },
            ],
        },
    },
];
export default function ServiceMeshList() {
    // const {selectedRowKeys, rowSelection, setSelectedRowKeys} = useListSelectedRow();
    // const {request, total} = useListTotal(useApiWithRegion(getServiceMeshListApi));
    const {request} = useListTotal(useApiWithRegion(getServiceMeshListApi));
    const getList = useCallback(
        params => {
            const newParams = formatMultiValueQuery(params, ['instanceStatus']);
            return request(newParams);
        },
        [request]
    );
    // 操作区
    const operations: IOperations = [
        {
            render() {
                return (
                    <Link to={urls.createServiceMesh.fill()}>
                        <AddButton>创建网格</AddButton>
                    </Link>
                );
            },
        },
        // {
        //     render(props, {refresh}) {
        //         return (
        //             <BulkOperation
        //                 selectedRowKeys={selectedRowKeys}
        //                 setSelectedRowKeys={setSelectedRowKeys}
        //                 refresh={refresh}
        //             />
        //         );
        //     },
        // },
        // {
        //     render() {
        //         return `已选中${selectedRowKeys.length}条，共${total}条`;
        //     },
        // },
    ];
    return (
        <>
            <ListHeaderWrap>
                <Layout.Header
                    title="网格列表"
                    extra={
                        <ProductDocLink />
                    }
                />
            </ListHeaderWrap>
            <Layout.MainContent>
                <ProTable
                    rowKey="instanceId"
                    tools={tools}
                    columns={baseColumns}
                    operations={operations}
                    filters={filters}
                    request={getList}
                    // rowSelection={rowSelection}
                />
            </Layout.MainContent>
        </>
    );
}
