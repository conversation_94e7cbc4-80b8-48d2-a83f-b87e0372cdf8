import {OutlinedQuestionCircle} from '@baidu/acud-icon';
import {IColumns} from '@baidu/icloud-ui-pro-table';
import StateTag from '@baidu/icloud-ui-state-tag';
import {But<PERSON>, Tooltip} from '@osui/ui';
import {Link} from 'react-router-dom';
import {
    BillingMode,
    ServiceMeshBillingModeDict,
    ServiceMeshStatus,
    ServiceMeshStatusDict,
    SERVICE_MESH_TYPE_DICT,
} from '@/dicts/serviceMesh';
import urls from '@/urls';
import DeleteSingleServiceMesh from '@/components/DeleteSingleServiceMesh';
import CopyDataWhenHover from '@/components/CopyDataWhenHover';
import UpgradeIstioButton from '@/components/UpgradeIstioButton';
import {ServiceMeshListItem} from '@/api/serviceMesh';
import c from './index.less';

const StatusFilterOptions = Object.entries(ServiceMeshStatusDict)
    .map(([key, value]) => ({value: key, text: value.text}));

const TypeFilterOptions = Object.entries(SERVICE_MESH_TYPE_DICT)
    .map(([key, value]) => ({value: key, text: value}));
const baseColumns: IColumns = [{
    title: '网格名称/ID',
    dataIndex: 'instanceName',
    width: 200,
    render(_, record) {
        const {instanceId: serviceMeshInstanceId, instanceName, instanceStatus: status, instanceType: type} = record;
        return (
            <>
                <div>
                    <Link to={urls.serviceMeshBaseInfo.fill({serviceMeshInstanceId},
                        {status, serviceMeshInstanceId, instanceName, type}
                    )}
                    >
                        {instanceName}
                    </Link>
                </div>
                <CopyDataWhenHover copyValue={record.instanceId} />
            </>
        );
    },
}, {
    title: '状态',
    dataIndex: 'instanceStatus',
    filters: StatusFilterOptions,
    filterType: 'single',
    render(value: ServiceMeshStatus) {
        const data = ServiceMeshStatusDict[value];
        return data ? <StateTag type={data.value}>{data.text}</StateTag> : null;
    },
}, {
    title: 'Istio版本',
    dataIndex: 'istioVersion',
}, {
    title: '网格类型',
    dataIndex: 'instanceType',
    renderType: 'enum',
    filters: TypeFilterOptions,
}, {
    title: '支付方式',
    dataIndex: 'billingModel',
    renderType: 'enum',
    render(value: BillingMode) {
        return ServiceMeshBillingModeDict[value];
    },
}, {
    title: 'Sidecar数量',
    dataIndex: 'sidecarCount',
}, {
    title: (
        <div className="flex">
            <span className="margin-right-8">集群状态</span>
            <Tooltip placement="top" title="集群状态指纳管的CCE集群状态，正常运行中/纳管的CCE集群总数。">
                <OutlinedQuestionCircle />
            </Tooltip>
        </div>
    ),
    render(_, record) {
        const {runningClusterCount, clusterCount, instanceId} = record;
        const bool = runningClusterCount < clusterCount;
        return (
            <>
                <span className={bool ? c.tagRedFontColor : undefined}>{runningClusterCount}</span>
                <span>/</span>
                <Link to={urls.cluster.list.fill({serviceMeshInstanceId: instanceId})}>{clusterCount}</Link>
            </>
        );
    },
}, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true,
    renderType: 'dateTime',
}, {
    title: '操作',
    render(_, record, index, {refresh}) {
        const {publicEnabled, blbInfo: {blbId}, eipInfo: {eipId}} = record;
        const disabled = publicEnabled && !(blbId && eipId);

        return (
            <div className="flex">
                <UpgradeIstioButton />
                {/* <Gap factor={5} orientation="horizontal" /> */}
                <DeleteSingleServiceMesh record={record as ServiceMeshListItem} callback={refresh}>
                    <Button
                        type="link"
                        disabled={disabled}
                        disabledReason="当前因资源未ready问题无法删除，请稍后再试"
                    >
                        删除
                    </Button>
                </DeleteSingleServiceMesh>
            </div>
        );
    },
}];
export default baseColumns;
