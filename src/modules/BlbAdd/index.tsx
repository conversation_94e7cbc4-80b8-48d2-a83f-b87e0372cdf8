import {Form, message, Spin} from '@osui/ui';
import {useHistory, useParams} from 'react-router-dom';
import {useCallback} from 'react';
import {useBoolean, useRequest} from 'huse';
import IForm from '@baidu/icloud-ui-form';
import * as Layout from '@/components/Layout';
import urls from '@/urls';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getServiceMeshInstanceApi} from '@/api/serviceMesh';
import {getNetworkConfigForRequest} from '@/utils/gateway';
import {addGatewayBlbApi} from '@/api/gateway';
import NetworkConfig from '../GatewayCreate/NetworkConfig';
import c from './index.less';

export default function BlbAdd() {
    const history = useHistory();
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{gatewayId: string}>();
    const {
        pending: serviceMeshInstanceLoading,
        data: serviceMeshInstance,
    } = useRequest(getServiceMeshInstanceApi, {serviceMeshInstanceId});
    const [networkConfigFormInstante] = Form.useForm();

    const onBack = useCallback(
        () => history.push(urls.gateway.detail.fill(
            {serviceMeshInstanceId, gatewayId, tabId: 'basicConfig'}
        )),
        [gatewayId, history, serviceMeshInstanceId]
    );

    // 提交 相关
    const [isSubmit, {on: onIsSubmit, off: offIsSubmit}] = useBoolean(false);
    const onSubmit = useCallback(
        async () => {
            try {
                const networkConfig = await networkConfigFormInstante.validateFields();
                const networkConfigForRequest = getNetworkConfigForRequest(networkConfig, serviceMeshInstance);

                onIsSubmit();
                await addGatewayBlbApi({
                    serviceMeshInstanceId,
                    gatewayId,
                    networkConfig: networkConfigForRequest,
                });
                message.success('绑定BLB成功');
                onBack();
            } catch (error) {
                // console.log(error);
            } finally {
                offIsSubmit();
            }
        },
        [
            serviceMeshInstanceId, gatewayId,
            networkConfigFormInstante, serviceMeshInstance,
            offIsSubmit, onBack, onIsSubmit,
        ]
    );

    return (
        <>
            <Layout.Header
                backIcon
                onBack={onBack}
                title="绑定BLB"
            />

            <div className={c['network-config-wrapper']}>
                {
                    serviceMeshInstanceLoading || !serviceMeshInstance
                        ? <Spin />
                        : (
                            <NetworkConfig
                                form={networkConfigFormInstante}
                                serviceMeshInstanceLoading={serviceMeshInstanceLoading}
                                serviceMeshInstance={serviceMeshInstance}
                            />
                        )
                }
            </div>

            <IForm onSubmit={onSubmit}>
                <IForm.Footer
                    okText="确定"
                    submitting={isSubmit}
                    onCancel={onBack}
                />
            </IForm>
        </>
    );
}
