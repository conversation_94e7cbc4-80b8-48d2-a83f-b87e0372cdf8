import Card from '@baidu/icloud-ui-card';
import ProTable from '@baidu/icloud-ui-pro-table';
import {useCallback, useEffect, useMemo} from 'react';
import {useRequestCallback} from 'huse';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getGatewayListApi} from '@/api/gateway';
import {formatMultiValueQuery} from '@/utils';
import {genOperations} from './operations';
import {genColumns} from './columns';
import {filters} from './filters';
import {tools} from './tools';


export default function GatewayList() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [getGatewayList, {data: gatewayList}] = useRequestCallback(getGatewayListApi, {serviceMeshInstanceId});

    useEffect(
        () => {
            getGatewayList();
        },
        [getGatewayList]
    );

    const columns = genColumns({serviceMeshInstanceId, getGatewayList});
    const operations = useMemo(
        () => {
            return genOperations(serviceMeshInstanceId, gatewayList?.totalCount);
        },
        [serviceMeshInstanceId, gatewayList]
    );

    const getList = useCallback(
        params => {
            const newParams = formatMultiValueQuery(params, ['status']);
            return getGatewayListApi({...newParams, serviceMeshInstanceId});
        },
        [serviceMeshInstanceId]
    );

    return (
        <Card title="网关管理">
            <ProTable
                rowKey="gatewayId"
                columns={columns}
                request={getList}
                tools={tools}
                filters={filters}
                operations={operations}
            />
        </Card>
    );
}
