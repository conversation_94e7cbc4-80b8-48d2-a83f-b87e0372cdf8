import {IOperations} from '@baidu/icloud-ui-pro-table';
import {Link} from 'react-router-dom';
import AddButton from '@/components/AddButton';
import urls from '@/urls';

export const genOperations = (serviceMeshInstanceId: string, gatewayListLength: number | undefined): IOperations => {
    return [
        {
            render: () => {
                const disabled = gatewayListLength === undefined || gatewayListLength > 0;

                return (
                    <Link to={disabled ? '#' : urls.gateway.create.fill({serviceMeshInstanceId})}>
                        <AddButton
                            action="create"
                            disabled={disabled}
                            title={
                                disabled
                                    ? '网格实例最多存在1个网关实例'
                                    : ''
                            }
                        >
                            创建网关
                        </AddButton>
                    </Link>
                );
            },
        },
    ];
};
