import ProTable from '@baidu/icloud-ui-pro-table';
import {useCallback, useMemo} from 'react';
import {Checkbox, message, Modal} from '@osui/ui';
import {deleteGatewayApi, IGatewayItem} from '@/api/gateway';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import c from './index.less';

interface IProps {
    gatewayItem: IGatewayItem;
    onSuccess: () => void;
}
export default function GatewayDeleteBtn({gatewayItem, onSuccess}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId, gatewayName} = gatewayItem;

    const [modal, contextHolder] = Modal.useModal();

    const clickModalOk = useCallback(
        async () => {
            try {
                const res = await deleteGatewayApi({
                    serviceMeshInstanceId,
                    gatewayId,
                });
                if (res) {
                    message.success(`${gatewayName} 删除成功。`);
                    onSuccess();
                }
                else {
                    message.error(`${gatewayName} 删除失败，请重试。`);
                }
            } catch (error) {
                throw error;
            }
        },
        [gatewayId, gatewayName, serviceMeshInstanceId, onSuccess]
    );

    const config = useMemo(
        () => {
            return {
                title: '删除网关',
                content: (
                    <>
                        <p>⽹关 {gatewayName} 已关联以下信息，你确定要删除吗？</p>
                        <div>
                            <Checkbox defaultChecked disabled />
                            <span className={c['eni-release']}>释放⽹关实例创建的弹性⽹卡</span>
                            <ExternalLink
                                href={
                                    urls.external.eniList.fill()
                                }
                                value="弹性网卡列表"
                            />
                        </div>
                    </>
                ),
                onOk: () => {
                    return new Promise(async (resolve, reject) => {
                        try {
                            await clickModalOk();
                            resolve(true);
                        } catch (error) {
                            reject(error);
                        }
                    });
                },
                closable: true,
            };
        },
        [clickModalOk, gatewayName]
    );

    const handleButtonClick = useCallback(
        () => {
            modal.confirm(config);
        },
        [config, modal]
    );

    return (
        <>
            <ProTable.Operation
                onClick={handleButtonClick}
            >
                删除
            </ProTable.Operation>
            {contextHolder}
        </>
    );
}
