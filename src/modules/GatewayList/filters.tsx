import {IFilters} from '@baidu/icloud-ui-pro-table';

// 筛选区
export const filters: IFilters = [
    {
        renderType: 'groupSearch',
        props: {
            options: [
                {
                    label: '网关名称',
                    value: 'gatewayName',
                },
                {
                    label: '网关ID',
                    value: 'gatewayId',
                },
            ],
        },
        name: ['keywordType', 'keyword'],
        defaultValue: ['gatewayName', ''],
    },
];
