import ProTable, {IColumns} from '@baidu/icloud-ui-pro-table';
import StateTag from '@baidu/icloud-ui-state-tag';
import {Link} from 'react-router-dom';
import {
    gatewayBillingModelDict,
    gatewayDeployModeDict,
    gatewayResourceQuotaDict,
    gatewayStatusDict,
} from '@/dicts/gateway';
import {
    IGatewayItem,
    TGatewayBillingModel,
    TGatewayDeployMode,
    TGatewayResourceQuota,
    TGatewayStatus,
} from '@/api/gateway';
import urls from '@/urls';
import CopyDataWhenHover from '@/components/CopyDataWhenHover';
import GatewayDeleteBtn from './GatewayDeleteBtn';

interface IProps {
    serviceMeshInstanceId: string;
    getGatewayList: () => void;
}
export const genColumns = ({serviceMeshInstanceId, getGatewayList}: IProps): IColumns => {
    return [
        {
            title: '网关名称/ID',
            dataIndex: 'gatewayName',
            width: 200,
            render(value, record) {
                const {gatewayName, gatewayId} = record;
                return (
                    <>
                        <Link to={urls.gateway.detail.fill({serviceMeshInstanceId, gatewayId, tabId: 'basicConfig'})}>
                            {gatewayName}
                        </Link>
                        <CopyDataWhenHover copyValue={gatewayId} />
                    </>
                );
            },
        },
        {
            title: '状态',
            dataIndex: 'status',
            renderType: 'stateTag',
            render(value: TGatewayStatus) {
                const {text, type} = gatewayStatusDict[value];
                return (
                    <>
                        <StateTag type={type}>{text}</StateTag>
                    </>
                );
            },
        },
        {
            title: '支付方式',
            dataIndex: 'billingModel',
            render(value: TGatewayBillingModel) {
                const text = gatewayBillingModelDict[value];
                return (
                    <>
                        {text}
                    </>
                );
            },
        },
        {
            title: '部署方式',
            dataIndex: 'deployMode',
            render(value: TGatewayDeployMode) {
                const text = gatewayDeployModeDict[value];
                return (
                    <>
                        {text}
                    </>
                );
            },
        },
        {
            title: '实例规格',
            dataIndex: 'resourceQuota',
            render(value: TGatewayResourceQuota) {
                const text = gatewayResourceQuotaDict[value];
                return (
                    <>
                        {text}
                    </>
                );
            },
        },
        {
            title: '副本数',
            dataIndex: 'replicas',
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            renderType: 'dateTime',
            sorter: true,
        },
        {
            title: '操作',
            dataIndex: 'operation',
            render(value, record, index, {refresh}) {
                return (
                    <ProTable.OperationsWrapper>
                        <GatewayDeleteBtn
                            gatewayItem={record as IGatewayItem}
                            onSuccess={
                                () => {
                                    refresh();
                                    getGatewayList();
                                }
                            }
                        />
                    </ProTable.OperationsWrapper>
                );
            },
        },
    ];
};
