import {useCallback, useState} from 'react';
import {useHistory} from 'react-router-dom';
import {Form, message, Steps} from '@osui/ui';
import {useBoolean, useRequest} from 'huse';
import {Header} from '@/components/Layout';
import urls from '@/urls';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getBasicConfigForRequest, getNetworkConfigForRequest} from '@/utils/gateway';
import {createGatewayApi} from '@/api/gateway';
import {getServiceMeshInstanceApi} from '@/api/serviceMesh';
import c from './index.less';
import BasicConfigForm from './BasicConfigForm';
import FooterBtn from './FooterBtn';
import NetworkConfig from './NetworkConfig';
import OrderConfirm from './OrderConfirm';

const {Step} = Steps;

export default function GatewayCreate() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {
        pending: serviceMeshInstanceLoading,
        data: serviceMeshInstance,
    } = useRequest(getServiceMeshInstanceApi, {serviceMeshInstanceId});
    const history = useHistory();
    const [basicConfigFormInstante] = Form.useForm();
    const [networkConfigFormInstante] = Form.useForm();

    const onBack = useCallback(
        () => history.push(urls.gateway.list.fill({serviceMeshInstanceId})),
        [history, serviceMeshInstanceId]
    );

    // 进度相关
    const [stepIndex, setStepIndex] = useState(0);
    const clickPreStep = useCallback(
        () => {
            setStepIndex(stepIndex => stepIndex - 1);
        },
        []
    );
    const clickNextStep = useCallback(
        async () => {
            if (stepIndex === 0) {
                await basicConfigFormInstante.validateFields();
            }
            else if (stepIndex === 1) {
                await networkConfigFormInstante.validateFields();
            }
            setStepIndex(stepIndex => stepIndex + 1);
        },
        [basicConfigFormInstante, networkConfigFormInstante, stepIndex]
    );
    const [isSubmit, {on: isSubmitOn, off: isSubmitOff}] = useBoolean(false);
    const clickSubmit = useCallback(
        async () => {
            try {
                const [basicConfig, networkConfig] = await Promise.all([
                    basicConfigFormInstante.validateFields(),
                    networkConfigFormInstante.validateFields(),
                ]);
                // 请求前，进行预处理
                const basicConfigForRequest = getBasicConfigForRequest(basicConfig);
                const networkConfigForRequest = getNetworkConfigForRequest(networkConfig, serviceMeshInstance);

                isSubmitOn();
                await createGatewayApi({
                    serviceMeshInstanceId,
                    basicConfig: basicConfigForRequest,
                    networkConfig: networkConfigForRequest,
                });
                message.success('创建网关成功');
                onBack();
            } catch (error) {
                // console.log(error);
            } finally {
                isSubmitOff();
            }
        },
        [
            isSubmitOff, isSubmitOn, onBack,
            serviceMeshInstance, serviceMeshInstanceId,
            basicConfigFormInstante, networkConfigFormInstante,
        ]
    );

    return (
        <div className={c['gateway-create']}>
            <Header backIcon onBack={onBack} title="创建网关" />

            <div className={c['steps-wrapper']}>
                <Steps className={c['steps']} current={stepIndex}>
                    <Step title="基础配置" />
                    <Step title="BLB配置" />
                    <Step title="订单确认" />
                </Steps>
            </div>

            <div className={c['form-wrapper']}>
                <BasicConfigForm
                    style={{display: stepIndex === 0 ? 'block' : 'none'}}
                    form={basicConfigFormInstante}
                />
                {
                    serviceMeshInstance
                    && (
                        <NetworkConfig
                            style={{display: stepIndex === 1 ? 'block' : 'none'}}
                            form={networkConfigFormInstante}
                            serviceMeshInstanceLoading={serviceMeshInstanceLoading}
                            serviceMeshInstance={serviceMeshInstance}
                        />
                    )
                }

                {
                    stepIndex === 2 && serviceMeshInstance
                    && (
                        <OrderConfirm
                            basicConfigFormInstante={basicConfigFormInstante}
                            networkConfigFormInstante={networkConfigFormInstante}
                            serviceMeshInstance={serviceMeshInstance}
                        />
                    )
                }
            </div>

            <FooterBtn
                stepIndex={stepIndex}
                clickPreStep={clickPreStep}
                clickNextStep={clickNextStep}
                isSubmit={isSubmit}
                clickSubmit={clickSubmit}
                onBack={onBack}
            />
        </div>
    );
}
