import Card from '@baidu/icloud-ui-card';
import {Col, FormInstance, Row} from '@osui/ui';
import {useMemo} from 'react';
import {IGatewayBasicConfig, IGatewayNetworkConfig, TGatewayResourceQuota} from '@/api/gateway';
import {gatewayResourceQuotaDict} from '@/dicts/gateway';
import {ServiceMeshInstance} from '@/interface/serviceMesh';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import NetworkTypeView from '../NetworkConfig/NetworkTypeView';
import HpaCardField from './HpaCardField';

import c from './index.less';
import LogCardField from './LogCardField';
import MonitorCardField from './MonitorCardField';
import TlsAccCardField from './TlsAccCardField';

interface IProps {
    basicConfigFormInstante: FormInstance;
    networkConfigFormInstante: FormInstance;
    serviceMeshInstance: ServiceMeshInstance;
}
export default function OrderConfirm({
    basicConfigFormInstante,
    networkConfigFormInstante,
    serviceMeshInstance,
    ...props
}: IProps) {
    const basicConfig: IGatewayBasicConfig = useMemo(
        () => {
            return basicConfigFormInstante.getFieldsValue();
        },
        [basicConfigFormInstante]
    );
    const networkConfig: IGatewayNetworkConfig = useMemo(
        () => {
            return networkConfigFormInstante.getFieldsValue();
        },
        [networkConfigFormInstante]
    );

    return (
        <div
            {...props}
            className={c['order-confirm']}
        >
            <Card title="基础配置">
                <Row gutter={[48, 16]}>
                    <Col span={8}>
                        <Card.Field title="网关名称">
                            {basicConfig.gatewayName}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="部署⽅式">
                            托管⽹关
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="⽹关类型">
                            ⼊⼝
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="实例规格">
                            {
                                gatewayResourceQuotaDict[
                                    (basicConfig.resourceQuota[0]) as TGatewayResourceQuota
                                ]
                            }
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="副本数">
                            {basicConfig.replicas}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <HpaCardField basicConfig={basicConfig} />
                    </Col>
                    <Col span={8}>
                        <LogCardField basicConfig={basicConfig} />
                    </Col>
                    <Col span={8}>
                        <MonitorCardField source="form" monitor={basicConfig.monitor} />
                    </Col>
                    <Col span={8}>
                        <TlsAccCardField tlsAcc={basicConfig.tlsAcc} />
                    </Col>
                </Row>
            </Card>

            <Card title="BLB配置">
                <Row gutter={16}>
                    <Col span={8}>
                        <Card.Field title="BLB实例">
                            {networkConfig.blbName || '--'}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="网络类型">
                            <NetworkTypeView serviceMeshInstance={serviceMeshInstance} />
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field className={c['elasticPublicNetwork']} title="公⽹访问">
                            {networkConfig.elasticPublicNetwork.enabled ? '开启' : '关闭'}
                            {
                                networkConfig.elasticPublicNetwork.enabled
                                && (
                                    <span className={c['elasticPublicNetwork-detail']}>
                                        绑定已有弹性公⽹，
                                        公网IP：
                                        <ExternalLink
                                            href={
                                                urls.external.eipDetail.fill({},
                                                    {
                                                        eip: networkConfig.elasticPublicNetwork.ip,
                                                        eipType: networkConfig.elasticPublicNetwork.eipType,
                                                    }
                                                )
                                            }
                                            value={networkConfig.elasticPublicNetwork.ip}
                                        />
                                    </span>
                                )
                            }
                        </Card.Field>
                    </Col>
                </Row>
            </Card>
        </div>
    );
}
