import Card from '@baidu/icloud-ui-card';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {Popover} from '@osui/ui';
import {IGatewayTlsAcc} from '@/api/gateway';
import EditGatewayTlsAccModal from './EditGatewayTlsAccModal';
import {tlsAccTooltip} from './constant';

interface IProps {
    tlsAcc: IGatewayTlsAcc;
    isEdit?: boolean;
}
export default function TlsAccCardField({tlsAcc, isEdit}: IProps) {
    const {enabled = false} = tlsAcc || {};

    return (
        <Card.Field
            title={
                <>
                    TLS硬件加速
                    <Popover content={tlsAccTooltip}>
                        <QuestionCircleOutlined />
                    </Popover>
                </>
            }
        >
            {enabled ? '开启' : '关闭'}

            {
                isEdit
                && <EditGatewayTlsAccModal {...tlsAcc} />
            }
        </Card.Field>
    );
}
