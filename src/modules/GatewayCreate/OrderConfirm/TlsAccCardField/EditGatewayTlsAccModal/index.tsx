import {OutlinedEditingSquare} from '@baidu/acud-icon';
import {Button, Form, message, Modal} from '@osui/ui';
import {useBoolean} from 'huse';
import {useCallback, useContext, useMemo} from 'react';
import {useParams} from 'react-router-dom';
import {IGatewayTlsAcc, updateGatewayTlsAccApi} from '@/api/gateway';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {Context} from '@/modules/GatewayDetail/InstanceDetail';
import GatewayTlsAcc from '@/modules/GatewayCreate/BasicConfigForm/GatewayTlsAcc';
import c from './index.less';

type IProps = IGatewayTlsAcc;
export default function EditGatewayTlsAccModal(tlsAcc: IProps) {
    const {getGatewayRequest} = useContext(Context);
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{ gatewayId: string }>();

    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);
    const [confirmLoading, {on: onConfirmLoading, off: offConfirmLoading}] = useBoolean(false);

    const [formDataInstante] = Form.useForm();
    const initialValues = useMemo(
        () => {
            return tlsAcc.enabled ? tlsAcc : {...tlsAcc, enabled: false};
        },
        [tlsAcc]
    );

    const onOk = useCallback(
        async (e: React.MouseEvent<HTMLElement>) => {
            e.preventDefault();
            try {
                const {
                    tlsAcc: {enabled},
                } = await formDataInstante.validateFields();

                onConfirmLoading();
                const res = await updateGatewayTlsAccApi({
                    serviceMeshInstanceId,
                    gatewayId,
                    enabled,
                });
                if (res) {
                    message.success('TLS加速编辑成功。');
                    offVisible();
                    getGatewayRequest();
                }
                else {
                    message.error('TLS加速编辑失败，请重新操作。');
                }
            } catch (error) {
                // console.log(error);
            } finally {
                offConfirmLoading();
            }
        },
        [
            formDataInstante, serviceMeshInstanceId, gatewayId,
            onConfirmLoading, offConfirmLoading, offVisible, getGatewayRequest,
        ]
    );

    Form.useLabelLayout('basic', 0);

    return (
        <>
            <Button type="link" onClick={onVisible}>
                <OutlinedEditingSquare className={c['edit-icon']} />
            </Button>

            <Modal
                title="编辑"
                visible={visible}
                confirmLoading={confirmLoading}
                onCancel={offVisible}
                onOk={onOk}
            >
                <Form
                    name="basic"
                    labelAlign="left"
                    initialValues={{tlsAcc: initialValues}}
                    form={formDataInstante}
                >
                    <GatewayTlsAcc />
                </Form>
            </Modal>
        </>
    );
}
