import Card from '@baidu/icloud-ui-card';
import {TGatewayResourceQuota} from '@/api/gateway';
import EditGatewayResourceQuotaModal from './EditGatewayResourceQuotaModal';

interface IProps {
    resourceQuota: TGatewayResourceQuota;
    isEdit?: boolean;
}
export default function ResourceQuotaCardField({resourceQuota, isEdit}: IProps) {
    return (
        <Card.Field title="实例规格">
            {resourceQuota}

            {
                isEdit
                && <EditGatewayResourceQuotaModal resourceQuota={resourceQuota} />
            }
        </Card.Field>
    );
}
