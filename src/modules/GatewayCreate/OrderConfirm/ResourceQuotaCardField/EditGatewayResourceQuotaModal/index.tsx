import {OutlinedEditingSquare} from '@baidu/acud-icon';
import {Button, Form, message, Modal} from '@osui/ui';
import {useBoolean} from 'huse';
import {useCallback, useContext, useMemo} from 'react';
import {useParams} from 'react-router-dom';
import {TGatewayResourceQuota, updateGatewayResourceQuotaApi} from '@/api/gateway';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {Context} from '@/modules/GatewayDetail/InstanceDetail';
import ResourceQuotaTable from '@/modules/GatewayCreate/BasicConfigForm/GatewayResourceQuota';
import c from './index.less';

interface IProps {
    resourceQuota: TGatewayResourceQuota;
}
export default function EditGatewayResourceQuotaModal({resourceQuota}: IProps) {
    const {getGatewayRequest} = useContext(Context);
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{ gatewayId: string }>();

    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);
    const [confirmLoading, {on: onConfirmLoading, off: offConfirmLoading}] = useBoolean(false);

    const [formDataInstante] = Form.useForm();

    // 网关实例规格 - 降额，原先的 4C8G 需默认降为 2C4G
    const initialValues = useMemo(
        () => {
            return resourceQuota === '4C8G'
                ? {resourceQuota: ['2C4G']}
                : {resourceQuota: [resourceQuota]};
        },
        [resourceQuota]
    );
    const defaultSelectedRowKeys: TGatewayResourceQuota[] = useMemo(
        () => {
            return resourceQuota === '4C8G'
                ? ['2C4G']
                : [resourceQuota];
        },
        [resourceQuota]
    );

    const onOk = useCallback(
        async (e: React.MouseEvent<HTMLElement>) => {
            e.preventDefault();
            try {
                const {
                    resourceQuota,
                } = await formDataInstante.validateFields();

                onConfirmLoading();
                const res = await updateGatewayResourceQuotaApi({
                    serviceMeshInstanceId,
                    gatewayId,
                    resourceQuota: resourceQuota[0] || '1C2G',
                });
                if (res) {
                    message.success('实例规格编辑成功。');
                    offVisible();
                    getGatewayRequest();
                }
                else {
                    message.error('实例规格编辑失败，请重新操作。');
                }
            } catch (error) {
                // console.log(error);
            } finally {
                offConfirmLoading();
            }
        },
        [
            formDataInstante, serviceMeshInstanceId, gatewayId,
            onConfirmLoading, offConfirmLoading, offVisible, getGatewayRequest,
        ]
    );

    Form.useLabelLayout('basic', 0);

    return (
        <>
            <Button type="link" onClick={onVisible}>
                <OutlinedEditingSquare className={c['edit-icon']} />
            </Button>

            <Modal
                title="编辑"
                visible={visible}
                confirmLoading={confirmLoading}
                onCancel={offVisible}
                onOk={onOk}
            >
                <Form
                    name="basic"
                    labelAlign="left"
                    initialValues={initialValues}
                    form={formDataInstante}
                >
                    <ResourceQuotaTable defaultSelectedRowKeys={defaultSelectedRowKeys} />
                </Form>
            </Modal>
        </>
    );
}
