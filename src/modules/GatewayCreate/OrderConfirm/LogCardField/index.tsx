import Card from '@baidu/icloud-ui-card';
import {IGatewayBasicConfig} from '@/api/gateway';
import ExternalLink from '@/components/ExternalLink';
import {gatewayLogTypeDict} from '@/dicts/gateway';
import urls from '@/urls';

import c from './index.less';
import EditGatewayLogModal from './EditGatewayLogModal';

interface IProps {
    basicConfig: IGatewayBasicConfig;
    isEdit?: boolean;
}
export default function LogCardField({basicConfig, isEdit}: IProps) {
    const {enabled, type, logFile} = basicConfig?.log ?? {};

    return (
        <Card.Field title="日志采集" className={c['log']}>
            {enabled ? '开启' : '关闭'}
            {
                enabled
                && (
                    <span className={c['type-logFile']}>
                        日志存储：{gatewayLogTypeDict[type]}，
                        日志集：
                        <ExternalLink
                            href={
                                urls.external.bls.logDetail.fill(
                                    {},
                                    {
                                        logStoreName: logFile,
                                        logStreamName: null,
                                    }
                                )
                            }
                            value={logFile}
                        />
                    </span>
                )
            }

            {
                isEdit
                && <EditGatewayLogModal {...basicConfig.log} />
            }
        </Card.Field>
    );
}
