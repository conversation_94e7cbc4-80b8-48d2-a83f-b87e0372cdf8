import {IMonitorItem} from '@/api/createServiceMesh';

export const getmonitorForRequest = (monitor: IMonitorItem): IMonitorItem => {
    if (!monitor || !monitor.enabled) {
        return {
            enabled: false,
        };
    }

    const {enabled, instances} = monitor;
    const [region = '', idObjStr = '{}'] = (instances as string[]);
    const {id} = JSON.parse(idObjStr);
    return {
        enabled,
        instances: [
            {
                region,
                id,
            },
        ],
    };
};
