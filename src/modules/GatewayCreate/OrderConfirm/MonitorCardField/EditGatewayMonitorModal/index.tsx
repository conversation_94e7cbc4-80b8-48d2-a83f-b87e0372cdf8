import {OutlinedEditingSquare} from '@baidu/acud-icon';
import {Button, Form, message, Modal} from '@osui/ui';
import {useBoolean} from 'huse';
import {useCallback, useContext, useMemo} from 'react';
import {useParams} from 'react-router-dom';
import {updateGatewayMonitorApi} from '@/api/gateway';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {Context} from '@/modules/GatewayDetail/InstanceDetail';
import MonitorFormItem from '@/components/MonitorFormItem';
import {IInstanceItem, IMonitorItem} from '@/api/createServiceMesh';
import c from './index.less';
import {getmonitorForRequest} from './util';

type IProps = IMonitorItem;
export default function EditGatewayMonitorModal(monitor: IProps) {
    const {getGatewayRequest} = useContext(Context);
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{ gatewayId: string }>();

    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);
    const [confirmLoading, {on: onConfirmLoading, off: offConfirmLoading}] = useBoolean(false);

    const [formDataInstante] = Form.useForm();
    const initialValues = useMemo(
        () => {
            if (!monitor || !monitor.enabled || !monitor.instances) {
                return {
                    ...monitor,
                    instances: [],
                };
            }

            const {region, id, name} = (monitor.instances[0] as IInstanceItem);
            return {
                ...monitor,
                instances: [region, JSON.stringify({id, name})],
            };
        },
        [monitor]
    );

    const onOk = useCallback(
        async (e: React.MouseEvent<HTMLElement>) => {
            e.preventDefault();
            const {
                monitor,
            } = await formDataInstante.validateFields();
            const monitorForRequest = getmonitorForRequest(monitor);

            onConfirmLoading();
            try {
                await updateGatewayMonitorApi({
                    serviceMeshInstanceId,
                    gatewayId,
                    ...monitorForRequest,
                });
                message.success('监控指标采集编辑成功。');
                offVisible();
                getGatewayRequest();
            } catch (error) {
                message.error('监控指标采集编辑失败，请重新操作。');
            } finally {
                offConfirmLoading();
            }
        },
        [
            formDataInstante, serviceMeshInstanceId, gatewayId,
            onConfirmLoading, offConfirmLoading, offVisible, getGatewayRequest,
        ]
    );

    Form.useLabelLayout('basic', 0);

    return (
        <>
            <Button type="link" onClick={onVisible}>
                <OutlinedEditingSquare className={c['edit-icon']} />
            </Button>

            <Modal
                title="编辑"
                visible={visible}
                confirmLoading={confirmLoading}
                onCancel={offVisible}
                onOk={onOk}
            >
                <Form
                    name="basic"
                    labelAlign="left"
                    initialValues={{monitor: initialValues}}
                    form={formDataInstante}
                >
                    <MonitorFormItem type="gateway" />
                </Form>
            </Modal>
        </>
    );
}
