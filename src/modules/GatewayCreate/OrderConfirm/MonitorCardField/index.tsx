import Card from '@baidu/icloud-ui-card';
import {useMemo} from 'react';
import MonitorExternalLink from '@/modules/ServiceMeshBaseInfo/MonitorExternalLink';
import {IMonitorItem} from '@/api/createServiceMesh';
import EditGatewayMonitorModal from './EditGatewayMonitorModal';
import c from './index.less';

type TSource = 'request' | 'form';
interface IProps {
    source: TSource;
    monitor: IMonitorItem;
    isEdit?: boolean;
}
export default function MonitorCardField({source, monitor, isEdit}: IProps) {
    const {enabled, instances} = monitor;
    const monitorForEdit = useMemo(
        () => {
            let instancesProp =
                enabled
                    ? instances
                    : [];

            if (source === 'form' && instances) {
                instancesProp = enabled
                    ? [
                        {
                            region: (instances[0] as string),
                            id: JSON.parse(instances[1] as string).id,
                            name: JSON.parse(instances[1] as string).name,
                        },
                    ]
                    : [];
            }

            return {
                enabled,
                instances: instancesProp,
            };
        },
        [enabled, instances, source]
    );

    return (
        <Card.Field title="监控指标采集" className={c['monitor']}>
            {enabled ? '开启' : '关闭'}
            {
                enabled
                && (
                    <span className={c['instances']}>
                        <MonitorExternalLink monitor={monitorForEdit} />
                    </span>
                )
            }

            {
                isEdit
                && <EditGatewayMonitorModal {...monitorForEdit} />
            }
        </Card.Field>
    );
}
