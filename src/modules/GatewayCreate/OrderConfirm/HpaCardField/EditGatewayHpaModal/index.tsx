import {OutlinedEditingSquare} from '@baidu/acud-icon';
import {Button, Form, message, Modal} from '@osui/ui';
import {useBoolean} from 'huse';
import {useCallback, useContext, useMemo} from 'react';
import {useParams} from 'react-router-dom';
import {IGatewayHpa, updateGatewayHpaApi} from '@/api/gateway';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {Context} from '@/modules/GatewayDetail/InstanceDetail';
import Gatewayhpa from '../../../BasicConfigForm/GatewayHpa';

import c from './index.less';

type IProps = IGatewayHpa;
export default function EditGatewayHpaModal(hpa: IProps) {
    const {getGatewayRequest} = useContext(Context);
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{ gatewayId: string }>();

    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);
    const [confirmLoading, {on: onConfirmLoading, off: offConfirmLoading}] = useBoolean(false);

    const [formDataInstante] = Form.useForm();
    const initialValues = useMemo<IGatewayHpa>(
        () => {
            return hpa.enabled ? hpa : {...hpa, minReplicas: 1, maxReplicas: 3};
        },
        [hpa]
    );


    const onOk = useCallback(
        async (e: React.MouseEvent<HTMLElement>) => {
            e.preventDefault();
            try {
                const {
                    hpa: {enabled, minReplicas, maxReplicas},
                } = await formDataInstante.validateFields();

                onConfirmLoading();
                const res = await updateGatewayHpaApi({
                    serviceMeshInstanceId,
                    gatewayId,
                    enabled,
                    minReplicas,
                    maxReplicas,
                });
                if (res) {
                    message.success('扩缩容HPA编辑成功。');
                    offVisible();
                    getGatewayRequest();
                }
                else {
                    message.error('扩缩容HPA编辑失败，请重新操作。');
                }
            } catch (error) {
                // console.log(error);
            } finally {
                offConfirmLoading();
            }
        },
        [
            formDataInstante, serviceMeshInstanceId, gatewayId,
            onConfirmLoading, offConfirmLoading, offVisible, getGatewayRequest,
        ]
    );

    Form.useLabelLayout('basic', 0);

    return (
        <>
            <Button type="link" onClick={onVisible}>
                <OutlinedEditingSquare className={c['edit-icon']} />
            </Button>

            <Modal
                title="编辑"
                visible={visible}
                confirmLoading={confirmLoading}
                onCancel={offVisible}
                onOk={onOk}
            >
                <Form
                    name="basic"
                    labelAlign="left"
                    initialValues={{hpa: initialValues}}
                    form={formDataInstante}
                >
                    <Gatewayhpa />
                </Form>
            </Modal>
        </>
    );
}
