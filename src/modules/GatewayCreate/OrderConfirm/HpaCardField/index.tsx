import Card from '@baidu/icloud-ui-card';
import {IGatewayBasicConfig} from '@/api/gateway';
import EditGatewayHpaModal from './EditGatewayHpaModal';

import c from './index.less';

interface IProps {
    basicConfig: IGatewayBasicConfig;
    isEdit?: boolean;
}
export default function HpaCardField({basicConfig, isEdit}: IProps) {
    const {enabled, minReplicas, maxReplicas} = basicConfig?.hpa ?? {};

    return (
        <Card.Field title="扩缩容HPA" className={c['hpa']}>
            {enabled ? '开启' : '关闭'}
            {
                enabled
                && (
                    <span className={c['range']}>
                        最小副本数：{minReplicas}，
                        最大副本数：{maxReplicas}
                    </span>
                )
            }

            {
                isEdit
                && <EditGatewayHpaModal {...basicConfig.hpa} />
            }
        </Card.Field>
    );
}
