import {Button} from '@osui/ui';
import c from './index.less';

interface IProps {
    stepIndex: number;
    clickPreStep: () => void;
    clickNextStep: () => void;
    isSubmit: boolean;
    clickSubmit: () => void;
    onBack: () => void;
}
export default function FooterBtn(
    {
        stepIndex,
        clickPreStep,
        clickNextStep,
        isSubmit,
        clickSubmit,
        onBack,
    }: IProps
) {
    return (
        <>
            <div className={c['footer-btn-gap']}></div>
            <div className={c['footer-btn']}>
                {
                    stepIndex > 0
                    && <Button size="large" onClick={clickPreStep}>上一步</Button>
                }
                {
                    stepIndex < 2
                    && <Button size="large" type="primary" onClick={clickNextStep}>下一步</Button>
                }
                {
                    stepIndex === 2
                    && (
                        <Button
                            size="large"
                            type="primary"
                            loading={isSubmit}
                            disabled={isSubmit}
                            onClick={clickSubmit}
                        >
                            提交订单
                        </Button>
                    )
                }
                <Button size="large" onClick={onBack}>取消</Button>
            </div>
        </>
    );
}
