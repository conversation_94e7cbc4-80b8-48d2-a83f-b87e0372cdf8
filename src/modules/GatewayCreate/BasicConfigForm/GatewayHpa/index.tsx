import {Form, InputNumber, Switch} from '@osui/ui';
import ExternalLink from '@/components/ExternalLink';
import {maxReplicasRules, minReplicasRules} from '../rules';

import c from './index.less';

export default function GatewayHpa() {
    return (
        <div className={c['hpa']}>
            <Form.Item
                label="扩缩容HPA"
                name={['hpa', 'enabled']}
                valuePropName="checked"
            >
                <Switch />
            </Form.Item>

            {/* TODO 暂时隐掉 */}
            {false && <ExternalLink className={c['link']} value="扩缩容HPA策略" />}

            <Form.Item
                shouldUpdate
                noStyle
            >
                {
                    ({getFieldValue}) => getFieldValue(['hpa', 'enabled'])
                    && (
                        <Form.Item
                            label=" "
                            colon={false}
                            help="副本扩缩容范围为1-3。"
                        >
                            <div className={c['range']}>
                                <Form.Item
                                    name={['hpa', 'minReplicas']}
                                    rules={minReplicasRules}
                                >
                                    <InputNumber />
                                </Form.Item>
                                <div className={c['between']}>至</div>
                                <Form.Item
                                    name={['hpa', 'maxReplicas']}
                                    rules={maxReplicasRules}
                                >
                                    <InputNumber />
                                </Form.Item>
                            </div>
                        </Form.Item>
                    )
                }
            </Form.Item>
        </div>
    );
}
