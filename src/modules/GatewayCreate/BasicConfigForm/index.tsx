import {Form, FormInstance, Input, InputNumber} from '@osui/ui';
import {HTMLAttributes} from 'react';
import MonitorFormItem from '@/components/MonitorFormItem';
import GatewayResourceQuota from './GatewayResourceQuota';
import GatewayLog from './GatewayLog';
import {gatewayNameRules, replicasRules} from './rules';
import GatewayHpa from './GatewayHpa';
import c from './index.less';
import GatewayTlsAcc from './GatewayTlsAcc';

interface IProps extends HTMLAttributes<HTMLElement> {
    form: FormInstance;
}

const initialValues = {
    gatewayName: '',
    deployMode: 'hosting',
    gatewayType: 'ingress',
    resourceQuota: ['1C2G'],
    replicas: 1,
    hpa: {
        enabled: false,
        minReplicas: 1,
        maxReplicas: 3,
    },
    log: {
        enabled: false,
        type: 'BLS',
        logFile: '',
    },
    monitor: {
        enabled: false,
        instances: [],
    },
    tlsAcc: {
        enabled: false,
    },
};

export default function BasicConfigForm({form, ...props}: IProps) {
    Form.useLabelLayout('basic', 0);

    return (
        <Form
            name="basic"
            {...props}
            className={c['form']}
            labelAlign="left"
            initialValues={initialValues}
            form={form}
        >
            <Form.Item
                required
                label="网关名称"
                name={['gatewayName']}
                rules={gatewayNameRules}
                help="支持大小写字母、数字、中文以及-_ /.特殊字符，必须以字母开头，长度不超过65个字符"
            >
                <Input
                    className={c['gatewayname-input']}
                    maxLength={65}
                    showCount
                    placeholder="输入的网关名称不支持修改，请慎重输入。"
                />
            </Form.Item>
            <Form.Item
                key="deployMode"
                label="部署⽅式"
                name={['deployMode']}
            >
                托管⽹关
            </Form.Item>
            <Form.Item
                key="gatewayType"
                label="⽹关类型"
                name={['gatewayType']}
            >
                入口
            </Form.Item>

            <GatewayResourceQuota />

            <Form.Item
                required
                label="副本数"
                name={['replicas']}
                rules={replicasRules}
                // TODO 可能会变更文案 + 补充工单链接（提升用户体验）
                help="副本数范围1-3（免费期间最大允许创建3个副本，如需更大的副本数请提工单），推荐使⽤2个及以上副本以保证可⽤性。"
            >
                <InputNumber />
            </Form.Item>

            <GatewayHpa />

            <GatewayLog form={form} />

            <MonitorFormItem type="gateway" />

            <GatewayTlsAcc />

            <Form.Item
                noStyle
                name={['log', 'enabled']}
            />
        </Form>
    );
}
