import {Form} from '@osui/ui';
import {TGatewayResourceQuota} from '@/api/gateway';
import GatewayResourceQuotaTable from './GatewayResourceQuotaTable';

interface IProps {
    defaultSelectedRowKeys?: TGatewayResourceQuota[];
}
export default function GatewayResourceQuota({
    defaultSelectedRowKeys = ['1C2G'],
}: IProps) {
    return (
        <Form.Item
            key="resourceQuota"
            required
            label="实例规格"
            name={['resourceQuota']}
        >
            <GatewayResourceQuotaTable defaultSelectedRowKeys={defaultSelectedRowKeys} />
        </Form.Item>
    );
}
