import {Table} from '@osui/ui';
import {TableRowSelection} from 'antd/lib/table/interface';
import {TGatewayResourceQuota} from '@/api/gateway';
import {columns} from '../columns';
import c from './index.less';

interface TResourceQuota {
    id: TGatewayResourceQuota;
    cpu: string;
    memory: string;
    price: string;
    disabled?: boolean;
}
const dataSource: TResourceQuota[] = [
    {id: '1C2G', cpu: '1核', memory: '2G', price: '0.00'},
    {id: '2C4G', cpu: '2核', memory: '4G', price: '0.00'},
    {id: '4C8G', cpu: '4核', memory: '8G', price: '0.00', disabled: true},
];

interface IProps {
    onChange?: () => void;
    defaultSelectedRowKeys?: TGatewayResourceQuota[];
}
export default function GatewayResourceQuotaTable({
    onChange,
    defaultSelectedRowKeys = ['1C2G'],
}: IProps) {
    const rowSelection: TableRowSelection<TResourceQuota> = {
        type: 'radio',
        defaultSelectedRowKeys,
        onChange,
    };

    return (
        <Table
            className={c['resource-quota-table']}
            rowSelection={rowSelection}
            columns={columns}
            rowKey="id"
            dataSource={dataSource.filter(({disabled}) => !disabled)}
        />
    );
}
