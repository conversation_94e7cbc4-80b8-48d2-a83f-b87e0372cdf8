import {Rule} from 'antd/lib/form';

const isValueInRange = (value: number, start = 1, end = 3) => {
    return (value >= start && value <= end) ? Promise.resolve() : Promise.reject(new Error('范围不正确'));
};
export const gatewayNameRules: Rule[] = [
    {required: true, message: ''},
    {pattern: /^[a-zA-Z][\w-/.\u4e00-\u9fa5]{0,64}$/, message: ''},
];

export const replicasRules: Rule[] = [
    {required: true, message: ''},
    {
        validator: (rule, value: number) => {
            return isValueInRange(value);
        },
    },
];

const minAndMaxReplicasCommonRule: Rule = ({getFieldValue}) => ({
    validator() {
        const minReplicas = getFieldValue(['hpa', 'minReplicas']);
        const maxReplicas = getFieldValue(['hpa', 'maxReplicas']);

        return minReplicas <= maxReplicas ? Promise.resolve() : Promise.reject(new Error('左边值应小于等于右边值'));
    },
});
export const minReplicasRules: Rule[] = [
    {required: true, message: ''},
    {
        validator: (rule, value) => {
            return isValueInRange(value);
        },
    },
    minAndMaxReplicasCommonRule,
];

export const maxReplicasRules: Rule[] = [
    {required: true, message: ''},
    {
        validator: (rule, value: number) => {
            return isValueInRange(value);
        },
    },
    minAndMaxReplicasCommonRule,
];

export const logFileRules: Rule[] = [
    {required: true, message: '请选择日志集'},
];
