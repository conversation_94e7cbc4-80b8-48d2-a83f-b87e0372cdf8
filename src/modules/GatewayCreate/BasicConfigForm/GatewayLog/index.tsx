import {Form, FormInstance, Select, Switch} from '@osui/ui';
import {useCallback, useEffect, useMemo} from 'react';
import {OutlinedRefresh} from '@baidu/acud-icon';
import {useRequestCallback} from 'huse';
import {getLogstoreListApi} from '@/api/gateway';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {IS_QASANDBOX} from '@/dicts';
import {gatewayLogTypeDict} from '@/dicts/gateway';
import {logFileRules} from '../rules';
import c from './index.less';

const logTypeOptions: Array<{label: string, value: string}> = Object.entries(gatewayLogTypeDict)
    .map(([value, label]) =>
        ({label, value})
    );

interface IProps {
    form: FormInstance;
}
export default function GatewayLog({form}: IProps) {
    const [getList, {pending, data}] = useRequestCallback(getLogstoreListApi, undefined);
    const options = useMemo(
        () => {
            // TODO 不同的环境有不同的逻辑等时，需抽离处理
            const resultNew = IS_QASANDBOX
                ? [
                    {logStoreName: 'a'},
                    {logStoreName: 'bug2'},
                ]
                : [];

            if (data && data.result) {
                resultNew.push(...data.result);
            }

            return resultNew.map(({logStoreName}) => ({label: logStoreName, value: logStoreName}));
        },
        [data]
    );

    const clickRefresh = useCallback(
        () => {
            form.setFieldsValue({
                log: {
                    logFile: '',
                },
            });
            getList();
        },
        [form, getList]
    );

    useEffect(
        () => {
            getList();
        },
        [getList]
    );

    return (
        <div className={c['gateway-log']}>
            <Form.Item
                label="日志采集"
                name={['log', 'enabled']}
                valuePropName="checked"
            >
                <Switch />
            </Form.Item>

            <Form.Item
                shouldUpdate
                noStyle
            >
                {
                    ({getFieldValue}) => getFieldValue(['log', 'enabled'])
                    && (
                        <div className={c['log-form-wrapper']}>
                            <Form.Item
                                required
                                label="日志存储"
                                name={['log', 'type']}
                            >
                                <Select disabled options={logTypeOptions} />
                            </Form.Item>

                            <div className={c['log-file']}>
                                <Form.Item
                                    required
                                    label="日志集"
                                    name={['log', 'logFile']}
                                    rules={logFileRules}
                                >
                                    <Select loading={pending} options={options} />
                                </Form.Item>

                                <div className={c['refresh-wrapper']}>
                                    <OutlinedRefresh className={c['refresh']} onClick={clickRefresh} />
                                    <ExternalLink
                                        value="管理日志集"
                                        href={urls.external.bls.logList.fill()}
                                    />
                                </div>
                            </div>
                        </div>
                    )
                }
            </Form.Item>
        </div>
    );
}
