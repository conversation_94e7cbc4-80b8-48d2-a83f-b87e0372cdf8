import {Form, Popover, Switch} from '@osui/ui';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {tlsAccTip} from './constant';

export default function GatewayTlsAcc() {
    return (
        <Form.Item
            label={
                <>
                    TLS硬件加速：{
                        <Popover content={tlsAccTip}>
                            <QuestionCircleOutlined />
                        </Popover>
                    }
                </>
            }
            colon={false}
        >
            <Form.Item
                name={['tlsAcc', 'enabled']}
                valuePropName="checked"
            >
                <Switch />
            </Form.Item>
        </Form.Item>
    );
}
