import {IColumns, IColumnFilters} from '@baidu/icloud-ui-pro-table';
import {eipPurchaseTypeDict} from '@/dicts/gateway';
import {TEipPurchaseType} from '@/api/gateway';

const eipPurchaseTypeFilters: IColumnFilters = Object.entries(eipPurchaseTypeDict)
    .map(([k, v]) => ({text: v, value: k}));

export const columns: IColumns = [
    {
        title: '实例名称',
        dataIndex: 'name',
        render(value) {
            return value;
        },
    },
    {
        title: '公⽹IP',
        dataIndex: 'eip',
        render(value) {
            return value;
        },
    },
    {
        title: '线路类型',
        dataIndex: 'eipPurchaseType',
        filterType: 'single',
        filters: eipPurchaseTypeFilters,
        render(value: TEipPurchaseType) {
            return eipPurchaseTypeDict[value];
        },
    },
    {
        title: '最⼤宽带',
        dataIndex: 'bandWidth',
        render(value) {
            return `${value}Mbps`;
        },
    },
];
