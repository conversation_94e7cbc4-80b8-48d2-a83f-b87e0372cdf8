import {IFilters} from '@baidu/icloud-ui-pro-table';

export const filters: IFilters = [
    {
        name: ['keywordType', 'keyword'],
        renderType: 'groupSearch',
        defaultValue: ['INSTANCE_NAME', ''],
        props: {
            options: [
                {
                    label: '实例名称',
                    value: 'INSTANCE_NAME',
                },
                {
                    label: '公网IP',
                    value: 'INSTANCE_EIP',
                },
            ],
        },
    },
];
