import ProTable from '@baidu/icloud-ui-pro-table';
import {useCallback, useMemo} from 'react';
import {TableRowSelection} from 'antd/lib/table/interface';
import {getUnusedEipListApi, IUnusedEipItem} from '@/api/gateway';
import {formatMultiValueQuery} from '@/utils';
import {columns} from './columns';
import {filters} from './filters';
import {tools} from './tools';

interface IProps {
    defaultSelectedRowKeys: string[];
    onChange: (selectedRowKeys: React.Key[], selectedRows: IUnusedEipItem[]) => void;
}

export default function EipProTable({defaultSelectedRowKeys, onChange, ...props}: IProps) {
    const rowSelection: TableRowSelection<IUnusedEipItem> = useMemo(
        () => {
            return {
                type: 'radio',
                defaultSelectedRowKeys: defaultSelectedRowKeys,
                onChange,
                getCheckboxProps: () => ({
                    disabled: defaultSelectedRowKeys.length > 0,
                }),
            };
        },
        [defaultSelectedRowKeys, onChange]
    );

    const getList = useCallback(
        params => {
            const newParams = formatMultiValueQuery(params, ['eipPurchaseType']);
            return getUnusedEipListApi(newParams);
        },
        []
    );

    return (
        <ProTable
            {...props}
            rowSelection={rowSelection}
            rowKey="shortId"
            columns={columns}
            request={getList}
            filters={filters}
            tools={tools}
        />
    );
}
