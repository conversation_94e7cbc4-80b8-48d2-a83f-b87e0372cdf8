import {CheckboxOptionType, Form, FormInstance, Radio, Switch} from '@osui/ui';
import {useCallback, useMemo} from 'react';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import EipProTable from './EipProTable';
import {idRules} from './rules';

interface IProps {
    defaultSelectedRowKeys: string[];
    form: FormInstance;
}

export default function ElasticPublicNetwork({defaultSelectedRowKeys, form}: IProps) {
    const changeEipId = useCallback(
        (selectedKeys, selectedRows) => {
            form.setFieldsValue({
                elasticPublicNetwork: {
                    id: [selectedKeys[0]],
                    ip: selectedRows[0].eip,
                    eipType: selectedRows[0].eipType,
                },
            });
        },
        [form]
    );

    // 所选blb已绑定eip时，elasticPublicNetwork所有的东西不可被用户变更
    const isChangeElasticPublicNetworkDisabled = defaultSelectedRowKeys.length > 0;

    const options: CheckboxOptionType[] = useMemo(
        () => {
            return [
                {disabled: true, label: '购买弹性公网 IP（公网带宽：100 Mbps)', value: 'BUY'},
                {disabled: isChangeElasticPublicNetworkDisabled, label: '绑定已有弹性公网 IP', value: 'BIND'},
            ];
        },
        [isChangeElasticPublicNetworkDisabled]
    );

    return (
        <>
            {
                !isChangeElasticPublicNetworkDisabled
                && (
                    <>
                        <Form.Item
                            name={['elasticPublicNetwork', 'enabled']}
                            valuePropName="checked"
                        >
                            <Switch />
                        </Form.Item>

                        <Form.Item
                            shouldUpdate
                            noStyle
                        >
                            {
                                ({getFieldValue}) => {
                                    const enabled = getFieldValue(['elasticPublicNetwork', 'enabled']);
                                    return enabled && (
                                        <Form.Item
                                            name={['elasticPublicNetwork', 'type']}
                                        >
                                            <Radio.Group optionType="default" options={options} />
                                        </Form.Item>
                                    );
                                }
                            }
                        </Form.Item>
                    </>
                )
            }

            <Form.Item
                shouldUpdate
                noStyle
            >
                {
                    ({getFieldValue}) => {
                        const {enabled, type, ip, eipType} = getFieldValue(['elasticPublicNetwork']);


                        return enabled && type === 'BIND' && (
                            <>
                                <Form.Item
                                    required
                                    label=""
                                    name={['elasticPublicNetwork', 'id']}
                                    rules={idRules}
                                >
                                    {
                                        isChangeElasticPublicNetworkDisabled
                                            ? (
                                                <>
                                                    公网IP：
                                                    <ExternalLink
                                                        href={
                                                            urls.external.eipDetail.fill({},
                                                                {
                                                                    eip: ip,
                                                                    eipType: eipType,
                                                                }
                                                            )
                                                        }
                                                        value={ip}
                                                    />
                                                </>
                                            )
                                            : (
                                                <EipProTable
                                                    defaultSelectedRowKeys={defaultSelectedRowKeys}
                                                    onChange={changeEipId}
                                                />
                                            )
                                    }
                                </Form.Item>

                                <Form.Item
                                    noStyle
                                    name={['elasticPublicNetwork', 'enabled']}
                                />
                                <Form.Item
                                    noStyle
                                    name={['elasticPublicNetwork', 'type']}
                                />
                                <Form.Item
                                    noStyle
                                    name={['elasticPublicNetwork', 'name']}
                                />
                                <Form.Item
                                    noStyle
                                    name={['elasticPublicNetwork', 'ip']}
                                />
                                <Form.Item
                                    noStyle
                                    name={['elasticPublicNetwork', 'eipType']}
                                />
                            </>
                        );
                    }
                }
            </Form.Item>
        </>
    );
}
