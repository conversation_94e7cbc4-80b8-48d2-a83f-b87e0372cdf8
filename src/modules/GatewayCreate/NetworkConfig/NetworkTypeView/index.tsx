import {ServiceMeshInstance} from '@/interface/serviceMesh';

interface IProps {
    serviceMeshInstance: ServiceMeshInstance;
}

export default function NetworkTypeView({serviceMeshInstance}: IProps) {
    if (!(serviceMeshInstance && serviceMeshInstance.networkType)) {
        return <span>--</span>;
    }

    const {
        networkType: {
            vpcNetworkName = '--',
            vpcNetworkCidr = '--',
            subnetName = '--',
            subnetCidr = '--',
        },
    } = serviceMeshInstance;
    return (
        <span>
            VPC：{vpcNetworkName}（{vpcNetworkCidr}），子网：{subnetName}（{subnetCidr}）
        </span>
    );
}
