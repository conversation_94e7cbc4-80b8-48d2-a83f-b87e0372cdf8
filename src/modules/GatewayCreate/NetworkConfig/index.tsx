import {OutlinedRefresh} from '@baidu/acud-icon';
import {Form, FormInstance, Popover, Spin} from '@osui/ui';
import {useRequestCallback} from 'huse';
import {HTMLAttributes, useCallback, useEffect, useMemo, useState} from 'react';
import {DefaultOptionType} from 'antd/lib/select';
import {QuestionCircleOutlined} from '@ant-design/icons';
import AddButton from '@/components/AddButton';
import {getAvailableBlbList, IAvailableBlbItem} from '@/api/gateway';
import {ServiceMeshInstance} from '@/interface/serviceMesh';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {IS_QASANDBOX} from '@/dicts';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import ElasticPublicNetwork from './ElasticPublicNetwork';
import {blbIdRules} from './rules';
import BlbIdSelect from './BlbIdSelect';
import c from './index.less';
import NetworkTypeView from './NetworkTypeView';
import {blbIdTip} from './tooltip';

interface IProps extends HTMLAttributes<HTMLElement> {
    form: FormInstance;
    serviceMeshInstanceLoading: boolean;
    serviceMeshInstance: ServiceMeshInstance;
}

const initialElasticPublicNetwork = {
    enabled: false,
    type: 'BIND',
    id: '',
    ip: '',
    eipType: '',
};
const initialValues = {
    blbId: undefined,
    blbName: undefined,
    networkType: {
        vpcNetworkId: '',
        vpcNetworkName: '',
        vpcNetworkCidr: '',
        subnetId: '',
        subnetName: '',
        subnetCidr: '',
    },
    elasticPublicNetwork: initialElasticPublicNetwork,
};

const defaultBlbIdList: IAvailableBlbItem[] = [];
export default function NetworkConfig({form, serviceMeshInstance, serviceMeshInstanceLoading, ...props}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [getBlbList, res] = useRequestCallback(getAvailableBlbList, {serviceMeshInstanceId});
    const {pending: blbIdListLoading, data} = res;
    const blbIdList = data?.result ?? defaultBlbIdList;

    const [defaultSelectedRowKeys, setDefaultSelectedRowKeys] = useState<string[]>([]);

    const options: DefaultOptionType[] = useMemo(
        () => {
            return blbIdList.map(({name: label, shortId: value}) => ({label, value}));
        },
        [blbIdList]
    );
    const changeBlbId = useCallback(
        (blbId: string) => {
            const {
                name: blbName,
                publicIp: ip,
                eipType,
            } = blbIdList?.find(v => v.shortId === blbId) || {};

            // 若 ip 有值，则 说明所选blb已绑定eip。
            if (ip) {
                form.setFieldsValue({
                    blbId,
                    blbName,
                    elasticPublicNetwork: {
                        enabled: true,
                        type: 'BIND',
                        id: [ip],
                        ip,
                        eipType,
                    },
                });
                setDefaultSelectedRowKeys([ip]);
            }
            else {
                form.setFieldsValue({
                    blbId,
                    blbName,
                    elasticPublicNetwork: initialElasticPublicNetwork,
                });
                setDefaultSelectedRowKeys([]);
            }
        },
        [blbIdList, form]
    );

    const BlbIdSelectLoading = useMemo(
        () => blbIdListLoading || serviceMeshInstanceLoading,
        [blbIdListLoading, serviceMeshInstanceLoading]
    );

    useEffect(
        () => {
            getBlbList();
        },
        [getBlbList]
    );

    Form.useLabelLayout('basic', 0);

    return (
        <Form
            name="basic"
            {...props}
            className={c['form']}
            labelAlign="left"
            initialValues={initialValues}
            form={form}
        >
            <div className={c['blb-id-wrapper']}>
                <Form.Item
                    name={['blbId']}
                    // 沙盒环境可不填写 blb 。
                    rules={IS_QASANDBOX ? undefined : blbIdRules}
                    label={
                        <>
                            BLB实例：{
                                <Popover content={blbIdTip}>
                                    <QuestionCircleOutlined />
                                </Popover>
                            }
                        </>
                    }
                    colon={false}
                >
                    <BlbIdSelect
                        loading={BlbIdSelectLoading}
                        options={options}
                        onChange={changeBlbId}
                    />
                </Form.Item>
                <Form.Item
                    noStyle
                    name={['blbName']}
                />
                <Form.Item
                    shouldUpdate
                    noStyle
                >
                    {
                        () => {
                            const clickRefresh = () => {
                                form.setFieldsValue({
                                    blbId: undefined,
                                    blbName: undefined,
                                    elasticPublicNetwork: initialElasticPublicNetwork,
                                });
                                setDefaultSelectedRowKeys([]);
                                getBlbList();
                            };
                            return (
                                <OutlinedRefresh className={c['refresh']} onClick={clickRefresh} />
                            );
                        }
                    }
                </Form.Item>
                <ExternalLink
                    value={<AddButton className={c['create']} action="create" type="link">新建BLB</AddButton>}
                    href={
                        urls.external.blbCreate.fill(
                            {},
                            {instanceType: 'application'}
                        )
                    }
                />

            </div>

            <Form.Item
                label="⽹络类型"
                name={['networkType']}
            >
                {
                    serviceMeshInstanceLoading
                        ? <Spin />
                        : <NetworkTypeView serviceMeshInstance={serviceMeshInstance} />
                }
            </Form.Item>

            <Form.Item
                label="公⽹访问"
            >
                <ElasticPublicNetwork form={form} defaultSelectedRowKeys={defaultSelectedRowKeys} />
            </Form.Item>
        </Form>
    );
}
