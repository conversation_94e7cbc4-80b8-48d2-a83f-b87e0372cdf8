import {Select} from '@osui/ui';
import {BaseOptionType} from 'antd/lib/select';
import c from './index.less';

interface IProps {
    loading: boolean;
    options: BaseOptionType[];
    onChange: (blbId: string) => void;
}

export default function BlbIdSelect({loading, options, onChange, ...props}: IProps) {
    return (
        <Select
            className={c['select']}
            {...props}
            onChange={onChange}
            placeholder="请选择BLB实例"
            loading={loading}
            options={options}
        />
    );
}
