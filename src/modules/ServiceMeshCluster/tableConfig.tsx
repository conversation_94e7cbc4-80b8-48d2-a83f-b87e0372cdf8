import {ITools, IOperations, IFilters, IRequestConfig} from '@baidu/icloud-ui-pro-table';
import {Link} from 'react-router-dom';
import urls from '@/urls';
import AddButton from '@/components/AddButton';
import BatchDeleteCluster from './BatchDeleteCluster';
import c from './tableConfig.less';

export function operations(
    total: number,
    serviceMeshInstanceId: string, selectedRowKeys: any,
    setSelectedRowKeys: any, isAssociateBLB: boolean): IOperations {
    return [
        {
            render() {
                return (
                    <AddButton action="create" disabled={!isAssociateBLB}>
                        <Link
                            className={isAssociateBLB ? c['link'] : c['link-disabled']}
                            to={urls.cluster.associate.fill({serviceMeshInstanceId})}
                        >
                            添加集群
                        </Link>
                    </AddButton>
                );
            },
        },
        {
            render(props, {refresh}) {
                return (
                    <BatchDeleteCluster
                        serviceMeshInstanceId={serviceMeshInstanceId}
                        selectedRowKeys={selectedRowKeys}
                        setSelectedRowKeys={setSelectedRowKeys}
                        refresh={refresh}
                    />
                );
            },
        },
        {
            render: () => {
                return <span>已选中{selectedRowKeys.length}条，共{total}条</span>;
            },
        },
    ];
}
export const filters: IFilters = [
    {
        name: ['keywordType', 'keyword'],
        renderType: 'groupSearch',
        onFilter: (value, record) => {
            const [field, inputValue] = value || [];
            return !inputValue || (field && record[field].includes(inputValue));
        },
        defaultValue: ['clusterName', ''],
        props: {
            options: [
                {
                    label: '集群名称',
                    value: 'clusterName',
                },
                {
                    label: 'VPC网段',
                    value: 'vpcName',
                },
            ],
        },
    },
];
//  工作区
export const tools: ITools = [
    {
        renderType: 'refresh',
    },
];
export const requestConfig: IRequestConfig = {
    pageByServer: false,
    filterByServer: false,
};
