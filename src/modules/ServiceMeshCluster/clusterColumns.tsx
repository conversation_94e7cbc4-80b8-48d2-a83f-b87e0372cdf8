import {IColumns} from '@baidu/icloud-ui-pro-table';
import StateTag from '@baidu/icloud-ui-state-tag';
import Region from '@/components/Region';
import {dateFormat} from '@/utils';
import {RegionList} from '@/hooks/useFramework';
import {CONNECTION_FILTERS, ClusterStatusDict} from '@/dicts/clusterMesh';
import {CCE_PROJECT_NAME} from '@/dicts';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {K8sVersion} from '@/components/K8sVersion';
import {clusterRowKey} from '@/utils/cluster';
import AloneDeleteCluster from './AloneDeleteCluster';
import ClusterNameLink from './clusterNameLink';

const StatusFilterOptions = Object.entries(ClusterStatusDict)
    .map(([key, value]) => ({value: key, text: value.text}));

// 注：表格筛选是前端做的，所以有筛选功能的列需配置 onFilter 字段。
// 列
export function clusterColumns(
    serviceMeshInstanceId: string, regionList: RegionList, setSelectedRowKeys: any, clusterList: string[]) {
    const columns: IColumns = [
        {
            title: '集群名称',
            dataIndex: 'clusterName',
            width: 200,
            render(value, record) {
                const {region, clusterId, clusterName, isConfig, isPrimary} = record;
                return (
                    <ClusterNameLink
                        region={region}
                        clusterId={clusterId}
                        clusterName={clusterName}
                        isConfig={isConfig}
                        isPrimary={isPrimary}
                    />
                );
            },
        },
        {
            title: '运行状态',
            dataIndex: 'status',
            renderType: 'stateTag',
            filterType: 'single',
            onFilter: (value: string, record) => {
                return value === record?.status;
            },
            filters: StatusFilterOptions,
            render: (value: string) => {
                const statusItem = ClusterStatusDict[value];
                return statusItem ? <StateTag type={statusItem.value}>{statusItem.text}</StateTag> : null;
            },
        },
        {
            title: (
                <div className="flex">
                    <span>K8S版本</span>
                </div>
            ),
            dataIndex: 'version',
            render(value, record: any) {
                return <K8sVersion record={record} />;
            },
        },
        {
            title: '容器网段',
            dataIndex: 'networkSegment',
        },
        {
            title: '地域',
            dataIndex: 'region',
            renderType: 'text',
            filterType: 'single',
            onFilter: 'equals',
            filters: (
                Object.keys(regionList).map(key => {
                    return {text: regionList[key], value: key};
                })
            ),
            render(region) {
                return <Region value={region} projectName={CCE_PROJECT_NAME} />;
            },
        },
        {
            title: '添加时间',
            dataIndex: 'addedTime',
            sorter: (a, b) => new Date(a.addedTime).getTime() - new Date(b.addedTime).getTime(),
            render(addedTime) {
                return <span>{dateFormat(addedTime)}</span>;
            },
        },
        {
            title: 'VPC网段',
            dataIndex: 'vpcName',
            render(value, record) {
                const {vpcId, vpcName} = record;
                return (
                    <ExternalLink
                        href={urls.external.instanceDetail.fill({},
                            {vpcId}
                        )}
                        value={vpcName}
                    />
                );
            },
        },
        {
            title: '连通状态',
            dataIndex: 'connectionState',
            renderType: 'stateTag',
            filterType: 'single',
            onFilter: 'equals',
            filters: CONNECTION_FILTERS,
        },
        {
            title: '操作',
            dataIndex: 'clusterId',
            render(clusterId, {region, status, isConfig, isPrimary}, index, {refresh}) {
                return (
                    <AloneDeleteCluster
                        serviceMeshInstanceId={serviceMeshInstanceId}
                        refresh={refresh}
                        selectedRowKeys={[clusterRowKey({region, clusterId})]} // 怎么感觉怪怪的
                        status={status}
                        isConfig={isConfig}
                        isPrimary={isPrimary}
                        setSelectedRowKeys={setSelectedRowKeys}
                        disClusterRole={clusterList.includes(clusterId)}
                    />
                );
            },
        },
    ];

    return columns;
}
