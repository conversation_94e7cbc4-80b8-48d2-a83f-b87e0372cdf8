import {useCallback, useMemo} from 'react';
import {message, Modal, Button, Tooltip} from '@osui/ui';
import {CLUSTER_UNIQUE_JOIN_CHAR, addOrRemoveClusterByBatch} from '@/utils/cluster';
import {IS_QASANDBOX} from '@/dicts';

interface DeleteClusterProps {
    serviceMeshInstanceId: string;
    selectedRowKeys: string[];
    refresh: () => void;
    status: string;
    isConfig: boolean;
    isPrimary: boolean;
    setSelectedRowKeys: any;
    disClusterRole: boolean;
}

export default function AloneDeleteCluster({
    serviceMeshInstanceId,
    selectedRowKeys,
    refresh,
    status,
    isConfig,
    isPrimary,
    setSelectedRowKeys,
    disClusterRole,
}: DeleteClusterProps) {
    const deleteCluster = useCallback(
        () => {
            const selectedRowKeysStr = selectedRowKeys.map(v => {
                const [, clusterId = ''] = v.split(CLUSTER_UNIQUE_JOIN_CHAR);
                return clusterId;
            }).join(',');
            Modal.confirm({
                title: <span>确定移出集群 &lt; {selectedRowKeysStr} &gt; 吗？</span>,
                content: (
                    <>
                        <div>
                            集群移出后，Sidecar的注入规则将同时被移除。自动注入的服务网格Sidecar在工作负载重新启动前仍然有效，
                            继续使用移出前的规则并转发流量。
                        </div>
                        <div>请确认是否要移除集群 &lt; {selectedRowKeysStr} &gt; ？</div>
                    </>
                ),
                async onOk() {
                    try {
                        await addOrRemoveClusterByBatch(serviceMeshInstanceId, selectedRowKeys, 'remove');
                        // 重刷列表
                        refresh();
                        setSelectedRowKeys([]);
                        message.success(`集群 <${selectedRowKeysStr}> 移出成功。`);
                    } catch (error) {
                        message.error(`集群 <${selectedRowKeysStr}> 移出失败，请重新操作。`);
                    }
                },
                closable: true,
            });
        },
        [refresh, selectedRowKeys, serviceMeshInstanceId, setSelectedRowKeys]
    );
    const textTips = useMemo(
        () => {
            if (isConfig) {
                return 'Istio资源配置在该集群中，无法被移出';
            }
            if (isPrimary) {
                return '主集群不可被移除';
            }
            if (status === 'deleted') {
                return '';
            }
            if (status === 'Removing') {
                return '当前集群移除中';
            }
            // 注：沙盒环境，默认拥有所有的 cce 集群权限（因为沙盒里的cce接口对应不起来）。
            if (!IS_QASANDBOX && !disClusterRole) {
                return '无权限，请到CCE上申请相关的权限';
            }
            return '';
        },
        [status, isPrimary, disClusterRole]
    );

    return (
        <Tooltip title={textTips} placement="left">
            <Button onClick={deleteCluster} disabled={Boolean(textTips)} type="link">
                <span>移出集群</span>
            </Button>
        </Tooltip>
    );
}
