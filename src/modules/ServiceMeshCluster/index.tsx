import React, {useCallback, useMemo} from 'react';
import Card from '@baidu/icloud-ui-card';
import {Alert} from '@osui/ui';
import ProTable from '@baidu/icloud-ui-pro-table';
import styled from 'styled-components';
import {useListSelectedRow, useListTotal} from '@/hooks/useList';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {useFramework} from '@/hooks/useFramework';
import {useCurrentServiceMeshInstance} from '@/modules/ServiceMeshInstance';
import {CCE_PROJECT_NAME, IS_QASANDBOX} from '@/dicts';
import {formatMultiValueQuery} from '@/utils';
import {BlbStatusValue} from '@/dicts/serviceMesh';
import {useCCEClusterListWithPermissionInAllRegion} from '@/hooks/useCCEClusterRole';
import {ERole} from '@/dicts/authorize';
import {clusterRowKey} from '@/utils/cluster';
import AddOrRemoveClusterCheckbox from '@/components/AddOrRemoveClusterCheckbox';
import {getClusterListApi} from '@/api/cluster';
import {useManageClusterTip} from '../AssociateCluster/utils';
import {MAX_SELECTED_NUM} from '../AssociateCluster';
import {clusterColumns} from './clusterColumns';
import {operations as operationsConfig, filters, tools, requestConfig} from './tableConfig';

const AlertStyled = styled(Alert)`
    margin-bottom: 8px;
`;

export default function ServiceMeshCluster() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const clusterList = useCCEClusterListWithPermissionInAllRegion(ERole.CCE_ADMIN);
    const {selectedRowKeys, rowSelection, setSelectedRowKeys} = useListSelectedRow({
        getCheckboxProps: record => {
            const {status, isPrimary, isConfig} = record;
            let disabled = true;
            if (status === 'deleted' || !isConfig) {
                disabled = false;
            } else {
                disabled = (IS_QASANDBOX ? false : !clusterList.includes(record.clusterId))
                    || isPrimary
                    || status === 'Removing';
            }

            return {
                disabled,
            };
        },
        renderCell: (value, record: any, _index, originNode) => {
            return (
                <AddOrRemoveClusterCheckbox
                    type="remove"
                    clusterList={clusterList}
                    record={record}
                    originNode={originNode}
                />
            );
        },
    });
    const {region: {getRegionList}} = useFramework();
    const data = useCurrentServiceMeshInstance();
    const isAssociateBLB = useMemo(
        () => data.BlbInfo && data.BlbInfo.blbStatus === BlbStatusValue.running,
        [data.BlbInfo]
    );

    // 表格配置
    const columns = useMemo(
        () => clusterColumns(serviceMeshInstanceId, getRegionList(CCE_PROJECT_NAME), setSelectedRowKeys, clusterList),
        [serviceMeshInstanceId, getRegionList, clusterList, setSelectedRowKeys]
    );
    const {request, total} = useListTotal(getClusterListApi);
    const getList = useCallback(
        params => {
            const newParams = formatMultiValueQuery(params, ['status', 'region', 'connectionState']);
            return request({
                ...newParams,
                serviceMeshInstanceId,
            });
        },
        [request, serviceMeshInstanceId]
    );
    const operations = operationsConfig(
        total, serviceMeshInstanceId, selectedRowKeys, setSelectedRowKeys, isAssociateBLB);
    const MANEGE_CLUSTER_TIP = useManageClusterTip(total, MAX_SELECTED_NUM - total);

    return (
        <>
            <Card title="集群管理">
                {
                    isAssociateBLB ? (
                        <AlertStyled
                            showIcon
                            message={MANEGE_CLUSTER_TIP}
                            type="info"
                        />
                    ) : (
                        <AlertStyled
                            showIcon
                            message="控制面入口异常，请检查关联的负载均衡BLB状态。"
                            type="warning"
                        />
                    )
                }

                <ProTable
                    rowKey={clusterRowKey}
                    rowSelection={rowSelection}
                    columns={columns}
                    request={getList}
                    operations={operations}
                    filters={filters}
                    tools={tools}
                    requestConfig={requestConfig}
                />
            </Card>
        </>
    );
}
