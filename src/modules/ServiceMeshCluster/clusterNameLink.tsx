import {Tag} from '@osui/ui';
import {useBoolean} from 'huse';
import CopyDataWhenHover from '@/components/CopyDataWhenHover';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';

interface IProps {
    region: string;
    clusterId: string;
    clusterName: string;
    isConfig: boolean;
    isPrimary: boolean;
}
export default function ClusterNameLink({
    region,
    clusterId,
    clusterName,
    isConfig,
    isPrimary,
}: IProps) {
    const [visible, {on, off}] = useBoolean(true);
    return (
        <div className="flex items-center">
            <ExternalLink
                href={urls.external.clusterDetail.fill(
                    {},
                    {clusterUuid: clusterId, clusterName, region}
                )}
                value={
                    <CopyDataWhenHover
                        copyValue={clusterName}
                        type="link"
                        showTag={on}
                        hideTag={off}
                    />
                }
            />
            {isPrimary && <Tag visible={visible} className="margin-left-8" color="blue">主集群</Tag>}
            {isConfig && <Tag visible={visible} className="margin-left-8" color="blue">Istio资源</Tag>}
        </div>
    );
}
