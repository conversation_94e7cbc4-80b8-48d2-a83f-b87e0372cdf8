import {useCallback} from 'react';
import {Button, message, Modal} from '@osui/ui';
import {addOrRemoveClusterByBatch} from '@/utils/cluster';
interface DeleteClusterProps {
    serviceMeshInstanceId: string;
    selectedRowKeys: string[];
    refresh: any;
    setSelectedRowKeys?: any;
}

export default function BatchDeleteCluster(
    {serviceMeshInstanceId, selectedRowKeys, refresh, setSelectedRowKeys}: DeleteClusterProps) {
    const deleteCluster = useCallback(
        () => {
            Modal.confirm({
                title: '确定批量删除集群吗？',
                content: (
                    <>
                        <div>
                            集群移出后，Sidecar的注入规则将同时被移除。自动注入的服务网格Sidecar在工作负载重新启动前仍然有效，
                            继续使用移出前的规则并转发流量。
                        </div>
                        <div>请确定是否要删除 {selectedRowKeys.length} 个集群吗？</div>
                    </>
                ),
                async onOk() {
                    try {
                        await addOrRemoveClusterByBatch(serviceMeshInstanceId, selectedRowKeys, 'remove');
                        // 重刷列表
                        refresh();
                        setSelectedRowKeys([]);
                        message.success(`${selectedRowKeys.length} 个集群移出成功。`);
                    } catch (error) {
                        message.error(`${selectedRowKeys.length} 个集群移出失败，请重新操作。`);
                    }
                },
                closable: true,
            });
        },
        [refresh, serviceMeshInstanceId, setSelectedRowKeys, selectedRowKeys]
    );

    return (
        <Button disabled={selectedRowKeys.length === 0} onClick={deleteCluster}>
            移出集群
        </Button>
    );
}
