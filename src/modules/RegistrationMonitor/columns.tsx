import {IColumns} from '@baidu/icloud-ui-pro-table';
import StateTag from '@baidu/icloud-ui-state-tag';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {StageTagType} from '@/interface';

import {cpromInstanceTemplateMap} from './constant';

interface Status {
    [key: string]: {
        text: string;
        value: StageTagType;
    };
}

export const InstanceStatus: Status = {
    Pending: {text: '等待中', value: 'pending'},
    Creating: {text: '创建中', value: 'pending'},
    Terminating: {text: '删除中', value: 'pending'},
    Failed: {text: '失败', value: 'error'},
    Running: {text: '运行中', value: 'success'},
    Upgrading: {text: '升级中', value: 'pending'},
    Unknown: {text: '未知', value: 'error'},
};

export const columns: IColumns = [
    {
        title: '实例名称',
        dataIndex: 'spec',
        render(value) {
            const {instanceName} = value ?? {};
            return instanceName;
        },
    },
    {
        title: '实例ID',
        dataIndex: 'spec',
        render(value) {
            const {instanceID} = value ?? {};
            return instanceID;
        },
    },
    {
        title: '实例状态',
        dataIndex: 'status',
        render: (value: {phase: string}) => {
            const {phase} = value ?? {};
            const statusItem = InstanceStatus[phase];
            return statusItem ? <StateTag type={statusItem.value}>{statusItem.text}</StateTag> : null;
        },
    },
    {
        title: '实例规格',
        dataIndex: 'metadata',
        render(value) {
            const cpromInstanceTemplate = value?.labels['cprom-instance-template'] ?? '';
            return cpromInstanceTemplateMap.get(cpromInstanceTemplate);
        },
    },
    {
        title: '存储时长',
        dataIndex: 'spec',
        render(value) {
            const {vmClusterConfig: {retentionPeriod}} = value ?? {};
            return `${retentionPeriod}`;
        },
    },
    {
        title: 'Grafana 服务',
        dataIndex: 'monitorGrafanaName',
        render(value, record) {
            const {monitorGrafanaId} = record;
            return (
                <ExternalLink
                    href={urls.external.cprom.grafanaDetail.fill({}, {monitorGrafanaId})}
                    value={value}
                />
            );
        },
    },
];
