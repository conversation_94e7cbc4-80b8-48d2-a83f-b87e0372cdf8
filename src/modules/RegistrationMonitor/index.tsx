import React, {useState, useEffect} from 'react';
import Card from '@baidu/icloud-ui-card';
import ProTable from '@baidu/icloud-ui-pro-table';
import {useRequestCallback} from 'huse';
import {getRegistrationMonitor} from '@/api/registration';
import {useCurrentRegistrationInstance} from '@/modules/RegistrationInstance';

import {columns} from './columns';

export default function RegistrationInstanceMonitor() {
    const [dataSource, setDataSource] = useState([]);
    const instanceData = useCurrentRegistrationInstance();

    const [getList, {data}] = useRequestCallback(getRegistrationMonitor, {pageNo: 1, pageSize: 1000});
    useEffect(
        () => {
            getList();
        },
        [getList]
    );
    useEffect(
        () => {
            if (data?.items?.length) {
                const dataSource = data?.items.filter((element: any) => {
                    const instanceID = element.spec?.instanceID;
                    return instanceData?.monitorInstanceId === instanceID;
                });
                setDataSource(dataSource);
            }
        },
        [data, instanceData]
    );
    return (
        <Card title="Prometheus 监控">
            <ProTable
                columns={columns}
                dataSource={dataSource}
            />
        </Card>
    );
}
