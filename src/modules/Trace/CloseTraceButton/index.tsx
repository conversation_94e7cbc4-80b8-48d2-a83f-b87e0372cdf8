import {Button, Modal, message} from '@osui/ui';
import {useCallback} from 'react';
import {ButtonType} from 'antd/lib/button';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {updateTraceApi} from '@/api/trace';

interface IProps {
    type?: ButtonType;
    getTrace: () => void;
}
export default function CloseTraceButton({
    type = 'default',
    getTrace,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const deleteTrace = useCallback(
        () => {
            Modal.confirm({
                title: '确定关闭链路追踪吗？',
                content: '关闭后将不再为您采集数据面的 tracing 数据，请谨慎操作。',
                async onOk() {
                    try {
                        const rsp = await updateTraceApi({serviceMeshInstanceId, traceEnabled: false});
                        if (rsp) {
                            message.success('关闭链路追踪成功');
                            getTrace();
                        }
                    } catch (error) {
                        message.error('关闭链路追踪失败，请重试');
                        return Promise.reject(new Error('关闭链路追踪失败，请重试'));
                    }
                },
                closable: true,
            });
        },
        [serviceMeshInstanceId, getTrace]
    );

    return (
        <Button
            type={type}
            onClick={deleteTrace}
        >
            关闭服务
        </Button>
    );
}
