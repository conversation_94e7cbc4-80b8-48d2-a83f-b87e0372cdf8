import {Image} from 'antd';
import guidelinePng from '@/assets/cprom/guideline.png';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import OpenTraceButton from '../OpenTraceButton';
import c from './index.less';

interface IProps {
    getTrace: () => void;
}
export default function OpenTraceGuideline({
    getTrace,
}: IProps) {
    return (
        <div className={c['openTraceGuideline']}>
            <Image className={c['guidelinePng']} src={guidelinePng} preview={false} />
            <div className={c['title']}>链路追踪</div>
            <div className={c['description']}>
                链路追踪开启后，CSM 可将网格数据面内产生的 tracing 数据上报到您指定的第三方 Jaeger/Zipkin 服务，帮助您理解服务间的依赖关系、分析链路情况或快速排障。详情参见
                <ExternalLink
                    href={urls.external.csmDosTrace.fill()}
                    value="链路追踪"
                />
            </div>

            <OpenTraceButton type="guideline" getTrace={getTrace} />
        </div>
    );
}
