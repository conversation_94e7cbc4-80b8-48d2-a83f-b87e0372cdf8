import Card from '@baidu/icloud-ui-card';
import {useRequestCallback} from 'huse';
import {Spin} from '@osui/ui';
import {useEffect} from 'react';
import {OutlinedRefresh} from '@baidu/acud-icon';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import ExternalLink from '@/components/ExternalLink';
import {getTraceApi} from '@/api/trace';
import urls from '@/urls';
import {serviceMap} from '../CreateServiceMesh/CreateServiceMeshForm/TraceFormItem/constant';
import OpenTraceGuideline from './OpenTraceGuideline';
import CloseTraceButton from './CloseTraceButton';
import c from './index.less';
import OpenTraceButton from './OpenTraceButton';

export default function Trace() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const [
        getTrace,
        {pending: getTraceLoading, data: getTraceData},
    ] = useRequestCallback(getTraceApi, {serviceMeshInstanceId});

    useEffect(
        () => {
            getTrace();
        },
        [getTrace]
    );

    return (
        getTraceLoading
            ? (
                <div className={c['traceWrapper']}>
                    <Spin />
                </div>
            )
            : getTraceData?.traceEnabled === false
                ? (
                    <div className={c['traceWrapper']}>
                        <OpenTraceGuideline getTrace={getTrace} />
                    </div>
                )
                : (
                    <Card
                        className={c['trace']}
                        title={
                            <div className={c['title']}>
                                <div>链路追踪</div>
                                <div className={c['subtitle']}>
                                    可将请求经过的所有服务链路投递至第三方服务地址，以便您进行链路分析、快速排障，请确认服务地址可达，详情参见
                                    <ExternalLink
                                        href={urls.external.csmDosTrace.fill()}
                                        value="链路追踪"
                                    />。
                                </div>
                            </div>
                        }
                        extra={
                            <div className={c['extra']}>
                                <OutlinedRefresh className={c['refresh']} onClick={getTrace} />
                                <CloseTraceButton getTrace={getTrace} />
                            </div>
                        }
                    >
                        <OpenTraceButton
                            type="update"
                            getTrace={getTrace}
                            initialValues={{traceInfo: getTraceData}}
                        />
                        <Card className={c['configCard']}>
                            <Card.Field title="采样率">
                                {getTraceData?.samplingRate || '--'}
                            </Card.Field>
                            <Card.Field title="使用服务">
                                {serviceMap.get(getTraceData?.service as string) || '--'}
                            </Card.Field>
                            <Card.Field title="服务地址">
                                {getTraceData?.address || '--'}
                            </Card.Field>
                        </Card>
                    </Card>
                )
    );
}
