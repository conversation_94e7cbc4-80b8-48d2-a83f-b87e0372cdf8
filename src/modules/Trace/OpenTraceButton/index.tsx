import {useCallback, useMemo} from 'react';
import {Button, Form, Modal, message} from '@osui/ui';
import {useBoolean} from 'huse';
import {OutlinedEditingSquare} from '@baidu/acud-icon';
import AddButton from '@/components/AddButton';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import TraceFormItem from '@/modules/CreateServiceMesh/CreateServiceMeshForm/TraceFormItem';
import {updateTraceApi} from '@/api/trace';
import {traceInfoDft} from '@/modules/CreateServiceMesh/hook/constant';
import c from './index.less';

interface IProps {
    type: 'guideline' | 'update';
    initialValues?: any;
    getTrace: () => void;
}
export default function OpenTraceButton({
    type = 'guideline',
    initialValues: initialValuesFromProps,
    getTrace,
}: IProps) {
    const [form] = Form.useForm();
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);
    const [confirmLoading, {on: onConfirmLoading, off: offConfirmLoading}] = useBoolean(false);

    const openTrace = useCallback(
        () => {
            onVisible();
        },
        [onVisible]
    );

    const title = useMemo(
        () => (type === 'guideline' ? '开启链路追踪' : '编辑链路追踪'),
        [type]
    );

    const onClickOK = useCallback(
        async () => {
            try {
                const {
                    traceInfo: {
                        traceEnabled = true, samplingRate, service, address,
                    },
                } = await form.validateFields();
                onConfirmLoading();
                const rsp = await updateTraceApi({serviceMeshInstanceId, traceEnabled, samplingRate, service, address});
                if (rsp) {
                    offVisible();
                    message.success(`${title}成功`);
                    getTrace();
                }
                else {
                    message.error(`${title}失败`);
                    return Promise.reject(new Error(`${title}失败`));
                }
            } catch (error) {
                console.error(error);
            } finally {
                offConfirmLoading();
            }
        },
        [form, onConfirmLoading, serviceMeshInstanceId, offVisible, title, getTrace, offConfirmLoading]
    );

    const initialValues = useMemo(
        () => {
            return initialValuesFromProps || {
                traceInfo: {
                    ...traceInfoDft,
                    traceEnabled: true,
                },
            };
        },
        [initialValuesFromProps]
    );

    return (
        <div className={c['openTraceButton']}>
            {
                type === 'guideline'
                    ? (
                        <AddButton
                            type="primary"
                            action="create"
                            icon={null}
                            onClick={openTrace}
                        >
                            立即开启
                        </AddButton>
                    )
                    : (
                        <div className={c['edit-wrapper']}>
                            <div className={c['title']}>链路追踪配置</div>

                            <Button type="link" onClick={openTrace}>
                                <OutlinedEditingSquare className={c['edit-icon']} />
                                编辑
                            </Button>
                        </div>
                    )
            }

            <Modal
                title={title}
                visible={visible}
                confirmLoading={confirmLoading}
                onCancel={offVisible}
                onOk={onClickOK}
            >
                <Form
                    name="basic"
                    labelAlign="left"
                    form={form}
                    initialValues={initialValues}
                >
                    <TraceFormItem type="update" formDataInstante={form} />
                </Form>
            </Modal>
        </div>
    );
}
