import React, {useCallback} from 'react';
import Card from '@baidu/icloud-ui-card';
import ProTable, {IOperations, IFilters} from '@baidu/icloud-ui-pro-table';
import {getRegistrationNamespaceApi} from '@/api/registration';
import {useCurrentRegistrationInstance} from '@/modules/RegistrationInstance';
import {CreateModal} from './modals';

import {getColumns} from './columns';
import c from './index.less';

const filters: IFilters = [
    {
        name: 'name',
        renderType: 'search',
        props: {
            placeholder: '请输入命名空间名称进行搜索',
        },
    },
    {
        renderType: 'refresh',
    },
];


export default function RegistrationInstanceMonitor() {
    const instanceData = useCurrentRegistrationInstance();
    const getList = useCallback(
        params => {
            return getRegistrationNamespaceApi({registrationhInstanceId: instanceData?.id, ...params});
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );
    const operations: IOperations = [
        {
            render(props, {refresh}) {
                return (
                    <CreateModal refresh={refresh} instanceId={instanceData?.id} />
                );
            },
        },
    ];
    return (
        <Card title="命名空间列表" className={c['instance-list']}>
            <ProTable
                columns={getColumns(instanceData?.id)}
                request={getList}
                filters={filters}
                operations={operations}
            />
        </Card>
    );
}
