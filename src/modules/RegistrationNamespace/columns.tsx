import ProTable, {IColumns} from '@baidu/icloud-ui-pro-table';
import {Button, Tooltip} from 'acud';
import {EditModal, deleteConfirm} from './modals';

export const getColumns = (registrationhInstanceId: string): IColumns => {
    return [
        {
            title: '命名空间名称',
            dataIndex: 'name',
            width: 150,
        },
        {
            title: '描述',
            dataIndex: 'comment',
            render(value: string) {
                return value || '-';
            },
            width: 250,
        },
        {
            title: '服务数',
            dataIndex: 'serviceCount',
            width: 100,
            render(value, record) {
                return (
                    value > 0
                        ? (
                            <ProTable.OperationsWrapper>
                                <ProTable.OperationLink
                                    // eslint-disable-next-line max-len
                                    to={`/service-admin-list?instanceId=${registrationhInstanceId}&namespaceName=${record.name}`}
                                >
                                    {value}
                                </ProTable.OperationLink>
                            </ProTable.OperationsWrapper>
                        )
                        : value
                );
            },
        },
        {
            title: '操作',
            dataIndex: 'operation',
            width: 100,
            render(name, record, index, {refresh}) {
                return (
                    <ProTable.OperationsWrapper>
                        <EditModal
                            initialValues={record}
                            instanceId={registrationhInstanceId}
                            refresh={refresh}
                        />
                        <ProTable.Operation>
                            <Tooltip
                                trigger={(record.name === 'default' || record.serviceCount > 0) ? 'hover' : ''}
                                title={record.name === 'default' ? '默认命名空间不支持删除操作' : '当前命名空间仍有服务，您需要先删除服务才能删除该命名空间'}
                            >
                                <Button
                                    type="actiontext"
                                    disabled={record.name === 'default' || record.serviceCount > 0}
                                    onClick={deleteConfirm(record, registrationhInstanceId, refresh)}
                                >
                                    删除
                                </Button>
                            </Tooltip>
                        </ProTable.Operation>
                    </ProTable.OperationsWrapper>
                );
            },
        },
    ];
};
