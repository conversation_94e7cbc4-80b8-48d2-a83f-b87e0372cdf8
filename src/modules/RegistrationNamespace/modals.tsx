import React, {FC, useCallback, useState, useMemo} from 'react';
import {Button, Tooltip, Modal, Form, Input} from 'acud';
import {message} from '@osui/ui';
import {
    createRegistrationNamespaceApi,
    getRegistrationNamespaceApi,
    deleteRegistrationNamespaceApi,
    putRegistrationNamespaceApi,
} from '@/api/registration';

interface EditModalProps {
    initialValues?: Record<string, any>;
    instanceId: string;
    refresh(): void;
}

export const EditModal: FC<EditModalProps> = ({initialValues = {}, instanceId, refresh}) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [formDataInstante] = Form.useForm();

    const showModal = useCallback(
        () => {
            setIsModalVisible(true);
            formDataInstante.setFieldsValue({
                name: initialValues.name,
                comment: initialValues.comment,
            });
        },
        [formDataInstante, initialValues]
    );
    const handleCancel = useCallback(
        () => setIsModalVisible(false),
        []
    );

    const onClickOK = useCallback(
        async (e: React.MouseEvent<HTMLElement>) => {
            e.preventDefault();
            try {
                await formDataInstante.validateFields();
                const initialValues = formDataInstante.getFieldsValue();
                await putRegistrationNamespaceApi({registrationhInstanceId: instanceId, namespaces: [initialValues]});
                refresh();
                handleCancel();
            } catch (error) {
                // console.log(error);
            }
        },
        [formDataInstante, refresh, instanceId, handleCancel]
    );
    return (
        <>
            <Tooltip
                trigger={initialValues.name === 'default' ? 'hover' : ''}
                title={'默认命名空间不支持编辑操作'}
            >
                <Button type="actiontext" disabled={initialValues.name === 'default'} onClick={showModal}>编辑</Button>
            </Tooltip>
            <Modal
                title="编辑命名空间"
                maskClosable={false}
                destroyOnClose
                onOk={onClickOK}
                onCancel={handleCancel}
                visible={isModalVisible}
            >
                <Form
                    name="initialValues"
                    labelAlign="left"
                    form={formDataInstante}
                    labelWidth="100px"
                >
                    <Form.Item
                        label="命名空间名称"
                        name="name"
                        rules={[{required: true, message: ''}]}
                        initialValue={initialValues.name}
                    >
                        <Input disabled />
                    </Form.Item>
                    <Form.Item
                        rules={[{max: 1024, message: '描述长度不能超过1024个字符'}]}
                        initialValue={initialValues.comment}
                        name="comment"
                        label="描述"
                        extra="命名空间描述，不超过1024个字符"
                    >
                        <Input />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

export const deleteConfirm =
    (record: Record<string, any>, registrationhInstanceId: string, refresh: () => void) => () => {
        const onOk = () => {
            return deleteRegistrationNamespaceApi(
                {registrationhInstanceId: registrationhInstanceId, namespaces: record.name}
            ).then(() => {
                message.success('删除成功');
                refresh();
            }).catch(() => message.error('删除失败，请稍后重试'));
        };

        Modal.confirm({
            title: '删除命名空间',
            content: `确定要删除该命名空间${record.name}吗？删除后无法恢复，请谨慎操作`,
            onOk,
        });
    };

interface CreateModalProps {
    instanceId: string;
    refresh(): void;
}

export const CreateModal: FC<CreateModalProps> = ({refresh, instanceId}) => {
    const [nameSource, setNameSource] = useState<any>({});
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [formDataInstante] = Form.useForm();
    const getList = useCallback(
        () => {
            return getRegistrationNamespaceApi({registrationhInstanceId: instanceId, pageNo: 1, pageSize: 10000})
                .then((res: any) => {
                    if (res?.result?.length) {
                        const names = res.result.reduce((acc: any, item: any) => {
                            acc[item.name] = true;
                            return acc;
                        }, {});
                        setNameSource(names);
                    }
                });
        },
        [instanceId]
    );
    const showModal = useCallback(
        () => {
            setIsModalVisible(true);
            getList();
        },
        [getList]
    );
    const handleCancel = useCallback(
        () => {
            setIsModalVisible(false);
            formDataInstante.setFieldsValue({
                name: '',
                comment: '',
            });
        },
        [formDataInstante]
    );
    const onClickOK = useCallback(
        async (e: React.MouseEvent<HTMLElement>) => {
            e.preventDefault();
            try {
                await formDataInstante.validateFields();
                const formData = formDataInstante.getFieldsValue();
                await createRegistrationNamespaceApi({registrationhInstanceId: instanceId, namespaces: [formData]});
                refresh();
                handleCancel();
            } catch (error) {
                // console.log(error);
            }
        },
        [formDataInstante, refresh, instanceId, handleCancel]
    );

    const creatRules = useMemo(
        () => {
            return {
                name: [
                    {
                        required: true,
                        message: '命名空间名称不能为空',
                    },
                    {
                        max: 128,
                        message: '命名空间名称长度不能超过128个字符',
                    },
                    {
                        validator: (rule: any, value: string) => {
                            if (value && !/^[a-zA-Z0-9._-]+$/.test(value)) {
                                return Promise.reject(new Error('命名空间名称不合法'));
                            }
                            if (nameSource[value]) {
                                return Promise.reject(new Error('命名空间名称已存在'));
                            }
                            return Promise.resolve();
                        },
                    },
                ],
                comment: [{
                    max: 1024,
                    message: '描述长度不能超过1024个字符',
                }],
            };
        },
        [nameSource]
    );
    return (
        <>
            <Button type="primary" onClick={showModal}>
                创建命名空间
            </Button>
            <Modal
                title="创建命名空间"
                maskClosable={false}
                onOk={onClickOK}
                onCancel={handleCancel}
                visible={isModalVisible}
            >
                <Form
                    name="formData"
                    labelAlign="left"
                    form={formDataInstante}
                    labelWidth="100px"
                >
                    <Form.Item
                        rules={creatRules.name}
                        label="命名空间名称"
                        name="name"
                        tooltip="命名空间可提供逻辑上的隔离，创建后命名空间的名称不可更改，请谨慎输入。"
                        extra="仅支持英文字母、数字、'.'、'-'、'_',不超过128个字符"
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item rules={creatRules.comment} name="comment" label="描述" extra="命名空间描述，不超过1024个字符">
                        <Input />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};
