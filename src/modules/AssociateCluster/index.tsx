import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {useHistory} from 'react-router-dom';
import ProTable from '@baidu/icloud-ui-pro-table';
import {Alert, Modal, message} from '@osui/ui';
import Card from '@baidu/icloud-ui-card';
import {useBoolean} from 'huse';
import {TableRowSelection} from 'antd/lib/table/interface';
import * as Layout from '@/components/Layout';
import urls from '@/urls';
import {useCurrentServiceMeshIdSafe, useFramework} from '@/hooks';
import {useListTotal} from '@/hooks/useList';
import {
    checkClusterList,
    getCandidateClusterList,
    getClusterListApi,
    ParamsGetCandidateClusterList,
} from '@/api/cluster';
import PageFooter from '@/components/PageFooter';
import {CCE_PROJECT_NAME, IS_QASANDBOX} from '@/dicts';
import {formatMultiValueQuery} from '@/utils';
import {useApiWithRegion} from '@/hooks/useApiWithRegion';
import {clusterRowKey, addOrRemoveClusterByBatch, clusterWidthRegion} from '@/utils/cluster';
import {useCCEClusterListWithPermissionInAllRegion} from '@/hooks/useCCEClusterRole';
import {ERole} from '@/dicts/authorize';
import {Cluster} from '@/interface/cluster';
import AddOrRemoveClusterCheckbox from '@/components/AddOrRemoveClusterCheckbox';
import ExternalLink from '@/components/ExternalLink';
import {getColumns} from './columns';
import {useOperations} from './operations';
import {filters} from './filters';
import {tools} from './tools';
import c from './index.less';
import {useManageClusterTip} from './utils';

const {Header} = Layout;

export const MAX_SELECTED_NUM = 10;

export default function AssociatedCluster() {
    const history = useHistory();
    const clusterList = useCCEClusterListWithPermissionInAllRegion(ERole.CCE_ADMIN);
    const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const {request, total, data} = useListTotal(useApiWithRegion(getCandidateClusterList));
    const getList = useCallback(
        params => {
            const newParams = formatMultiValueQuery(params, ['status', 'masterMode', 'region']);
            return request({
                ...newParams,
                serviceMeshInstanceId,
            });
        },
        [request, serviceMeshInstanceId]
    );

    const goBack = useCallback(
        () => history.push(urls.cluster.list.fill({serviceMeshInstanceId})),
        [history, serviceMeshInstanceId]
    );

    const [loading, {on, off}] = useBoolean(false);
    const addClusterList = useCallback(
        async () => {
            try {
                on();
                await addOrRemoveClusterByBatch(serviceMeshInstanceId, selectedRowKeys, 'add');
                message.success(`${selectedRowKeys.length}个集群添加成功`);
                goBack();
            } catch (error) {
                message.error(`${selectedRowKeys.length}个集群添加失败，请重新操作`);
                return Promise.reject(new Error(''));
            } finally {
                off();
            }
        },
        [goBack, off, on, selectedRowKeys, serviceMeshInstanceId]
    );
    const onSubmit = useCallback(
        async () => {
            on();
            const clusterList = clusterWidthRegion(selectedRowKeys);
            const {result = []} = await checkClusterList({serviceMeshInstanceId, clusters: clusterList});
            if (result.length) {
                Modal.confirm({
                    title: '注意',
                    content: (
                        <>
                            {/* eslint-disable-next-line max-len */}
                            目前集群 {clusterList.filter(v => result.map(v => v.clusterId).includes(v.clusterId)).map(v => v.clusterName).join('、')} 的Node镜像内核未自动开启iptables，Sidecar无法正常工作，需要您手动开启，如已手动开启请忽略。
                            <br />
                            <ExternalLink
                                href={urls.external.sidecarPodFailed.fill()}
                                value="手动开启说明"
                            />
                        </>
                    ),
                    async onOk() {
                        await addClusterList();
                    },
                    closable: true,
                });
            }
            else {
                await addClusterList();
            }
            off();
        },
        [addClusterList, off, on, selectedRowKeys, serviceMeshInstanceId]
    );

    const [selectedNum, setSelectedNum] = useState(0);
    useEffect(
        () => {
            getClusterListApi({serviceMeshInstanceId} as ParamsGetCandidateClusterList).then(res => {
                setSelectedNum(res.totalCount);
            });
        },
        [serviceMeshInstanceId]
    );
    const selectedNumTotal = useMemo(
        () => selectedNum + selectedRowKeys.length,
        [selectedNum, selectedRowKeys.length]
    );
    const currentPageUsableSelectedNum = useMemo(
        () => data.filter(v => v.available),
        [data]
    );

    const hideSelectAll = currentPageUsableSelectedNum.length
        + selectedNum > MAX_SELECTED_NUM;
    // selectedRowKeys.length 这里是为了兼容其他页有已选择的数据
    /**
     * @description 暂时不支持全选
     * 核心是要区分当前页和其他页的selected的数据
     * 大致思路selection改可控,维护一个以pageNo为key,可选择数据的数量为value
     */
    const rowSelection: TableRowSelection<Cluster> = {
        selectedRowKeys,
        preserveSelectedRowKeys: true,
        hideSelectAll,
        onChange: setSelectedRowKeys,
        getCheckboxProps: record => ({
            disabled: (IS_QASANDBOX ? false : !clusterList.includes(record.clusterId))
                || !record.available
                || (selectedNumTotal >= MAX_SELECTED_NUM),
        }),
        renderCell: (value, record, _index, originNode) => {
            return (
                <AddOrRemoveClusterCheckbox
                    type="add"
                    clusterList={clusterList}
                    record={record}
                    originNode={originNode}
                    curSelectedCount={selectedNumTotal}
                    limitSelectedCount={MAX_SELECTED_NUM}
                />
            );
        },
    };
    const operations = useOperations(total, selectedRowKeys);
    const {region: {getRegionList}} = useFramework();
    const regionList = getRegionList(CCE_PROJECT_NAME);
    const myColumns = getColumns(regionList);
    const MANEGE_CLUSTER_TIP = useManageClusterTip(selectedNum, MAX_SELECTED_NUM - selectedNum);
    return (
        <>
            <Header
                backIcon
                title="添加集群"
                onBack={goBack}
            >
                <Alert
                    message={MANEGE_CLUSTER_TIP}
                    type="info"
                    showIcon
                />
            </Header>
            {/* TODO 表格样式调整；筛选功能（接口、UE） */}
            <Card className={c['associate-cluster']} title={null}>
                <ProTable
                    rowKey={clusterRowKey}
                    request={getList}
                    columns={myColumns}
                    operations={operations}
                    filters={filters}
                    tools={tools}
                    rowSelection={rowSelection}
                />
            </Card>

            <PageFooter
                className={c['page-footer']}
                onConfirm={onSubmit}
                onCancel={goBack}
                confirmButton={
                    {disabled: loading || !selectedRowKeys.length}
                }
            />
        </>
    );
}
