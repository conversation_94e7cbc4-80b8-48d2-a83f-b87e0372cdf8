import {IColumns} from '@baidu/icloud-ui-pro-table';
import StateTag from '@baidu/icloud-ui-state-tag';
import urls from '@/urls';
import ExternalLink from '@/components/ExternalLink';
import {MASTER_MODE_FILTERS, ClusterStatusDict} from '@/dicts/clusterMesh';
import {getTextFromValue} from '@/utils';
import Region from '@/components/Region';
import {RegionList} from '@/hooks/useFramework';
import {CCE_PROJECT_NAME} from '@/dicts';
import {K8sVersion} from '@/components/K8sVersion';

const StatusFilterOptions = Object.entries(ClusterStatusDict)
    .map(([key, value]) => ({value: key, text: value.text}));

export function getColumns(regionList: RegionList) {
    const columns: IColumns = [
        {
            title: '集群名称',
            dataIndex: 'clusterName',
            render(value, record) {
                const {clusterId, clusterName, region} = record;

                return (
                    <ExternalLink
                        href={
                            urls.external.clusterDetail.fill(
                                {},
                                {clusterUuid: clusterId, clusterName, region}
                            )
                        }
                        value={clusterName}
                    />
                );
            },
        },
        {
            title: '运行状态',
            dataIndex: 'status',
            filterType: 'single',
            filters: StatusFilterOptions,
            render: (value: string) => {
                const statusItem = ClusterStatusDict[value];
                return statusItem ? <StateTag type={statusItem.value}>{statusItem.text}</StateTag> : null;
            },
        },
        {
            title: () => {
                return (
                    <div className="flex">
                        <span>K8S版本</span>
                    </div>
                );
            },
            dataIndex: 'version',
            render(value, record) {
                return <K8sVersion record={record} />;
            },
        },
        {
            title: '模式',
            dataIndex: 'masterMode',
            render(value) {
                return (
                    <div>{getTextFromValue(MASTER_MODE_FILTERS, value)}</div>
                );
            },
        },
        {
            title: '容器网段',
            dataIndex: 'networkSegment',
        },
        {
            title: '地域',
            dataIndex: 'region',
            filterType: 'single',
            filters: (
                Object.keys(regionList).map(key => {
                    return {text: regionList[key], value: key};
                })
            ),
            render(region) {
                return <Region value={region} projectName={CCE_PROJECT_NAME} />;
            },
        },
        {
            title: 'VPC网段',
            render(value, record) {
                const {vpcId, vpcName} = record;
                return (
                    <ExternalLink
                        href={urls.external.instanceDetail.fill({},
                            {vpcId}
                        )}
                        value={vpcName}
                    />
                );
            },
        },
    ];

    return columns;
}
