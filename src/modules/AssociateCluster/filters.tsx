import {IFilters} from '@baidu/icloud-ui-pro-table';

// 筛选区
export const filters: IFilters = [
    {
        name: ['keywordType', 'keyword'],
        renderType: 'groupSearch',
        onFilter: (value, record) => {
            const [field, inputValue] = value || [];
            return !inputValue || (field && record[field].includes(inputValue));
        },
        defaultValue: ['clusterName', ''],
        props: {
            options: [
                {
                    label: '集群名称',
                    value: 'clusterName',
                },
                {
                    label: 'VPC网段',
                    value: 'vpcName',
                },
            ],
        },
    },
];
