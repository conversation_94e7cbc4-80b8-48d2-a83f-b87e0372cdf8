import React, {useState, useCallback} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, Button as OSUIButton, message} from '@osui/ui';
import {Upload, Button} from 'acud';
import {OutlinedButtonUpload} from 'acud-icon';
import {importCrd, ImportCrdResponse} from '@/api/crd';
// @ts-ignore
import styles from './ImportCrdModal.module.css';
interface ImportCrdModalProps {
    visible: boolean;
    serviceMeshInstanceId: string;
    onCancel: () => void;
    onSuccess: (result: ImportCrdResponse) => void;
    // onFailure: () => void;  // 注释掉未使用的属性
}

export default function ImportCrdModal({
    visible, serviceMeshInstanceId, onCancel, onSuccess,
}: ImportCrdModalProps) {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [actionType, setActionType] = useState<'1' | '2'>('1'); // 1跳过；2覆盖
    const [loading, setLoading] = useState(false);

    // 组件状态日志
    // eslint-disable-next-line no-console
    console.log('[ImportCrdModal] 组件状态:', {
        visible,
        serviceMeshInstanceId,
        selectedFile: selectedFile?.name,
        actionType,
        loading,
        timestamp: new Date().toISOString(),
    });

    // 重置状态
    const resetState = useCallback(
        () => {
            setSelectedFile(null);
            setActionType('1');
            setLoading(false);
        },
        []
    );

    // 处理取消
    const handleCancel = useCallback(
        () => {
            resetState();
            onCancel();
        },
        [
            resetState,
            onCancel,
        ]
    );

    // 处理文件选择
    const handleFileChange = useCallback(
        (info: any) => {
            const {file} = info;
            // eslint-disable-next-line no-console
            console.log('[ImportCrdModal] 文件选择事件:', {
                fileName: file.name,
                fileType: file.type,
                fileSize: file.size,
                timestamp: new Date().toISOString(),
            });

            if (file.status === 'uploading') {
                return;
            }

            // 校验文件扩展名
            const isValidExt = ['.yaml', '.yml'].some(ext => file.name.endsWith(ext));
            const isValidType = ['application/x-yaml', 'text/yaml'].includes(file.type);

            if (!isValidExt && !isValidType) {
                Modal.error({
                    title: '文件格式错误',
                    content: '仅支持上传 YAML 格式文件',
                    okText: '确定',
                });
                setSelectedFile(null);
                return;
            }

            // 校验文件内容
            const reader = new FileReader();
            reader.onload = async e => {
                const content = e.target?.result as string;
                if (!content.trim()) {
                    Modal.error({
                        title: '文件内容为空',
                        content: '请检查导入的数据文件',
                        okText: '确定',
                    });
                    setSelectedFile(null);
                    return;
                }

                try {
                    // 基本YAML格式验证
                    if (!content.includes('kind:') || !content.includes('metadata:')) {
                        throw new Error('Invalid YAML structure');
                    }

                    // 验证通过，设置选中文件
                    setSelectedFile(file);
                    message.success(`已选择文件：${file.name}`);
                    // eslint-disable-next-line no-console
                    console.log('[ImportCrdModal] 文件选择成功:', {
                        fileName: file.name,
                        fileSize: `${(file.size / 1024).toFixed(2)} KB`,
                        isValidYaml: true,
                    });
                } catch (e) {
                    Modal.error({
                        title: '文件格式错误',
                        content: '请检查YAML格式是否正确',
                        okText: '确定',
                    });
                    setSelectedFile(null);
                }
            };
            reader.readAsText(file);
        },
        []
    );

    const beforeUpload = useCallback(
        () => false,
        []
    );

    // 处理确定导入
    const handleImport = useCallback(
        async () => {
            if (!selectedFile) {
                message.error('请先选择要导入的 YAML 文件');
                // eslint-disable-next-line no-console
                console.log('[ImportCrdModal] 导入被阻止 - 未选择文件');
                throw new Error('未选择文件');
            }

            // eslint-disable-next-line no-console
            console.log('[ImportCrdModal] 开始导入流程:', {
                fileName: selectedFile.name,
                fileSize: `${(selectedFile.size / 1024).toFixed(2)} KB`,
                fileType: selectedFile.type,
                actionType,
                serviceMeshInstanceId,
                timestamp: new Date().toISOString(),
            });

            setLoading(true);
            try {
                // 确保文件已通过校验
                if (!selectedFile) {
                    throw new Error('No file selected');
                }

                const result = await importCrd({
                    serviceMeshInstanceId,
                    file: selectedFile,
                    actionType,
                });

                // eslint-disable-next-line no-console
                console.log('[ImportCrdModal] 导入API调用结果:', result);

                if (result?.success === false) {
                    // 构造标准错误响应结构
                    const errorResponse = {
                        success: false,
                        result: {
                            failureCount: 1,
                            failureList: [{
                                error: result?.message?.global || 'Invalid parameters',
                            }],
                        },
                    };
                    return errorResponse;
                }

                resetState();
                onSuccess(result);
            } catch (error) {
                console.error('[ImportCrdModal] 导入失败:', error);
                setSelectedFile(null);
                setLoading(false); // 重置加载状态
                // 构造标准错误响应结构
                const errorResponse = {
                    success: false,
                    result: {
                        failureCount: 1,
                        failureList: [{
                            error: error instanceof Error ? error.message : 'Unknown error',
                        }],
                    },
                };
                onSuccess(errorResponse); // 通过onSuccess传递错误结构
            } finally {
                setLoading(false); // 确保无论如何都会重置加载状态
            }
        },
        [
            selectedFile,
            actionType,
            serviceMeshInstanceId,
            resetState,
            onSuccess,
        ]
    );

    return (
        <Modal
            title="导入"
            open={visible}
            onCancel={handleCancel}
            width={800}
            destroyOnClose
            transitionName=""
            maskTransitionName=""
            footer={
                <div className={styles.footer}>
                    <OSUIButton onClick={handleCancel}>取消</OSUIButton>
                    <OSUIButton
                        type="primary"
                        onClick={handleImport}
                        loading={loading}
                    >
                        确定
                    </OSUIButton>
                </div>
            }
        >
            <div className={styles.container}>
                {/* 提示信息 */}
                <Alert
                    type="info"
                    showIcon
                    message={
                        <div>
                            {'您可以将其他服务网格实例中的 Istio 资源导入到当前网格实例中，'
                            + '实现流量策略的快速迁移。如果 Istio 资源配置存储在数据面集群中，'
                            + '且该集群中不存在相关命名空间，我们将为您自动创建。'}
                        </div>
                    }
                    style={{
                        marginBottom: 24,
                        display: 'flex',
                        alignItems: 'flex-start',
                    }}
                />

                {/* 表单区域 - 垂直布局 */}
                <div className={styles.formContainer}>
                    {/* 上传文件行 */}
                    <div className={styles.uploadSection}>
                        <div className={styles.label}>
                            <span className={styles.required}>*</span>
                            <span>上传文件：</span>
                        </div>

                        <div className={styles.uploadContainer}>
                            <Upload
                                name="file"
                                beforeUpload={beforeUpload}
                                onChange={handleFileChange}
                                accept=".yaml,.yml"
                                multiple={false}
                                showUploadList={false}
                                description="仅支持上传 yaml 文件"
                            >
                                <Button
                                    icon={<OutlinedButtonUpload />}
                                    className={styles.upload}
                                >
                                    上传文件
                                </Button>
                            </Upload>

                            {selectedFile && (
                                <div
                                    className={styles.selectedFile}
                                    style={{
                                        width: '100%',
                                        whiteSpace: 'nowrap',
                                        overflow: 'hidden',
                                        textOverflow: 'clip',
                                    }}
                                >
                                    已选择文件：{selectedFile.name}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* 冲突处理行 */}
                    <div className={styles.conflictSection}>
                        <div className={styles.label}>
                            <span className={styles.required}>*</span>
                            <span>冲突处理：</span>
                        </div>

                        <div className={styles.radioContainer}>
                            <div
                                className={`${styles.radioButton} ${styles.radioLeft} ${
                                    actionType === '1' ? styles.selected : ''
                                }`}
                                onClick={() => setActionType('1')}
                            >
                                跳过
                            </div>
                            <div
                                className={`${styles.radioButton} ${styles.radioRight} ${
                                    actionType === '2' ? styles.selected : ''
                                }`}
                                onClick={() => setActionType('2')}
                            >
                                覆盖
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Modal>
    );
}
