import React, {useState, useCallback} from 'react';
import {Modal, Button, Upload, Radio, toast} from 'acud';
import {OutlinedButtonUpload, OutlinedInfoCircle} from 'acud-icon';
import {importCrd, ImportCrdResponse} from '@/api/crd';
interface ImportCrdModalProps {
    visible: boolean;
    serviceMeshInstanceId: string;
    onCancel: () => void;
    onSuccess: (result: ImportCrdResponse) => void;
    // onFailure: () => void;  // 注释掉未使用的属性
}

export default function ImportCrdModal({
    visible, serviceMeshInstanceId, onCancel, onSuccess,
}: ImportCrdModalProps) {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [actionType, setActionType] = useState<'1' | '2'>('1'); // 1跳过；2覆盖
    const [loading, setLoading] = useState(false);

    // 组件状态日志
    // eslint-disable-next-line no-console
    console.log('[ImportCrdModal] 组件状态:', {
        visible,
        serviceMeshInstanceId,
        selectedFile: selectedFile?.name,
        actionType,
        loading,
        timestamp: new Date().toISOString(),
    });

    // 重置状态
    const resetState = useCallback(
        () => {
            setSelectedFile(null);
            setActionType('1');
            setLoading(false);
        },
        []
    );

    // 处理取消
    const handleCancel = useCallback(
        () => {
            resetState();
            onCancel();
        },
        [
            resetState,
            onCancel,
        ]
    );

    // 处理文件选择
    const handleFileChange = useCallback(
        (info: any) => {
            const {file} = info;
            // eslint-disable-next-line no-console
            console.log('[ImportCrdModal] 文件选择事件:', {
                fileName: file.name,
                fileType: file.type,
                fileSize: file.size,
                timestamp: new Date().toISOString(),
            });

            if (file.status === 'uploading') {
                return;
            }

            // 校验文件扩展名
            const isValidExt = ['.yaml', '.yml'].some(ext => file.name.endsWith(ext));
            const isValidType = ['application/x-yaml', 'text/yaml'].includes(file.type);

            if (!isValidExt && !isValidType) {
                toast.error({
                    message: '文件格式错误',
                    description: '仅支持上传 YAML 格式文件',
                });
                setSelectedFile(null);
                return;
            }

            // 校验文件内容
            const reader = new FileReader();
            reader.onload = async e => {
                const content = e.target?.result as string;
                if (!content.trim()) {
                    toast.error({
                        message: '文件内容为空',
                        description: '请检查导入的数据文件',
                    });
                    setSelectedFile(null);
                    return;
                }

                try {
                    // 基本YAML格式验证
                    if (!content.includes('kind:') || !content.includes('metadata:')) {
                        throw new Error('Invalid YAML structure');
                    }

                    // 验证通过，设置选中文件
                    setSelectedFile(file);
                    toast.success({
                        message: '文件选择成功',
                        description: `已选择文件：${file.name}`,
                    });
                    // eslint-disable-next-line no-console
                    console.log('[ImportCrdModal] 文件选择成功:', {
                        fileName: file.name,
                        fileSize: `${(file.size / 1024).toFixed(2)} KB`,
                        isValidYaml: true,
                    });
                } catch (e) {
                    toast.error({
                        message: '文件格式错误',
                        description: '请检查YAML格式是否正确',
                    });
                    setSelectedFile(null);
                }
            };
            reader.readAsText(file);
        },
        []
    );

    const beforeUpload = useCallback(
        () => false,
        []
    );

    // 处理冲突处理选项变化
    const handleActionTypeChange = useCallback(
        (e: Event) => {
            setActionType((e.target as HTMLInputElement).value as '1' | '2');
        },
        []
    );

    // 处理确定导入
    const handleImport = useCallback(
        async () => {
            if (!selectedFile) {
                toast.error({
                    message: '请选择文件',
                    description: '请先选择要导入的 YAML 文件',
                });
                // eslint-disable-next-line no-console
                console.log('[ImportCrdModal] 导入被阻止 - 未选择文件');
                throw new Error('未选择文件');
            }

            // eslint-disable-next-line no-console
            console.log('[ImportCrdModal] 开始导入流程:', {
                fileName: selectedFile.name,
                fileSize: `${(selectedFile.size / 1024).toFixed(2)} KB`,
                fileType: selectedFile.type,
                actionType,
                serviceMeshInstanceId,
                timestamp: new Date().toISOString(),
            });

            setLoading(true);
            try {
                // 确保文件已通过校验
                if (!selectedFile) {
                    throw new Error('No file selected');
                }

                const result = await importCrd({
                    serviceMeshInstanceId,
                    file: selectedFile,
                    actionType,
                });

                // eslint-disable-next-line no-console
                console.log('[ImportCrdModal] 导入API调用结果:', result);

                resetState();
                onSuccess(result);
            } catch (error) {
                console.error('[ImportCrdModal] 导入失败:', error);
                setSelectedFile(null);
                setLoading(false); // 重置加载状态

                // 构造标准错误响应结构
                const errorResponse: ImportCrdResponse = {
                    totalCount: 0,
                    successCount: 0,
                    updateCount: 0,
                    failureCount: 1,
                    skippedCount: 0,
                    successList: [],
                    updateList: [],
                    skippedList: [],
                    failureList: [{
                        kind: '',
                        name: '',
                        namespace: '',
                        operation: 'create',
                        error: error instanceof Error ? error.message : 'Unknown error',
                    }],
                };
                onSuccess(errorResponse); // 通过onSuccess传递错误结构
            } finally {
                setLoading(false); // 确保无论如何都会重置加载状态
            }
        },
        [
            selectedFile,
            actionType,
            serviceMeshInstanceId,
            resetState,
            onSuccess,
        ]
    );

    return (
        <Modal
            title="导入"
            visible={visible}
            onCancel={handleCancel}
            width={800}
            destroyOnClose
            footer={[
                <Button key="cancel" onClick={handleCancel}>
                    取消
                </Button>,
                <Button
                    key="submit"
                    type="primary"
                    onClick={handleImport}
                    loading={loading}
                >
                    确定
                </Button>,
            ]}
        >
            <div style={{padding: 0}}>
                {/* 提示信息 */}
                <div style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: '12px',
                    padding: '4px 16px',
                    marginBottom: '24px',
                    backgroundColor: '#E6F0FF',
                    borderRadius: '4px',
                }}
                >
                    <OutlinedInfoCircle style={{
                        color: '#2468F2',
                        fontSize: '16px',
                        marginTop: '2px',
                        flexShrink: 0,
                    }}
                    />
                    <div style={{
                        fontSize: '12px',
                        lineHeight: '20px',
                        color: '#151B26',
                    }}
                    >
                        您可以将其他服务网格实例中的 Istio 资源导入到当前网格实例中，实现流量策略的快速迁移。如果 Istio 资源配置存储在数据面集群中，且该集群中不存在相关命名空间，我们将为您自动创建。
                    </div>
                </div>

                {/* 表单区域 */}
                <div style={{display: 'flex', flexDirection: 'column', gap: '24px'}}>
                    {/* 上传文件行 */}
                    <div style={{display: 'flex', alignItems: 'flex-start', gap: '16px'}}>
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            fontSize: '12px',
                            color: '#5C5F66',
                            minWidth: 'fit-content',
                            paddingTop: '6px',
                        }}
                        >
                            <span style={{color: '#F33D3D', marginRight: '2px'}}>*</span>
                            <span>上传文件：</span>
                        </div>

                        <div style={{display: 'flex', flexDirection: 'column', gap: '4px', width: '100%'}}>
                            <Upload
                                name="file"
                                beforeUpload={beforeUpload}
                                onChange={handleFileChange}
                                accept=".yaml,.yml"
                                multiple={false}
                                showUploadList={false}
                            >
                                <Button
                                    icon={<OutlinedButtonUpload />}
                                    style={{
                                        width: '92px',
                                        height: '32px',
                                    }}
                                >
                                    上传文件
                                </Button>
                            </Upload>

                            <div style={{
                                fontSize: '12px',
                                color: '#84868C',
                                textAlign: 'center',
                            }}
                            >
                                仅支持上传 yaml 文件
                            </div>

                            {selectedFile && (
                                <div style={{
                                    fontSize: '12px',
                                    color: '#52C41A',
                                    marginTop: '8px',
                                }}
                                >
                                    已选择文件：{selectedFile.name}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* 冲突处理行 */}
                    <div style={{display: 'flex', alignItems: 'flex-start', gap: '16px'}}>
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            fontSize: '12px',
                            color: '#5C5F66',
                            minWidth: 'fit-content',
                            paddingTop: '6px',
                        }}
                        >
                            <span style={{color: '#F33D3D', marginRight: '2px'}}>*</span>
                            <span>冲突处理：</span>
                        </div>

                        <Radio.Group
                            value={actionType}
                            onChange={handleActionTypeChange}
                            options={[
                                {label: '跳过', value: '1'},
                                {label: '覆盖', value: '2'},
                            ]}
                            optionType="button"
                        />
                    </div>
                </div>
            </div>
        </Modal>
    );
}
