.container {
    padding: 0;
}

/* 表单容器 - 垂直布局 */
.formContainer {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* 上传文件区域 - 单行布局 */
.uploadSection {
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

/* 标签样式 */
.label {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 400;
    line-height: 1.4;
    color: #5c5f66;
    min-width: fit-content;
    padding-top: 6px; /* 与上传按钮顶部对齐 */
}

.required {
    color: #f33d3d;
    margin-right: 2px;
}

/* 上传容器 - 纵向布局，包含按钮和提示文字 */
.uploadContainer {
    display: flex;
    flex-direction: column;
    gap: 4px; /* 按钮和提示文字间距 */
    width: 100%; /* 增加宽度以适应文件名显示 */
    min-height: 56px; /* 最小高度，允许动态扩展 */
    transform: rotate(0deg); /* 角度 */
    opacity: 1; /* 透明度 */
}

/* 上传按钮 - 固定尺寸 */
.upload {
    width: 92px !important; /* 固定宽度 */
    height: 32px !important; /* 固定高度 */
    border-radius: 4px !important; /* regular-radius */
    transform: rotate(0deg); /* 角度 */
    opacity: 1; /* 透明度 */
    padding: 6px 12px !important;
    border-width: 1px !important;
    border-style: solid !important;
    border-color: #d4d6d9 !important;
    background: #fff !important;
    cursor: pointer;
    transition: border-color .3s;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 4px !important; /* 减小图标和文字间距 */
}

.upload:hover {
    border-color: #2468f2 !important;
}

/* 上传文件图标区域（图标+文字） */
.uploadContent {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 6px; /* 图标和文字间距 */
}

.uploadIcon {
    font-size: 14px;
    color: #151b26;
    width: 16px;
    height: 16px;
    flex-shrink: 0; /* 防止图标被压缩 */
}

.uploadText {
    font-size: 14px;
    font-weight: 400;
    color: #151b26;
    line-height: 1.4;
    white-space: nowrap;
    flex-shrink: 0; /* 防止文字被压缩 */
}

/* 上传提示文字 */
.uploadHint {
    font-size: 12px;
    font-weight: 400;
    color: #84868c;
    line-height: 1.6666666666666667;
    text-align: center; /* 居中显示 */
}

/* 冲突处理区域 - 单行布局 */
.conflictSection {
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

.conflictSection .label {
    padding-top: 6px; /* 与单选按钮顶部对齐 */
}

.radioContainer {
    display: flex;
    align-items: center;
    gap: -1px;
}

.radioButton {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 6px 24px;
    height: 32px;
    font-size: 12px;
    font-weight: 400;
    line-height: 1.6666666666666667;
    text-align: center;
    cursor: pointer;
    border: 1px solid #d4d6d9;
    background: #fff;
    color: #151b26;
    transition: all .3s;
}

.radioLeft {
    border-radius: 4px 0 0 4px;
}

.radioRight {
    border-radius: 0 4px 4px 0;
    border-left: 0;
}

.radioButton.selected {
    background: #e6f0ff;
    border-color: #2468f2;
    color: #2468f2;
}

.selectedFile {
    font-size: 12px;
    color: #52c41a;
    margin-top: 8px;
    white-space: nowrap; /* 确保显示为一行 */
    line-height: 1.4;
}

.footer {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
}
