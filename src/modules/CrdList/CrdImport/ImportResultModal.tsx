import React, {useCallback} from 'react';
import {ExclamationCircleOutlined} from '@ant-design/icons';
import {Modal, Button, Table} from '@osui/ui';
import {ImportCrdResponse, ImportResourceItem} from '@/api/crd';
// @ts-ignore
import styles from './ImportResultModal.module.css';

interface ImportResultModalProps {
    visible: boolean;
    result: ImportCrdResponse | null;
    actionType: '1' | '2'; // 1跳过；2覆盖
    onCancel: () => void;
}

// eslint-disable-next-line complexity
export default function ImportResultModal({
    visible, result, actionType, onCancel,
}: ImportResultModalProps) {
    // eslint-disable-next-line no-console
    console.log('[ImportResultModal] 组件渲染:', {visible, actionType, result: !!result});

    // 表格行key生成函数 - Hook必须在条件判断之前调用
    const getRowKey = useCallback(
        (record: ImportResourceItem) => {
            const key = `${record.namespace}-${record.kind}-${record.name}`;
            // eslint-disable-next-line no-console
            console.log('[ImportResultModal] 生成行key:', key, record);
            return key;
        },
        []
    );

    if (!result || result.success === false) {
        // eslint-disable-next-line no-console
        console.log('[ImportResultModal] 无有效导入结果数据 - 组件返回失败弹窗');
        const errorMsg = '未读取到合法数据，请检查导入的数据文件。';
        return (
            <Modal
                title={
                    <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        paddingTop: '16px',
                        marginLeft: 10,
                        marginBottom: 16,
                    }}
                    >
                        <ExclamationCircleOutlined
                            style={{
                                color: 'white',
                                backgroundColor: '#fa8c16',
                                fontSize: 24,
                                borderRadius: '50%',
                                marginRight: 8,
                            }}
                        />
                        <span style={{fontWeight: 500}}>导入失败</span>
                    </div>
                }
                open={visible}
                onCancel={onCancel}
                width={400}
                destroyOnClose
                bodyStyle={{paddingTop: '8px'}}
                footer={
                    <div style={{textAlign: 'right'}}>
                        <Button
                            type="primary"
                            onClick={onCancel}
                            style={{border: 'none'}}
                        >
                            确定
                        </Button>
                    </div>
                }
            >
                <div style={{
                    padding: '0 24px 16px',
                    fontSize: '14px',
                    color: '#000000',
                    lineHeight: '22px',
                    textAlign: 'center',
                }}
                >
                    {errorMsg}
                </div>
            </Modal>
        );
    }

    const {
        successCount,
        updateCount,
        failureCount,
        skippedCount,
        successList,
        updateList,
        skippedList,
        failureList,
    } = result;

    // eslint-disable-next-line no-console
    console.log('[ImportResultModal] 导入结果统计:', {
        actionType,
        counts: {successCount, updateCount, failureCount, skippedCount},
        lists: {
            successList: successList?.length || 0,
            updateList: updateList?.length || 0,
            skippedList: skippedList?.length || 0,
            failureList: failureList?.length || 0,
        },
        actualData: {successList, updateList, skippedList, failureList},
    });

    // 表格列配置 - 只有3列：名称、命名空间、类型
    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            width: '25%',
        },
        {
            title: '命名空间',
            dataIndex: 'namespace',
            key: 'namespace',
            width: '50%',
        },
        {
            title: '类型',
            dataIndex: 'kind',
            key: 'kind',
            width: '25%',
        },
    ];

    // 失败表格列配置
    const failureColumns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            width: '20%',
        },
        {
            title: '命名空间',
            dataIndex: 'namespace',
            key: 'namespace',
            width: '30%',
        },
        {
            title: '类型',
            dataIndex: 'kind',
            key: 'kind',
            width: '20%',
        },
        {
            title: '错误信息',
            dataIndex: 'error',
            key: 'error',
            width: '30%',
        },
    ];

    // 判断是否有失败的情况
    const hasFailures = failureCount > 0;
    const hasSuccessResults = successCount > 0 || updateCount > 0 || skippedCount > 0;

    // 如果全部失败，显示失败弹窗
    if (hasFailures && !hasSuccessResults) {
        return (
            <Modal
                title={
                    <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        paddingTop: '16px',
                        marginLeft: 10,
                    }}
                    >
                        <ExclamationCircleOutlined
                            style={{
                                color: 'white',
                                backgroundColor: '#fa8c16',
                                fontSize: 24,
                                borderRadius: '50%',
                                marginRight: 8,
                                strokeWidth: 0,
                            }}
                        />
                        <span style={{fontWeight: 500}}>导入失败</span>
                    </div>
                }
                open={visible}
                onCancel={onCancel}
                width={400}
                destroyOnClose
                transitionName="no-animation"
                maskTransitionName="no-animation"
                getContainer={false}
                bodyStyle={{paddingTop: '8px'}}
                footer={(
                    <div style={{textAlign: 'right'}}>
                        <Button
                            type="primary"
                            onClick={onCancel}
                            style={{border: 'none'}}
                        >
                            确定
                        </Button>
                    </div>
                )}
            >
                <div style={{
                    padding: '0 24px 16px',
                    fontSize: '14px',
                    color: '#000000',
                    lineHeight: '22px',
                    textAlign: 'center',
                }}
                >
                    未读取到合法数据，请检查导入的数据文件。
                </div>
            </Modal>
        );
    }

    return (
        <Modal
            title="导入完成"
            open={visible}
            onCancel={onCancel}
            width={800}
            destroyOnClose
            transitionName="no-animation" // 使用空动画
            maskTransitionName="no-animation"
            getContainer={false} // 避免portal动画
            footer={
                <div style={{textAlign: 'right'}}>
                    <Button
                        type="primary"
                        onClick={onCancel}
                    >
                        确定
                    </Button>
                </div>
            }
        >
            <div className={styles.container}>
                {/* 成功导入的资源 */}
                {successCount > 0 && (
                    <div className={styles.section}>
                        <div className={styles.sectionTitle}>
                            成功导入 Istio 资源数量：{successCount}
                        </div>
                        <Table
                            columns={columns}
                            dataSource={successList}
                            pagination={false}
                            size="small"
                            rowKey={getRowKey}
                            className={styles.table}
                        />
                    </div>
                )}

                {/* 覆盖的资源（当选择覆盖时） */}
                {updateCount > 0 && (
                    <div className={styles.section}>
                        <div className={styles.sectionTitle}>
                            覆盖 Istio 资源数量：{updateCount}
                        </div>
                        <Table
                            columns={columns}
                            dataSource={updateList}
                            pagination={false}
                            size="small"
                            rowKey={getRowKey}
                            className={styles.table}
                        />
                    </div>
                )}

                {/* 跳过的资源（当选择跳过时） */}
                {actionType === '1' && skippedCount > 0 && (
                    <div className={styles.section}>
                        <div className={styles.sectionTitle}>
                            跳过 Istio 资源数量：{skippedCount}
                        </div>
                        <Table
                            columns={columns}
                            dataSource={skippedList}
                            pagination={false}
                            size="small"
                            rowKey={getRowKey}
                            className={styles.table}
                        />
                    </div>
                )}

                {/* 失败的资源 */}
                {hasFailures && (
                    <div className={styles.section}>
                        <div className={styles.sectionTitle}>
                            失败 Istio 资源数量：{failureCount}
                        </div>
                        <Table
                            columns={failureColumns}
                            dataSource={failureList}
                            pagination={false}
                            size="small"
                            rowKey={getRowKey}
                            className={styles.table}
                        />
                    </div>
                )}
            </div>
        </Modal>
    );
}
