import React, {cloneElement, ReactElement, useCallback, useState} from 'react';
import ProTable from '@baidu/icloud-ui-pro-table';
import {Button, message, Modal, Tooltip} from '@osui/ui';
import Card from '@baidu/icloud-ui-card';
import {getCrdListByInstanceId, deleteCrd, ImportCrdResponse, ParamsDeleteCrd} from '@/api/crd';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {useCurrentServiceMeshInstance} from '@/modules/ServiceMeshInstance';
import {isHostingMesh} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/util';
import {useListSelectedRow, useListTotal} from '@/hooks/useList';
import {Crd} from '@/interface';
import {getCrdIdByRecord, getDisabledReasonByCrd, getRecordByCrdId} from './utils';
import {filters, tools, useTableColumns, useOperations} from './tableConfig';
import ImportCrdModal from './CrdImport/ImportCrdModal';
import ImportResultModal from './CrdImport/ImportResultModal';

// @ts-ignore
export function DeleteBatchCrd({serviceMeshInstanceId, selectedRowKeys, refresh, setSelectedRowKeys}) {
    const deleteBatchCrd = useCallback(
        () => {
            const selectedCrdList = selectedRowKeys.map((v: string) => getRecordByCrdId(v));
            Modal.confirm({
                title: '确定批量删除Istio资源吗？',
                content: <>Istio资源定义了服务网格转发流量的规则，Istio资源删除后无法恢复。<br />请确定是否要删除 {selectedCrdList.length}个 Istio资源？</>,
                async onOk() {
                    try {
                        // TODO 类型
                        await deleteCrd({serviceMeshInstanceId, crdList: selectedCrdList});
                        setSelectedRowKeys([]);
                        // 重刷列表
                        refresh();
                        message.success(`${selectedRowKeys.length}个Istio资源删除成功。`);
                    } catch (error) {
                        message.error(`${selectedRowKeys.length}个Istio资源删除失败，请重新操作。`);
                    }
                },
                closable: true,
            });
        },
        [refresh, selectedRowKeys, serviceMeshInstanceId, setSelectedRowKeys]
    );

    return (
        <Button
            disabled={selectedRowKeys.length === 0}
            onClick={deleteBatchCrd}
        >
            删除
        </Button>
    );
}

// 导入按钮组件
// @ts-ignore
export function ImportCrdButton({serviceMeshInstanceId, refresh}) {
    const [importModalVisible, setImportModalVisible] = useState(false);
    const [resultModalVisible, setResultModalVisible] = useState(false);
    const [importResult, setImportResult] = useState<ImportCrdResponse | null>(null);

    // 打开导入弹窗
    const handleOpenImportModal = useCallback(
        () => {
            setImportModalVisible(true);
        },
        []
    );

    // 关闭导入弹窗
    const handleCloseImportModal = useCallback(
        () => {
            setImportModalVisible(false);
        },
        []
    );

    // 导入成功处理
    const handleImportSuccess = useCallback(
        (result: ImportCrdResponse) => {
            setImportResult(result);
            setImportModalVisible(false);
            setResultModalVisible(true);
            refresh(); // 刷新资源列表
        },
        [refresh]
    );

    // 关闭结果弹窗
    const handleCloseResultModal = useCallback(
        () => {
            setResultModalVisible(false);
            setImportResult(null);
        },
        []
    );

    return (
        <>
            <Button
                onClick={handleOpenImportModal}
            >
                导入
            </Button>

            <ImportCrdModal
                visible={importModalVisible}
                serviceMeshInstanceId={serviceMeshInstanceId}
                onCancel={handleCloseImportModal}
                onSuccess={handleImportSuccess}
            />

            <ImportResultModal
                visible={resultModalVisible}
                result={importResult}
                actionType="1"
                onCancel={handleCloseResultModal}
            />
        </>
    );
}

interface IProps {
    serviceMeshInstanceId: string;
    record: Crd;
    refresh: () => void;
}
export function DeleteOneCrd({serviceMeshInstanceId, record, refresh}: IProps) {
    const deleteOneCrd = useCallback(
        () => {
            const {namespace, kind, name} = record;
            const namespaceTip = namespace === '*' ? '所有' : namespace;
            Modal.confirm({
                title: '确定删除Istio资源吗？',
                content: <>Istio资源定义了服务网格转发流量的规则，Istio资源删除后无法恢复。<br />请确定是否要删除{namespaceTip}命名空间下的 &lt;{name}&gt; ？</>,
                async onOk() {
                    try {
                        await deleteCrd({
                            serviceMeshInstanceId,
                            crdList: [{namespace, kind, name}],
                        } as ParamsDeleteCrd);
                        // 重刷列表
                        refresh();
                        message.success(`${namespaceTip}命名空间下的 <${name}> 删除成功。`);
                    } catch (error) {
                        message.error(`${namespaceTip}命名空间下的 <${name}> 删除失败，请重新操作。`);
                    }
                },
                closable: true,
            });
        },
        [record, refresh, serviceMeshInstanceId]
    );

    return (
        <ProTable.Operation
            disabled={!!getDisabledReasonByCrd(record)}
            disabledReason={getDisabledReasonByCrd(record)}
            onClick={deleteOneCrd}
        >
            删除
        </ProTable.Operation>
    );
}

export default function CrdList() {
    // TODO 删除;pageSize的切换(没找到)
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {type} = useCurrentServiceMeshInstance();
    // 勾选（批量删除）
    // TODO 不同筛选条件下的勾选应该做并集操作，然后出来列表的展示弹窗，不然交互上有缺陷；勾选后，对其执行删除的交互优化
    const {selectedRowKeys, rowSelection, setSelectedRowKeys} = useListSelectedRow({
        getCheckboxProps: record => {
            const disabled = !!getDisabledReasonByCrd(record as Crd);

            return {
                disabled,
            };
        },
        renderCell: (_value, record, _index, originNode) => {
            return (
                <Tooltip title={getDisabledReasonByCrd(record as Crd)}>
                    {
                        cloneElement(originNode as ReactElement, {
                            disabled: !!getDisabledReasonByCrd(record as Crd),
                        })
                    }
                </Tooltip>
            );
        },
    });
    const {request, total} = useListTotal(getCrdListByInstanceId);
    const getList = useCallback(
        params => {
            return request({
                serviceMeshInstanceId,
                ...params,
            });
        },
        [request, serviceMeshInstanceId]
    );
    const rowKey: any = useCallback(
        (record: Crd) => getCrdIdByRecord(record),
        []
    );
    const operations = useOperations(total, selectedRowKeys, setSelectedRowKeys);
    const columns = useTableColumns();
    return (
        <Card title="Istio资源管理">
            <ProTable
                rowKey={rowKey}
                rowSelection={rowSelection}
                columns={columns}
                request={getList}
                operations={operations}
                filters={filters}
                tools={isHostingMesh(type) ? tools : []}
            />
        </Card>
    );
}
