// namespace、kind、name之间的的拼接符
import {Crd} from '@/interface';
const unupdatableCrdName = 'gateway-gw';
const joinChar = '#';
export const getCrdIdByRecord = (record: Crd) => {
    const {namespace, kind, name} = record;
    return [namespace, kind, name].join(joinChar);
};
export const getRecordByCrdId = (crdId: string) => {
    const [namespace, kind, name] = crdId.split(joinChar);
    return {namespace, kind, name};
};

export const getDisabledReasonByCrd = (record: Crd) => {
    return record.name === unupdatableCrdName ? '该Istio资源的变更，请前往域名管理页' : '';
};
