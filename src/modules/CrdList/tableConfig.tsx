import {Link} from 'react-router-dom';
import ProTable, {IFilters, ITools, IColumns, IOperations} from '@baidu/icloud-ui-pro-table';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {useCurrentServiceMeshInstance} from '@/modules/ServiceMeshInstance';
import {isHostingMesh} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/util';
import urls from '@/urls';
import AddButton from '@/components/AddButton';
import {Crd} from '@/interface';
import {DeleteOneCrd, DeleteBatchCrd, ImportCrdButton} from './index';
import {getDisabledReasonByCrd} from './utils';

// 筛选区
export const filters: IFilters = [
    {
        name: ['keywordType', 'keyword'],
        renderType: 'groupSearch',
        onFilter: (value, record) => {
            const [field, inputValue] = value || [];
            return !inputValue || (field && record[field].includes(inputValue));
        },
        defaultValue: ['name', ''],
        props: {
            options: [
                {
                    label: '名称',
                    value: 'name',
                },
                {
                    label: '类型',
                    value: 'kind',
                },
            ],
        },
    },
];
//  工作区
export const tools: ITools = [
    {
        renderType: 'refresh',
    },
];
// 列
export const useTableColumns = (): IColumns => {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    return [
        {
            title: '名称',
            dataIndex: 'name',
            render: (value, record) => {
                const {namespace, kind, name} = record;
                return (
                    <Link
                        to={
                            urls.crd.view.fill(
                                {serviceMeshInstanceId, namespace: namespace || '*'},
                                {kind, name}
                            )
                        }
                    >
                        <ProTable.Operation>{value}</ProTable.Operation>
                    </Link>
                );
            },
        },
        {
            title: '命名空间',
            dataIndex: 'namespace',
            render: value => {
                return value === '*' ? '' : value;
            },
        },
        {
            title: '类型',
            dataIndex: 'kind',
        },
        {
            title: '更新时间',
            dataIndex: 'updatedAt',
            sorter: true,
        },
        {
            title: '操作',
            dataIndex: 'action',
            render: (value, record, index, {refresh}) => {
                const {namespace, kind, name} = record;
                return (
                    <ProTable.OperationsWrapper>
                        <Link
                            to={
                                urls.crd.update.fill(
                                    {serviceMeshInstanceId, namespace: namespace || '*'},
                                    {kind, name}
                                )
                            }
                        >
                            <ProTable.Operation
                                disabled={!!getDisabledReasonByCrd(record as Crd)}
                                disabledReason={getDisabledReasonByCrd(record as Crd)}
                            >
                                编辑
                            </ProTable.Operation>
                        </Link>
                        <DeleteOneCrd
                            serviceMeshInstanceId={serviceMeshInstanceId}
                            record={record as Crd}
                            refresh={refresh}
                        />
                    </ProTable.OperationsWrapper>
                );
            },
        },
    ];
};
// 操作区
export const useOperations = (total: number, selectedRowKeys: any, setSelectedRowKeys: any): IOperations => {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {type} = useCurrentServiceMeshInstance();
    const operations = [
        {
            render: () => {
                return (
                    <Link to={urls.crd.create.fill({serviceMeshInstanceId})}>
                        <AddButton action="create">创建Istio资源</AddButton>
                    </Link>
                );
            },
        },
        {
            render: (props, {refresh}) => {
                return (
                    <DeleteBatchCrd
                        serviceMeshInstanceId={serviceMeshInstanceId}
                        selectedRowKeys={selectedRowKeys}
                        setSelectedRowKeys={setSelectedRowKeys}
                        refresh={refresh}
                    />
                );
            },
        },
    ];

    if (isHostingMesh(type)) {
        operations.push({
            render: (props, {refresh}) => {
                return (
                    <ImportCrdButton
                        serviceMeshInstanceId={serviceMeshInstanceId}
                        refresh={refresh}
                    />
                );
            },
        });
    }

    operations.push({
        render: () => {
            return <span>已选中{selectedRowKeys.length}条，共{total}条</span>;
        },
    });

    return operations;
};
