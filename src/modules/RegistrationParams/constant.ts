
export const paramsList = [
    {
        'paramName': 'api.eureka.custom.dci.class',
        'paramDesc': '设置 dataCenterInfo 信息的 class（Eureka协议接入有效）',
        'defaultValue': '-',
        'selectValue': '-',
        'tip': 'Eureka 协议接入有效',
    },
    {
        'paramName': 'api.eureka.custom.dci.name',
        'paramDesc': '设置 dataCenterInfo 信息的 name（Eureka协议接入有效）',
        'defaultValue': '-',
        'selectValue': '-',
        'tip': 'Eureka 协议接入有效',
    },
    {
        'paramName': 'api.eureka.deltaIntervalSeconds',
        'paramDesc': 'Eureka delta接口时间窗口，单位为秒（Eureka协议接入有效）',
        'defaultValue': '60',
        'selectValue': '-',
        'tip': 'Eureka 协议接入有效',
    },
    {
        'paramName': 'job.deleteServiceUnreferenced.enable',
        'paramDesc': '是否自动清理引用计数为零的服务',
        'defaultValue': 'false',
        'selectValue': 'true/false',
        'tip': '-',
    },
    {
        'paramName': 'job.deleteServiceUnreferenced.intervalMinutes',
        'paramDesc': '设置服务处于引用计数为零状态的时间需要满足 intervalMinutes 后可以被删除，单位为分钟',
        'defaultValue': '30',
        'selectValue': '-',
        'tip': '-',
    },
    {
        'paramName': 'job.deleteInstanceUnhealthy.enable',
        'paramDesc': '是否自动清理不健康的实例',
        'defaultValue': 'false',
        'selectValue': 'true/false',
        'tip': '-',
    },
    {
        'paramName': 'job.deleteInstanceUnhealthy.intervalMinutes',
        'paramDesc': '设置实例处于不健康状态的时间需要满足 intervalMinutes 后可以被删除，单位为分钟',
        'defaultValue': '60',
        'selectValue': '-',
        'tip': '-',
    },
    {
        'paramName': 'monitor.enable',
        'paramDesc': '是否开启Prometheus监控实例',
        'defaultValue': 'false',
        'selectValue': 'true/false',
        'tip': '-',
    },
    {
        'paramName': 'monitor.cprom.id',
        'paramDesc': 'Prometheus监控实例ID',
        'defaultValue': '-',
        'selectValue': '-',
        'tip': '-',
    },
    {
        'paramName': 'monitor.cprom.token',
        'paramDesc': 'Prometheus监控实例token',
        'defaultValue': '-',
        'selectValue': '-',
        'tip': '-',
    },
    {
        'paramName': 'namespace.autoCreate',
        'paramDesc': '开启/关闭命名空间自动创建，关闭后端口若指定命名空间会请求失败',
        'defaultValue': 'true',
        'selectValue': 'true/false',
        'tip': '-',
    },
];

