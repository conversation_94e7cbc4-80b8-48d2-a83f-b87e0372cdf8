import React, {useEffect, useRef, useState} from 'react';
import Card from '@baidu/icloud-ui-card';
import {Button} from 'acud';
import {message} from '@osui/ui';
import {useRequestCallback} from 'huse';
import ProTable from '@baidu/icloud-ui-pro-table';
import {putRegistrationParams, getRegistrationParams} from '@/api/registration';
import {useCurRegistrationId} from '@/hooks';
import MonacoEditorComponent from '@/components/MonacoEditorComponent';

import {columns} from './columns';
import {paramsList} from './constant';

import c from './index.less';
export default function RegistrationInstanceMonitor() {
    const editorRef = useRef<any>(null);
    const [isEdit, setIsEdit] = useState(false);
    const [curValue, setCurValue] = useState('');
    const [oldValue, setOldValue] = useState('');
    const registrationhInstanceId = useCurRegistrationId();

    const handleEditorDidMount = (editor: any) => {
        editorRef.current = editor;
    };
    const [
        getParams,
        {data},
    ] = useRequestCallback(getRegistrationParams, {registrationhInstanceId});

    useEffect(
        () => {
            getParams();
        },
        [getParams]
    );

    useEffect(
        () => {
            if (data) {
                const value = Object.entries(data)
                    .map(([key, value]) => `${key}: ${value}`)
                    .join('\n') || '';
                setOldValue(value);
                setCurValue(value);
            }
        },
        [data]
    );
    /**
     * 更改参数
     *
     * @param edit 是否处于编辑状态
     */
    const changeParams = (edit: boolean) => {
        setIsEdit(edit);
        if (editorRef.current) {
            editorRef.current.setValue(oldValue);
        }
    };
    /**
     * 当值发生变化时触发的回调函数
     *
     * @param value 变化的值
     */
    const onChange = (value: any) => {
        setCurValue(value);
    };
    /**
     * 更新参数
     *
     * @returns 无返回值
     */
    const updateParams = async () => {
        const payload = curValue.split('\n')
            .map(line => line.split(': ').map(part => part.trim()))
            .reduce((acc: any, [key, value]) => {
                acc[key] = value;
                return acc;
            }, {});
        try {
            const data = await putRegistrationParams({registrationhInstanceId, ...payload});
            if (data) {
                setIsEdit(false);
                getParams();
                message.success('更新成功');
            } else {
                message.error('更新失败');
            }
        } catch (error) {
            message.error('更新失败');
        }
    };
    return (
        <Card title="参数配置" className={c['detail-params']}>
            <div className={c['monaco-editor']}>
                <Button
                    // eslint-disable-next-line react/jsx-no-bind
                    onClick={() => changeParams(true)}
                    className={c['change-param-button']}
                    disabled={isEdit}
                    type="enhance"
                >
                    修改启动参数
                </Button>
                <MonacoEditorComponent
                    language="plaintext"
                    width="560px"
                    height="510px"
                    value={curValue}
                    // eslint-disable-next-line react/jsx-no-bind
                    onMount={handleEditorDidMount}
                    // eslint-disable-next-line react/jsx-no-bind
                    onChange={e => onChange(e)}
                    options={{
                        scrollBeyondLastLine: false,
                        readOnly: !isEdit,
                        wordWrap: 'on',
                        domReadOnly: !isEdit,
                        lineNumbers: 'on',
                        contextmenu: false,
                    }}
                />
                {isEdit
                && (
                    <div className={c['confirm-button-group']}>
                        <Button
                            type="primary"
                            className={c['confirm-button']}
                            // eslint-disable-next-line react/jsx-no-bind
                            onClick={() => updateParams()}
                        >更新
                        </Button>
                        <Button
                            // eslint-disable-next-line react/jsx-no-bind
                            onClick={() => changeParams(false)}
                            className={c['confirm-button']}
                        >取消
                        </Button>
                    </div>)}
            </div>
            <div>
                <span
                    className={c['param-description-span']}
                >
                    参数说明
                </span>
                <ProTable
                    columns={columns}
                    rowKey="paramName"
                    dataSource={paramsList}
                    pagination={false}
                    bordered
                />
            </div>

        </Card>
    );
}


