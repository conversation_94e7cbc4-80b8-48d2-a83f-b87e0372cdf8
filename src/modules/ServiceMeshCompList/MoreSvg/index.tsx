import {Button, Image, Modal, Tooltip, message} from '@osui/ui';
import {useCallback, useMemo} from 'react';
import {useBoolean} from 'huse';
import moreSvg from '@/assets/more.svg';
import {uninstallServiceMeshCompApi} from '@/api/serviceMesh';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import c from './index.less';

interface IProps {
    getServiceMeshCompList: () => void;
    name: string;
}
export default function MoreSvg({
    getServiceMeshCompList,
    name,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [modal, contextHolder] = Modal.useModal();

    const [isClickUninstallBtn, {on: onClickUninstallBtn, off: offClickUninstallBtn}] = useBoolean(true);
    const [isOpen, {on: onOpen, off: offOpen}] = useBoolean(false);

    const onOpenChange = useCallback(
        (open: boolean) => {
            if (open) {
                onOpen();
            }
            else {
                offOpen();
            }
        },
        [onOpen, offOpen]
    );

    const config = useMemo(
        () => {
            return {
                title: '卸载组件',
                content: (
                    <>
                        请确定是否要卸载组件 {name} ？
                    </>
                ),
                onOk: () => {
                    return new Promise(async (resolve, reject) => {
                        try {
                            await uninstallServiceMeshCompApi({serviceMeshInstanceId, name});
                            message.success('组件卸载成功');
                            getServiceMeshCompList();
                            resolve(true);
                        } catch (error) {
                            message.error('组件卸载失败，请重新卸载');
                            reject(error);
                        } finally {
                            onClickUninstallBtn();
                        }
                    });
                },
                onCancel: () => {
                    onClickUninstallBtn();
                },
                closable: true,
            };
        },
        [getServiceMeshCompList, name, onClickUninstallBtn, serviceMeshInstanceId]
    );
    const clickUninstallBtn = useCallback(
        () => {
            offOpen();
            modal.confirm(config);
            offClickUninstallBtn();
        },
        [config, modal, offOpen, offClickUninstallBtn]
    );

    return (
        <Tooltip
            open={isOpen}
            overlayStyle={{padding: 0}}
            placement="bottom"
            title={
                <>
                    {
                        isClickUninstallBtn
                            ? <Button type="text" onClick={clickUninstallBtn}>卸载</Button>
                            : null
                    }
                    {contextHolder}
                </>
            }
            showArrow={false}
            onOpenChange={onOpenChange}
        >
            <Image
                className={c['more-svg']}
                src={moreSvg}
                preview={false}
            />
        </Tooltip>
    );
}
