import {useRequestCallback} from 'huse';
import {useEffect} from 'react';
import CardIcloud from '@baidu/icloud-ui-card';
import {Button, Card, Image} from '@osui/ui';
import StateTag from '@baidu/icloud-ui-state-tag';
import {getServiceMeshCompListApi} from '@/api/serviceMesh';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import registry2istioSvg from '@/assets/registry2istio.svg';
import SpinAlignCenter from '@/components/SpinAlignCenter';
import c from './index.less';
import InstallComp from './InstallComp';
import MoreSvg from './MoreSvg';

export default function ServiceMeshCompList() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [
        getServiceMeshCompList,
        {pending, data},
    ] = useRequestCallback(getServiceMeshCompListApi, {serviceMeshInstanceId});

    useEffect(
        () => {
            getServiceMeshCompList();
        },
        [getServiceMeshCompList]
    );

    return (
        <CardIcloud
            extra={<Button onClick={getServiceMeshCompList}>刷新</Button>}
            title="组件管理"
        >
            {
                pending
                    ? <SpinAlignCenter />
                    : (
                        <div className={c['service-mesh-comp-list']}>
                            {
                                data?.map(item => {
                                    const {meta, instance} = item || {};

                                    return (
                                        <Card key={meta.name} className={c['service-mesh-comp-item']}>
                                            <div className={c['name-wrapper']}>
                                                <Image src={registry2istioSvg} preview={false} />
                                                <span className={c.name}>{meta.name}</span>
                                                <div className={c['install-wrapper']}>
                                                    {
                                                        instance
                                                            ? (
                                                                instance?.status?.phase === 'Running'
                                                                    ? (
                                                                        <>
                                                                            <StateTag type="success">已安装</StateTag>
                                                                            <MoreSvg
                                                                                name={meta.name}
                                                                                getServiceMeshCompList={
                                                                                    getServiceMeshCompList
                                                                                }
                                                                            />
                                                                        </>
                                                                    )
                                                                    : instance?.status?.phase === 'Abnormal'
                                                                        ? (
                                                                            <>
                                                                                <StateTag type="warning">异常</StateTag>
                                                                                <MoreSvg
                                                                                    name={meta.name}
                                                                                    // eslint-disable-next-line max-len
                                                                                    getServiceMeshCompList={getServiceMeshCompList}
                                                                                />
                                                                            </>
                                                                        )
                                                                        : <StateTag type="pending">安装中</StateTag>
                                                            )
                                                            : (
                                                                <InstallComp
                                                                    name={meta.name}
                                                                    getServiceMeshCompList={getServiceMeshCompList}
                                                                />
                                                            )
                                                    }
                                                </div>
                                            </div>

                                            <div className={c['short-introduction-wrapper']}>
                                                {meta.shortIntroduction}
                                            </div>

                                            {
                                                instance && (
                                                    <div className={c['installed-version-wrapper']}>
                                                        <span className={c['installed-version']}>
                                                            当前版本：{instance.installedVersion}
                                                        </span>
                                                    </div>
                                                )
                                            }
                                        </Card>
                                    );
                                })
                            }
                        </div>
                    )
            }

        </CardIcloud>
    );
}
