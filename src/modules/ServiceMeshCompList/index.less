.service-mesh-comp-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .service-mesh-comp-item {
        width: 320px;
        margin-bottom: 16px;

        :global .ant-card-body {
            padding: 16px;
        }

        .name-wrapper {
            display: flex;
            align-items: center;

            .name {
                font-family: PingFangSC-Medium;
                font-size: 12px;
                color: var(--GRAY_2);
                line-height: 20px;
                font-weight: 500;
                margin-left: 8px;
            }

            .install-wrapper {
                margin-left: auto;
                display: flex;
            }
        }

        .short-introduction-wrapper {
            color: var(--GRAY_4);
            margin: 8px 0 16px;

            .link {
                margin-left: 8px;
            }
        }

        .installed-version-wrapper {
            .installed-version {
                color: var(--GRAY_5);
            }
        }
    }
}
