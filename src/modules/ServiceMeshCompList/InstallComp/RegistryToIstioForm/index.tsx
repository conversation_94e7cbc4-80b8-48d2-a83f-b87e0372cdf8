import {Form, FormInstance, Input} from '@osui/ui';
import {useMemo} from 'react';
import {rulesConsulAddress} from './constant';
import c from './index.less';

interface IProps {
    form: FormInstance;
}
export default function RegistryToIstioForm({
    form,
}: IProps) {
    const initialValues = useMemo(
        () => {
            return {
                consulAddress: '',
                token: '',
            };
        },
        []
    );

    return (
        <Form
            className={c['registry-to-istio-form']}
            name="basic"
            labelAlign="left"
            initialValues={initialValues}
            form={form}
        >
            <Form.Item
                name={['consulAddress']}
                required
                label="Consul注册中心地址"
                rules={rulesConsulAddress}
            >
                <Input placeholder="格式如 https://www.example.com " />
            </Form.Item>

            <Form.Item
                name={['token']}
                label="Consul ACL token"
            >
                <Input placeholder="请输入Consul ACL token" />
            </Form.Item>
        </Form>
    );
}
