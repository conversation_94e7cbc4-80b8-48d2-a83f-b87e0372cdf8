import {Button, Drawer, Form, message} from '@osui/ui';
import {useCallback} from 'react';
import {useBoolean} from 'huse';
import {installServiceMeshCompApi} from '@/api/serviceMesh';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import RegistryToIstioForm from './RegistryToIstioForm';
import c from './index.less';

interface IProps {
    getServiceMeshCompList: () => void;
    name: string;
}

export default function InstallComp({
    getServiceMeshCompList,
    name,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const [form] = Form.useForm();
    const [isOpenDrawer, {on: onOpenDrawer, off: offOpenDrawer}] = useBoolean(false);
    const [isSubmiting, {on: onSubmiting, off: offSubmiting}] = useBoolean(false);

    const onSubmit = useCallback(
        async () => {
            const formValues = await form.validateFields();
            const params = Object.entries(formValues).map(([key, val]) => `${key}: ${val}\n`).join('');
            try {
                onSubmiting();
                if (name === 'registry2istio') {
                    const rsp = await installServiceMeshCompApi({serviceMeshInstanceId, name, params});
                    if (rsp) {
                        message.success('组件安装成功');
                        offOpenDrawer();
                        getServiceMeshCompList();
                    }
                    else {
                        message.error('组件安装失败，请重新安装');
                    }
                }
            } catch (error) {
                console.error(error);
            } finally {
                offSubmiting();
            }
        },
        [form, name, serviceMeshInstanceId, offOpenDrawer, getServiceMeshCompList, onSubmiting, offSubmiting]
    );

    return (
        <>
            <Button
                type="primary"
                onClick={onOpenDrawer}
            >
                安装
            </Button>

            <Drawer
                width={472}
                title="配置 Consul 注册中心"
                placement="right"
                onClose={offOpenDrawer}
                open={isOpenDrawer}
                footer={
                    <div className={c['footer-btn-wrapper']}>
                        <Button
                            onClick={offOpenDrawer}
                        >
                            取消
                        </Button>
                        <Button
                            className={c['submit-btn']}
                            type="primary"
                            loading={isSubmiting}
                            onClick={onSubmit}
                        >
                            确定
                        </Button>
                    </div>
                }
            >

                {
                    name === 'registry2istio'
                        ? <RegistryToIstioForm form={form} />
                        : null
                }
            </Drawer>
        </>
    );
}
