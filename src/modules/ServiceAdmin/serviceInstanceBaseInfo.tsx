import React, {useCallback, useEffect, useState} from 'react';
import {useRequestCallback} from 'huse';
import {Popover} from 'acud';
import Card from '@baidu/icloud-ui-card';
import StateTag from '@baidu/icloud-ui-state-tag';
import {Row, Col} from '@osui/ui';
import {useHistory} from 'react-router-dom';
import {getServiceInstanceListApi} from '@/api/serviceAdmin';
import {SERVICE_MESH_ADMIN_INSTANCE_PATH, SERVICE_MESH_ADMIN_PATH} from '@/links';
import * as Layout from '@/components/Layout';
import {useFramework} from '@/hooks';
import {useQuery} from '@/hooks/useUrl';
import PageLoading from '@/components/PageLoading';
import c from './index.less';

// eslint-disable-next-line complexity
export default function ServiceInstanceDetail() {
    const history = useHistory();
    const [detailInfo, setDetailInfo] = useState<any>({});
    const {serviceId, serviceName, instanceId, instanceServiceId} =
        useQuery<{serviceId: string, serviceName: string, instanceId: string, instanceServiceId: string}>();
    // 服务实例列表
    const payload = {serviceId, serviceName, queryInstanceId: instanceServiceId, instanceId: instanceId};
    const [request, {data}] = useRequestCallback(getServiceInstanceListApi, payload);
    useEffect(
        () => {
            request();
        },
        [request]
    );
    useEffect(
        () => {
            if (data?.result?.length) {
                setDetailInfo(data.result[0]);
            }
        },
        [data]
    );
    const onHeaderBack = useCallback(
        () => {
            history.push(SERVICE_MESH_ADMIN_INSTANCE_PATH.fill({}, {
                serviceId,
                serviceName,
                instanceId,
            }));
        },
        [history, serviceId, serviceName, instanceId]
    );
    const goToList = useCallback(
        () => {
            history.push(SERVICE_MESH_ADMIN_PATH.fill({}));
        },
        [history]
    );
    const {region: {onRegionChange, unRegionChange}} = useFramework();
    useEffect(
        () => {
            onRegionChange(goToList);
            return () => unRegionChange(goToList);
        },
        [goToList, onRegionChange, unRegionChange]
    );
    const Header = () => {
        return (
            <Layout.Header
                backIcon
                onBack={onHeaderBack}
                title={'实例详情'}
            />
        );
    };
    if (!detailInfo.serviceInstanceId) {
        return <PageLoading />;
    }
    const listItemsHover = Object.keys(detailInfo?.metadata).map(key => {
        return (
            <>
                <span>{key}:{detailInfo.metadata[key]}</span>
                <br />
            </>
        );
    });

    const listItems = Object.keys(detailInfo?.metadata).map(key => {
        return <><span className={c('wrap-tag')}>{key}:{detailInfo.metadata[key]}</span></>;
    });

    return (
        <>
            <Header />
            <Card
                title="基本信息"
                className={c('instance-detail-container')}
            >
                <Row gutter={[48, 16]}>
                    <Col span={8}>
                        <Card.Field title="Id">
                            {detailInfo.serviceInstanceId || '-'}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="IP">
                            {detailInfo.host || '-'}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="端口">
                            {detailInfo.port || '-'}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="健康状态">
                            {
                                <StateTag
                                    type={detailInfo.healthStatus ? 'success' : 'warning'}
                                >
                                    {detailInfo.healthStatus === true
                                        ? '健康' : (detailInfo.healthStatus === false ? '异常' : '-')}
                                </StateTag>
                            }
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="隔离状态">
                            {
                                <StateTag
                                    type={detailInfo.isolateEnable ? 'warning' : 'success'}
                                >
                                    {detailInfo.isolateEnable === true ? '隔离'
                                        : (detailInfo.isolateEnable === false ? '不隔离' : '-')}
                                </StateTag>
                            }
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="创建时间">
                            {detailInfo.createTime}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="修改时间">
                            {detailInfo.updateTime}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="健康检查">
                            <StateTag
                                type={detailInfo.healthCheckEnable ? 'success' : 'warning'}
                            >
                                {detailInfo.healthCheckEnable ? '开启' : '关闭'}
                            </StateTag>
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="TTL">
                            {detailInfo.healthCheckEnable ? `${detailInfo.ttl}秒` : '-'}
                        </Card.Field>
                    </Col>
                    <Col span={8}>
                        <Card.Field title="最近健康检查时间">
                            {detailInfo.lastHeartbeatTime}
                        </Card.Field>
                    </Col>
                </Row>
                <div className={c('instance-detail-border')}></div>
                <Card.Field title="实例标签">
                    <Popover
                        placement="top"
                        content={(
                            <div className={c('wrap-content')}>
                                {listItemsHover}
                            </div>
                        )}
                        trigger="hover"
                    >
                        <div className={c('tag-content')}>
                            {listItems}
                        </div>
                    </Popover>
                </Card.Field>
            </Card>
        </>
    );
}
