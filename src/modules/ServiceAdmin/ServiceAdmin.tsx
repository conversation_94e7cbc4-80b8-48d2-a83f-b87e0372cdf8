import React from 'react';
import {Switch, Route, Redirect} from 'react-router-dom';
import {
    SERVICE_MESH_ADMIN_PATH,
    SERVICE_MESH_ADMIN_INSTANCE_PATH,
} from '@/links';
import ServiceList from './ServiceList';
import ServiceInstance from './ServiceInstance';


export default function ServiceAdmin() {
    return (
        <div>
            <Switch>
                <Route path={SERVICE_MESH_ADMIN_INSTANCE_PATH.fill()} component={ServiceInstance} />
                <Route path={SERVICE_MESH_ADMIN_PATH.fill()} component={ServiceList} />
                <Redirect to={SERVICE_MESH_ADMIN_PATH.fill()} />
            </Switch>

        </div>
    );

}
