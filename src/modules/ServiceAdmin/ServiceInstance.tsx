import React, {useCallback, useRef, RefObject, useState} from 'react';
import {Link} from 'react-router-dom';
import styled from 'styled-components';
import ProTable, {IColumns, IFilters, ITools, IOperations} from '@baidu/icloud-ui-pro-table';
import {Space} from '@osui/ui';
import StateTag from '@baidu/icloud-ui-state-tag';
import {useQuery} from '@/hooks/useUrl';
import {
    SERVICE_MESH_ADMIN_PATH,
    SERVICE_MESH_ADMIN_INSTANCE_BASE_PATH,
} from '@/links';
import {formatMultiValueQuery} from '@/utils';
import ProductDocLink from '@/components/ProductDocLink';
import {getServiceInstanceListApi, ServiceInstanceListItemType} from '@/api/serviceAdmin';
import * as Layout from '@/components/Layout';
import {useListTotal} from '@/hooks/useList';
import {LayoutHeaderWrap} from '@/components/HeaderWrap';
import {useApiWithRegion} from '../../hooks/useApiWithRegion';
import c from './index.less';
import {DeleteInstance} from './DeleteInstance';
import {CreateInstance} from './CreateInstance';
import {BatchOperations} from './batchOperations';


const ListHeaderWrap = styled(LayoutHeaderWrap)`
    .ant-page-header.osui-page-header {
        padding: 12px 16px;
    }
`;

const tools: ITools = [
    {
        renderType: 'refresh',
        style: {marginTop: 26},
    },
];

// 筛选区
const filters: IFilters = [
    {
        name: ['keywordType', 'keyword'],
        renderType: 'groupSearch',
        style: {marginTop: 26},
        defaultValue: ['queryInstanceId', ''],
        props: {
            options: [
                {
                    label: '实例ID',
                    value: 'queryInstanceId',
                },
                {
                    label: '实例IP',
                    value: 'host',
                },
            ],
        },
    },
];


const getColumns = (instanceId: string, serviceId: string, serviceName: string, refreshRef: any) => {
    const columns: IColumns = [
        {
            title: '实例ID',
            dataIndex: 'serviceInstanceId',
            render(value) {
                return (
                    <Link
                        to={SERVICE_MESH_ADMIN_INSTANCE_BASE_PATH.fill({}, {
                            serviceId,
                            serviceName,
                            instanceId,
                            instanceServiceId: value,
                        })}
                    >
                        {value}
                    </Link>
                );

            },
        },
        {
            title: '实例IP',
            dataIndex: 'host',
            render(value) {
                return value || '-';
            },
        },
        {
            title: '端口',
            dataIndex: 'port',
            render(value) {
                return value || '-';
            },
        },
        {
            title: '健康状态',
            dataIndex: 'healthStatus',
            filters: [
                {text: '全部状态', value: ''},
                {text: '健康', value: true},
                {text: '异常', value: false},
            ],
            filterType: 'single',
            render(value) {
                const type = value ? 'success' : 'warning';
                const text = value === true ? '健康' : (value === false ? '异常' : '-');
                return <StateTag type={type}>{text}</StateTag>;
            },
        },
        {
            title: '隔离状态',
            dataIndex: 'isolateEnable',
            filters: [
                {text: '全部状态', value: ''},
                {text: '隔离', value: true},
                {text: '不隔离', value: false},
            ],
            filterType: 'single',
            render(value) {
                const type = value ? 'warning' : 'success';
                const text = value === true ? '隔离' : (value === false ? '不隔离' : '-');
                return <StateTag type={type}>{text}</StateTag>;
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            render(value) {
                return value || '-';
            },
        },
        {
            title: '修改时间',
            dataIndex: 'updateTime',
            render(value) {
                return value || '-';
            },
        },
        {
            title: '最近健康检查时间',
            dataIndex: 'lastHeartbeatTime',
            render(value) {
                return value || '-';
            },
        },
        {
            title: '操作',
            render: (value, row, index, {refresh}) => {
                refreshRef.current = refresh;
                return (
                    <>
                        <Space>
                            <CreateInstance
                                isEdit
                                instanceId={instanceId}
                                serviceId={serviceId}
                                serviceName={row.serviceName}
                                instance={row}
                                refresh={refresh as any}
                            />
                            <DeleteInstance
                                instanceId={instanceId}
                                serviceId={serviceId}
                                item={row as ServiceInstanceListItemType}
                                refresh={refresh as any}
                            />
                        </Space>

                    </>
                );
            },
        },
    ];
    return columns;
};


const getOperations = (instanceId: string, serviceId: string, setSelectedRows: React.Key[],
    serviceName: string, refresh: RefObject<() => Promise<any>> | (() => Promise<any>)) => {
    const operations: IOperations = [
        {
            render() {
                return (
                    <div>
                        <div className={c['service-instance-title']}>服务实例列表</div>
                        <CreateInstance
                            instanceId={instanceId}
                            serviceId={serviceId}
                            serviceName={serviceName}
                            refresh={refresh}
                        />
                        <BatchOperations
                            setSelectedRows={setSelectedRows}
                            instanceId={instanceId}
                            serviceId={serviceId}
                            refresh={refresh}
                        />
                    </div>
                );
            },
        },
    ];
    return operations;
};

export default function ServiceInstance() {
    const refreshRef = useRef<() => Promise<any>>(null);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<React.Key[]>([]);
    // 获取url中注册中心实例instanceId和服务管理name和id；
    const {instanceId, serviceId, serviceName} = useQuery<{
        instanceId: string; serviceId: string; serviceName: string;
    }>();

    // 服务实例列表
    const {request} = useListTotal(useApiWithRegion(getServiceInstanceListApi));
    const getList = useCallback(
        params => {
            const newParams: any = formatMultiValueQuery(params, ['healthStatus', 'isolateEnable']);
            if (newParams.isolateEnable) {
                newParams.isolateStatus = newParams.isolateEnable;
            }
            if (newParams.keyword) {
                newParams[newParams.keywordType] = newParams.keyword;
            }
            delete newParams.isolateEnable;
            delete newParams.keywordType;
            delete newParams.keyword;
            return request({...newParams, serviceId});
        },
        [request, serviceId]
    );

    const getRequest = useCallback(
        params => {
            setSelectedRows([]);
            setSelectedRowKeys([]);
            return getList(params);
        },
        [getList]
    );
    const onSelectChange = (newSelectedRowKeys: React.Key[], newSelectedRows: any) => {
        setSelectedRowKeys(newSelectedRowKeys);
        setSelectedRows(newSelectedRows);
    };

    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
    };

    return (
        <div className={c['service-instance-list']}>
            <ListHeaderWrap>
                <Layout.Header
                    extra={
                        <>
                            <div className={c['service-instance-head']}>
                                <Link to={SERVICE_MESH_ADMIN_PATH.fill({}, {instanceId})}>服务列表</Link>
                                <span>{` > ${serviceName}`}</span>
                            </div>
                            <ProductDocLink />
                        </>
                    }
                />
            </ListHeaderWrap>
            <Layout.MainContent>
                <ProTable
                    rowKey="serviceInstanceId"
                    rowSelection={rowSelection}
                    operations={
                        getOperations(
                            instanceId, serviceId, selectedRows, serviceName,
                            refreshRef
                        )
                    }
                    filters={filters}
                    tools={tools}
                    columns={getColumns(instanceId, serviceId, serviceName, refreshRef)}
                    request={getRequest}
                />
            </Layout.MainContent>
        </div>
    );
}
