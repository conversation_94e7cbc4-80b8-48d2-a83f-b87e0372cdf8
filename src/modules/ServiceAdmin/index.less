.registration-select {
    position: absolute;
    left: 100px;
    top: 10px;

    & > span {
        font-size: 14px;
    }
}

.instance-detail-container {
    border-radius: 6px;
    margin: 24px;
}

.batch-operations {
    margin-left: 24px;
    width: 160px;
}

.create-instance-modal-tips {
    color: #84868c;
    position: relative;
    padding-left: 10px;
    top: -20px;
}

.service-instance-head {
    position: absolute;
    left: 16px;
    top: 12px;

    & > a {
        // color: #000;

        &:hover {
            color: #2468f2;
        }
    }
}

.service-instance-list {
    .service-instance-title {
        position: relative;
        font-size: 16px;
        font-weight: 500;
        color: #000;
        z-index: 100;
        margin-top: -4px;
        margin-bottom: 10px;
    }
}

.create-instance-modal {
    :global {
        .icloud-ui-form-label {
            flex-basis: 100px;
        }

        .ant-modal-footer {
            display: none;
        }
    }
}

.instance-detail-border {
    height: 1px;
    background-color: var(--color-gray-4);
    margin: 24px 0;
}

.wrap-tag {
    background: #ecedf6;
    margin-right: 20px;
    line-height: 30px;
    border-radius: 15px;
    display: inline-block;
    padding: 0 10px;
    max-width: 150px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.wrap-content {
    width: 400px !important;
    max-height: 400px !important;
    overflow: hidden;
    overflow-y: scroll;
}

.tag-content {
    max-width: 1000px;
    overflow: hidden;
    display: inline-block;
    vertical-align: top;
    white-space: nowrap;
    text-overflow: ellipsis;
}
