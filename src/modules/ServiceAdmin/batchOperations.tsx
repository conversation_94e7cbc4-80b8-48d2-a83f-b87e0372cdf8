import React, {useCallback, useEffect, useState, RefObject} from 'react';
import {Select, Tooltip, Modal, Form, Switch} from 'acud';
import {batchUpdateServiceInstanceApi, ServiceInstanceItemType} from '@/api/serviceAdmin';


import c from './index.less';

const selectOptions = [
    {value: 'delete', label: '删除', disabled: false, disabledTip: ''},
    {value: 'updateStatus', label: '修改隔离状态', disabled: false, disabledTip: ''},
];


export function BatchOperations(props: {
    setSelectedRows: React.Key[];
    instanceId: string;
    serviceId: string;
    refresh: RefObject<() => Promise<any>> | (() => Promise<any>);
}) {
    const {setSelectedRows, instanceId, serviceId, refresh} = props;
    const [curIsolateEnable, setCurIsolateEnable] = useState<boolean>(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [operationValue, setOperationValue] = useState<string[]>([]);
    const [serviceInstanceList, setServiceInstanceList] = useState<Array<ServiceInstanceItemType | null>>([]);
    useEffect(
        () => {
            const isAllIsolate = [...new Set(setSelectedRows.map((item: any) => item.isolateEnable))];
            if (isAllIsolate.length) {
                setCurIsolateEnable(isAllIsolate[0]);
            }
            selectOptions.forEach(item => {
                item.disabled = !setSelectedRows.length;
                item.disabledTip = '请先选择要操作的实例';
                if (item.value === 'updateStatus' && isAllIsolate.length > 1) {
                    item.disabled = true;
                    item.disabledTip = '已选择的实例隔离状态不一致，请重新选择';
                }
            });

            const instanceList = setSelectedRows.map((item: any) => ({
                id: item.serviceInstanceId,
                host: item.host,
                namespace: 'default',
                isolateEnable: item.isolateEnable,
                service: item.serviceName,
            }));
            setServiceInstanceList(instanceList);
        },
        [setSelectedRows, isModalOpen]
    );

    const onRefresh = useCallback(
        () => {
            if (typeof refresh === 'function') {
                refresh();
            } else if (typeof refresh.current === 'function') {
                refresh.current();
            }
        },
        [refresh]
    );

    const handleChange = useCallback(
        value => {
            setOperationValue([]);
            if (value === 'updateStatus') {
                // 更新隔离状态
                setIsModalOpen(true);
            } else {
                // 删除
                Modal.confirm({
                    title: '删除确认',
                    content: '确认删除实例吗？删除后无法恢复！',
                    onOk() {
                        return batchUpdateServiceInstanceApi({
                            instanceId,
                            serviceId,
                            serviceInstanceList,
                            action: 'BatchDelete',
                        }).then(() => {
                            setIsModalOpen(false);
                            onRefresh();
                        });
                    },
                });
            }
        }
        , [instanceId, serviceId, serviceInstanceList, onRefresh]
    );
    const optionRender = useCallback(
        (label, option) => {
            return (
                <Tooltip trigger={option.disabled ? 'hover' : ''} placement="top" title={option.disabledTip}>
                    {label}
                </Tooltip>
            );
        },
        []
    );

    const onSubmit = useCallback(
        async () => {
            const instanceList = serviceInstanceList.map((item: any) => {
                return {...item, isolate: curIsolateEnable};
            });
            await batchUpdateServiceInstanceApi({
                instanceId,
                serviceId,
                serviceInstanceList: instanceList,
                action: 'BatchIsolate',
            }).then(() => {
                setIsModalOpen(false);
                onRefresh();
            });
        },
        [instanceId, serviceId, serviceInstanceList, onRefresh, curIsolateEnable]
    );
    return (
        <>
            <Select
                className={c('batch-operations')}
                onChange={handleChange}
                value={operationValue}
                placeholder="批量操作"
                options={selectOptions}
                optionRender={optionRender}
            />
            {isModalOpen
                && (
                    <Modal
                        className={c['create-instance-modal']}
                        title="修改隔离状态"
                        visible
                        // eslint-disable-next-line react/jsx-no-bind
                        onCancel={() => setIsModalOpen(false)}
                        onOk={onSubmit}
                        width={610}
                    >
                        <Form>
                            <Form.Item
                                label="隔离服务实例"
                                name="isolateEnable"
                            >
                                <Switch
                                    // eslint-disable-next-line react/jsx-no-bind
                                    onChange={(checked: boolean) => setCurIsolateEnable(checked)}
                                    defaultChecked={curIsolateEnable}
                                    checkedChildren="开"
                                    unCheckedChildren="关"
                                />
                            </Form.Item>
                            <div className={c['create-instance-modal-tips']}>
                                开启服务实例隔离后，主调方无法发现识别被隔离的服务实例，不论其健康状态如何。
                            </div>
                        </Form>
                    </Modal>)
            }
        </>
    );
}
