import React, {useCallback, useEffect, useState, useMemo} from 'react';
import {Link} from 'react-router-dom';
import styled from 'styled-components';
import ProTable, {IColumns, IFilters, ITools} from '@baidu/icloud-ui-pro-table';
import StateTag from '@baidu/icloud-ui-state-tag';
import {Select} from '@osui/ui';
import {useQuery} from '@/hooks/useUrl';
import {useFramework} from '@/hooks';
import {
    SERVICE_MESH_ADMIN_INSTANCE_PATH,
} from '@/links';
import ProductDocLink from '@/components/ProductDocLink';
import {getServiceAdminListApi} from '@/api/serviceAdmin';
import * as Layout from '@/components/Layout';
import {useListTotal} from '@/hooks/useList';
import {LayoutHeaderWrap} from '@/components/HeaderWrap';
import {getRegistrationListApi} from '@/api/registration';
import {useApiWithRegion} from '../../hooks/useApiWithRegion';
import c from './index.less';


const ListHeaderWrap = styled(LayoutHeaderWrap)`
    .ant-page-header.osui-page-header {
        padding: 12px 16px;
    }
`;

const tools: ITools = [
    {
        renderType: 'refresh',
    },
];
// 筛选区
const filters: IFilters = [
    {
        name: ['keywordType', 'keyword'],
        renderType: 'groupSearch',
        defaultValue: ['serviceName', ''],
        props: {
            options: [
                {
                    label: '服务名称',
                    value: 'serviceName',
                },
                {
                    label: '命名空间',
                    value: 'namespace',
                },
            ],
        },
    },
];

const getColumns = (currentRegistrationInstance: string | null) => {
    const columns: IColumns = [
        {
            title: '服务名',
            dataIndex: 'name',
            render(value, row) {
                const {name, id, namespace} = row || {};
                return (
                    <Link
                        to={SERVICE_MESH_ADMIN_INSTANCE_PATH.fill({}, {
                            serviceId: id,
                            serviceName: name,
                            namespace: namespace,
                            instanceId: currentRegistrationInstance,
                        })}
                    >
                        {value}
                    </Link>
                );

            },
        },
        {
            title: '命名空间',
            dataIndex: 'namespace',
            render(value) {
                return value || '-';
            },
        },
        {
            title: '状态',
            dataIndex: 'state',
            render(value, row) {
                let classType: any = 'info';
                let textType = '无实例';
                if (row.healthCount > 0) {
                    classType = 'success';
                    textType = '运行中';
                } else if (row.totalCount > 0 && row.healthCount === 0) {
                    classType = 'error';
                    textType = '不可用';
                }
                return (
                    <StateTag type={classType}>
                        {textType}
                    </StateTag>
                );
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            render(value) {
                return value || '-';
            },
        },
        {
            title: '修改时间',
            dataIndex: 'updateTime',
            render(value) {
                return value || '-';
            },
        },
        {
            title: '健康实例/总实例',
            dataIndex: 'count',
            render(value, row) {
                return `${row.healthCount ?? '-'}/${row.totalCount ?? '-'}`;
            },
        },
    ];
    return columns;
};


const filterOption = (input: any, option: any) => {
    return option?.label?.toLowerCase()?.includes(input.toLowerCase());
};


export default function ServiceList() {
    // 设置当前选中的注册中心实例
    const {region: {currentRegion}} = useFramework();
    const [currentRegistrationInstance, setRegistrationInstance] = useState<string | null>(null);
    const onRegistrationInstanceChange = (value: string) => {
        setRegistrationInstance(value);
    };

    // 获取注册中心列表
    const [registrationList, setRegistrationList] = useState<Array<{ label: string, value: string }>>([]);
    const {instanceId, namespaceName} = useQuery<{ instanceId: string, namespaceName: string }>();

    const curFilters = useMemo(
        () => {
            if (namespaceName) {
                return [{...filters[0], defaultValue: ['namespace', namespaceName]}];
            }
            return filters;
        },
        [namespaceName]
    );

    const getRegistrationList = useCallback(
        async () => {
            // 获取所有注册中心实例
            const data = await getRegistrationListApi({pageSize: 10000, pageNo: 1} as unknown as void);
            const list = data?.result?.filter(e => e.status === 2)?.map(item => ({
                label: item.name, value: item.id,
            }));
            setRegistrationList(list || []);
            if (list?.length) {
                if (list.find(e => e.value === instanceId)) {
                    setRegistrationInstance(instanceId);
                } else {
                    setRegistrationInstance(list[0].value);
                }
            }
        },
        [instanceId]
    );

    useEffect(
        () => {
            getRegistrationList();
        },
        [getRegistrationList, currentRegion]
    );

    // 服务管理列表
    const {request} = useListTotal(useApiWithRegion(getServiceAdminListApi));
    const getList = useCallback(
        params => {
            if (!currentRegistrationInstance) {
                return Promise.resolve({request: []});
            }
            return request({...params, instanceId: currentRegistrationInstance});
        },
        [request, currentRegistrationInstance]
    );


    return (
        <>
            <ListHeaderWrap className="service-admin-list">
                <Layout.Header
                    title="服务列表"
                    extra={
                        <>
                            <div className={c['registration-select']}>
                                <span>所属注册中心：</span>
                                <Select
                                    showSearch
                                    allowClear
                                    value={currentRegistrationInstance}
                                    // eslint-disable-next-line react/jsx-no-bind
                                    onChange={onRegistrationInstanceChange}
                                    placeholder="请选择注册中心实例"
                                    options={registrationList}
                                    style={{width: 200}}
                                    filterOption={filterOption}
                                />
                            </div>
                            <ProductDocLink />
                        </>

                    }
                />
            </ListHeaderWrap>
            <Layout.MainContent>
                <ProTable
                    rowKey="id"
                    tools={tools}
                    columns={getColumns(currentRegistrationInstance)}
                    filters={curFilters}
                    request={getList}
                />
            </Layout.MainContent>
        </>
    );
}
