import React, {useState, useRef, RefObject} from 'react';
import {Button, Modal, Input, Switch, InputNumber, Popover, Space} from '@osui/ui';
import Form, {useFormInstance} from '@baidu/icloud-ui-form';
import {OutlinedPlus} from '@baidu/acud-icon';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {createServiceInstanceApi, editServiceInstanceApi} from '@/api/serviceAdmin';
import Label, {LabelItemType} from './Label';
import c from './index.less';

const TTL = (props: { value?: number, onChange?: () => void }) => {
    const {value, onChange} = props;
    return (
        <div>
            <span>检查方式：心跳上报</span>
            <span
                style={{marginLeft: 20, marginTop: -8}}
            >
                TTL：<InputNumber
                    min={1}
                    max={60}
                    value={value}
                    step={1}
                    precision={0}
                    onChange={onChange}
                />  秒
            </span>
        </div>
    );
};

const Submit = (props: {
    serviceName: string;
    onSubmit: (data: Record<string, any>) => void;
    onCancel: () => void; labelValidate: () => Promise<any>;
}) => {
    const {serviceName, onSubmit, onCancel, labelValidate} = props;
    const formInstance = useFormInstance();
    const onConfirm = async () => {
        await Promise.all([formInstance?.validate(), labelValidate?.()]);
        const data = formInstance.getFormState()?.values;
        data.host = data.host?.trim();
        const metadata = data.metadata || [];
        const metadataObj: Record<string, any> = {};
        metadata.forEach((item: LabelItemType) => {
            item.label && (metadataObj[item.label] = item.value);
        });
        data.metadata = metadataObj;
        data.namespace = 'default';
        data.serviceName = serviceName;
        onSubmit(data);
    };
    return (
        <>
            <Space style={{float: 'right'}}>
                <Button onClick={onCancel}>取消</Button>
                <Button type="primary" onClick={onConfirm}>确认</Button>
            </Space>
        </>
    );
};


export function CreateInstance(props: {
    instanceId: string;
    serviceId: string;
    serviceName: string;
    instance?: Record<string, any>;
    refresh: RefObject<() => Promise<any>> | (() => Promise<any>);
    isEdit?: boolean;
}) {
    const {instanceId, serviceId, serviceName, instance, refresh, isEdit = false} = props;
    const {host, port, healthCheckEnable, ttl, isolateEnable, metadata, serviceInstanceId} = instance || {};
    const metadataArr: LabelItemType[] = [];
    if (metadata) {
        Object.keys(metadata).forEach(key => {
            metadataArr.push({
                label: key,
                value: metadata[key] as string,
            });
        });
    }
    const defaultValue = isEdit && instance ? {
        serviceName: serviceName,
        namespace: 'default',
        host,
        port,
        healthCheckEnable,
        ttl,
        isolateEnable,
        metadata: metadataArr,
    } : {
        serviceName: '',
        namespace: 'default',
        host: '',
        port: '',
        healthCheckEnable: false,
        ttl: 1,
        isolateEnable: false,
        metadata: [],
    };

    const [isModalOpen, setIsModalOpen] = useState(false);

    const showModal = () => {
        setIsModalOpen(true);
    };

    const onCancel = () => {
        setIsModalOpen(false);
    };

    const labelRef = useRef<{ validate: () => Promise<any> }>(null);

    const labelValidate = async () => {
        return labelRef.current?.validate();
    };

    const onSubmit = async (data: Record<string, any>) => {
        if (isEdit) {
            await editServiceInstanceApi({instanceId, serviceId, serviceInstanceId}, {data});
        } else {
            await createServiceInstanceApi({instanceId, serviceId}, {data});
        }
        if (typeof refresh === 'function') {
            refresh();
        } else if (typeof refresh.current === 'function') {
            refresh.current();
        }
        setIsModalOpen(false);
    };
    return (
        <>
            <Button
                type={isEdit ? 'link' : 'primary'}
                icon={isEdit ? null : <OutlinedPlus />}
                onClick={showModal}
            >
                { isEdit ? '编辑' : ' 创建服务实例'}
            </Button>
            {isModalOpen
                && (
                    <Modal
                        className={c['create-instance-modal']}
                        title={isEdit ? '编辑服务实例' : '创建服务实例'}
                        open
                        width={610}
                        footer={<></>}
                        onCancel={onCancel}
                    >
                        <Form
                            defaultValue={defaultValue}
                            validateFirst={false}
                        >
                            <Form.Field
                                label="实例IP"
                                name="host"
                                required
                                rules={{
                                    validator: (value: string) => {
                                        // eslint-disable-next-line
                                        if (!/^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}$/.test(value)) {
                                            return 'IP格式不合法';
                                        }
                                        return null;
                                    },
                                }}
                            >
                                <Input style={{width: 400}} disabled={isEdit} />
                            </Form.Field>
                            <Form.Field
                                label="端口"
                                name="port"
                                required
                            >
                                <InputNumber
                                    style={{width: 400}}
                                    min={0}
                                    step={1}
                                    max={65535}
                                    precision={0}
                                    disabled={isEdit}
                                />
                            </Form.Field>
                            <Form.Field
                                label="实例标签"
                                name="metadata"
                                description="温馨提示：实例标签用于标识实例的功能和特征，每个标签包含键和值两部分"
                            >
                                <Label ref={labelRef} />
                            </Form.Field>
                            <Form.Field
                                label={
                                    <>
                                        开启健康检查
                                        <Popover content="开启后，服务实例的健康状态检查将由Server端负责">
                                            <QuestionCircleOutlined />
                                        </Popover>
                                        <span style={{marginRight: 3}}></span>
                                    </>
                                }
                                name="healthCheckEnable"

                            >
                                <Switch />
                            </Form.Field>
                            <Form.Data>
                                {data => {
                                    return data.healthCheckEnable ? (
                                        <Form.Field
                                            emptyLabel
                                            name="ttl"
                                            required
                                        >
                                            <TTL />
                                        </Form.Field>
                                    ) : null;
                                }}
                            </Form.Data>
                            <Form.Field
                                label={
                                    <>
                                        是否隔离
                                        <Popover content="在隔离状态下，主调方无法发现识别被隔离的服务实例，不论其健康状态如何">
                                            <QuestionCircleOutlined />
                                        </Popover>
                                        <span style={{marginRight: 3}}></span>
                                    </>
                                }
                                name="isolateEnable"
                            >
                                <Switch />
                            </Form.Field>
                            <Submit
                                serviceName={serviceName}
                                labelValidate={labelValidate}
                                onSubmit={onSubmit}
                                onCancel={onCancel}
                            />
                        </Form>
                    </Modal>)
            }
        </>
    );
}
