.label {
    width: 400px;

    .label-item {
        .label-item-content {
            width: 100%;
            display: flex;
            margin-bottom: 10px;
            justify-content: space-between;
            position: relative;

            & > div {
                display: flex;
                line-height: 30px;

                & > span {
                    white-space: nowrap;
                }

                &:nth-child(2) {
                    margin-left: 10px;
                    flex-grow: 0;
                }

                & > button {
                    position: absolute;
                    right: 0;
                }
            }
        }

        .error-msg {
            margin-bottom: 10px;
            margin-left: 50px;
            color: #e71515;
        }
    }
}
