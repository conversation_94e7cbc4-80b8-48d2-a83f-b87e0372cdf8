import React, {useCallback} from 'react';
import {Button, Modal} from '@osui/ui';
import {deleteServiceInstanceApi, ServiceInstanceListItemType} from '@/api/serviceAdmin';

export function DeleteInstance(props: {
    item: ServiceInstanceListItemType;
    instanceId: string;
    serviceId: string;
    refresh: () => void;
}) {
    const {instanceId, serviceId, item} = props;
    const onDelete = useCallback(
        () => {
            Modal.confirm({
                title: '删除确认',
                content: '确认删除实例吗？删除后无法恢复！',
                onOk() {
                    return deleteServiceInstanceApi({
                        instanceId,
                        serviceId,
                        serviceInstanceId: item.serviceInstanceId,
                    }).then(() => {
                        props?.refresh();
                    });
                },
            });
        },
        [instanceId, serviceId, item, props]
    );
    return <Button type="link" onClick={onDelete}>删除</Button>;
}
