
import {useMemo} from 'react';
import ProTable from '@baidu/icloud-ui-pro-table';
import {ICprom} from '@/api/cprom';
import {useCurrentServiceMeshInstance} from '@/modules/ServiceMeshInstance';
import CloseCpromButton from '../CloseCpromButton';
import OpenCpromButton from '../OpenCpromButton';
import {genColumns} from './columns';

interface IProps {
    cpromList: ICprom[];
    getCprom: () => void;
}
export default function CpromList({
    cpromList,
    getCprom,
}: IProps) {
    const serviceMeshInstance = useCurrentServiceMeshInstance();

    const isHostingMesh = useMemo(
        () => {
            return serviceMeshInstance?.type === 'hosting';
        },
        [serviceMeshInstance?.type]
    );

    const columns = genColumns(serviceMeshInstance);

    return (
        <ProTable
            rowKey="name"
            columns={columns as any}
            dataSource={cpromList || []}
            pagination={false}
            emptyDescription={
                <div>
                    <div>Prometheus 实例不存在或已被删除</div>
                    <div>
                        {/* eslint-disable-next-line max-len */}
                        你可以 <OpenCpromButton type="reselect" getCprom={getCprom} isHostingMesh={isHostingMesh} /> 或 <CloseCpromButton type="link" getCprom={getCprom} isHostingMesh={isHostingMesh} />
                    </div>
                </div>
            }
        />
    );
}
