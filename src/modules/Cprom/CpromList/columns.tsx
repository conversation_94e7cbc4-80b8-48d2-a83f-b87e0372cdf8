import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {ServiceMeshInstance} from '@/interface/serviceMesh';
import {isHostingMesh} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/util';
import {ICprom} from '@/api/cprom';
import {cpromInstanceTemplateMap} from './constant';
import c from './columns.less';

export const genColumns = (serviceMeshInstance: ServiceMeshInstance) => {
    return [
        {
            title: '实例名称',
            dataIndex: 'spec',
            render(value: any) {
                const {instanceName} = value ?? {};
                return instanceName;
            },
        },
        {
            title: '实例 ID',
            dataIndex: 'spec',
            render(value: any) {
                const {instanceID} = value ?? {};
                return instanceID;
            },
        },
        {
            title: '实例状态',
            dataIndex: 'status',
            render(value: any) {
                const {phase} = value ?? {};
                return phase;
            },
        },
        {
            title: '实例规格',
            dataIndex: 'metadata',
            render(value: any) {
                const cpromInstanceTemplate = value?.labels['cprom-instance-template'] ?? '';
                return cpromInstanceTemplateMap.get(cpromInstanceTemplate);
            },
        },
        {
            title: '存储时长',
            dataIndex: 'spec',
            render(value: any) {
                const {vmClusterConfig: {retentionPeriod}} = value ?? {};
                return `${retentionPeriod}`;
            },
        },
        {
            title: 'Grafana 服务',
            dataIndex: 'monitorGrafanaName',
            render(value: any, record: ICprom) {
                const {monitorGrafanaId} = record;
                return (
                    <ExternalLink
                        href={urls.external.cprom.grafanaDetail.fill({}, {monitorGrafanaId})}
                        value={value}
                    />
                );
            },
        },
        {
            title: '操作',
            dataIndex: 'operation',
            render(value: any, record: ICprom) {
                return (
                    <>
                        <ExternalLink
                            href={urls.external.cprom.clusterCollect.fill({}, {id: record?.spec?.instanceID})}
                            value={isHostingMesh(serviceMeshInstance.type) ? '查看详情' : '查看数据面/控制面采集任务'}
                        />

                        <ExternalLink
                            className={c['configAlarm']}
                            href={urls.external.instanceAlarm.fill({}, {id: record?.spec?.instanceID})}
                            value="配置告警"
                        />
                    </>
                );
            },
        },
    ];
};
