import {Select} from 'antd';
import {useCallback, useMemo} from 'react';
import {Button, Form, Modal, Tooltip, message} from '@osui/ui';
import {useBoolean, useRequestCallback} from 'huse';
import AddButton from '@/components/AddButton';
import ExternalLink from '@/components/ExternalLink';
import {getMonitorInstanceListApi} from '@/api/createServiceMesh';
import {updateCpromApi} from '@/api/cprom';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {useCurrentServiceMeshInstance} from '@/modules/ServiceMeshInstance';
import urls from '@/urls';
import {instanceRules} from './constant';
import c from './index.less';

interface IProps {
    type: 'guideline' | 'reselect';
    getCprom: () => void;
    isHostingMesh: boolean;
}
interface Payload {
    clusterId?: string;
    instanceId?: string;
}
export default function OpenCpromButton({
    type = 'guideline',
    getCprom,
    isHostingMesh,
}: IProps) {
    const [form] = Form.useForm();
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);
    const [confirmLoading, {on: onConfirmLoading, off: offConfirmLoading}] = useBoolean(false);

    const serviceMeshInstance = useCurrentServiceMeshInstance();
    const clusterId = useMemo(
        () => {
            return serviceMeshInstance?.ClusterInfo?.clusterId || '';
        },
        [serviceMeshInstance?.ClusterInfo?.clusterId]
    );

    const payload: Payload = {};
    if (isHostingMesh) {
        payload.instanceId = serviceMeshInstanceId;
    } else {
        payload.clusterId = clusterId;
    }

    const [
        getMonitorInstanceList,
        {pending: getMonitorInstanceListLoading, data: getMonitorInstanceListData},
    ] = useRequestCallback(getMonitorInstanceListApi, payload);

    const monitorInstanceOptions = useMemo(
        () => {
            return (getMonitorInstanceListData?.instances || []).map(v => {
                return {
                    label: (
                        <Tooltip
                            placement="right"
                            trigger={v.isInstallCPromAgent ? '' : 'hover'}
                            title={
                                <>
                                    该 CProm 关联的 CCE 集群没有安装 Agent。
                                    <ExternalLink
                                        href={urls.external.cprom.cpromInstanceCluster.fill({}, {id: v.id})}
                                        value="去安装"
                                    />
                                </>
                            }
                        >
                            {v.name}/{v.id}
                        </Tooltip>
                    ),
                    value: JSON.stringify(v),
                    disabled: !v.isInstallCPromAgent,
                };
            });
        },
        [getMonitorInstanceListData]
    );

    const openCprom = useCallback(
        () => {
            onVisible();
            getMonitorInstanceList();
        },
        [getMonitorInstanceList, onVisible]
    );

    const onClickOK = useCallback(
        async () => {
            try {
                const {instance = '{}'} = await form.validateFields();
                const {region = '', id = ''} = JSON.parse(instance);
                onConfirmLoading();
                const rsp = await updateCpromApi({serviceMeshInstanceId, enabled: true, instances: [{region, id}]});
                if (rsp) {
                    offVisible();
                    message.success('监控服务开启成功');
                    getCprom();
                }
                else {
                    message.error('监控服务开启失败');
                    return Promise.reject(new Error('监控服务开启失败'));
                }
            } catch (error) {
                console.error(error);
            } finally {
                offConfirmLoading();
            }
        },
        [serviceMeshInstanceId, form, getCprom, offConfirmLoading, offVisible, onConfirmLoading]
    );

    return (
        <div className={c['openCpromButton']}>
            {
                type === 'guideline'
                    ? (
                        <AddButton
                            type="primary"
                            action="create"
                            icon={null}
                            onClick={openCprom}
                        >
                            立即开启
                        </AddButton>
                    )
                    : <Button type="link" onClick={openCprom}>重新选择实例</Button>
            }

            <Modal
                title={type === 'guideline' ? '开启监控服务' : '选择监控实例'}
                visible={visible}
                confirmLoading={confirmLoading}
                onCancel={offVisible}
                onOk={onClickOK}
            >
                <Form
                    name="basic"
                    labelAlign="left"
                    form={form}
                >
                    <Form.Item
                        label="选择实例"
                        required
                        rules={instanceRules}
                        name={['instance']}
                        help={
                            <>
                                没有合适的监控实例，可以<ExternalLink href={urls.external.cpromInstanceList.fill()} value="去创建实例" />
                            </>
                        }
                    >
                        <Select
                            style={{width: '320px'}}
                            showSearch
                            placeholder="请选择"
                            loading={getMonitorInstanceListLoading}
                            options={monitorInstanceOptions}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
}
