import {Button, Modal, message} from '@osui/ui';
import {useCallback} from 'react';
import {ButtonType} from 'antd/lib/button';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {updateCpromApi} from '@/api/cprom';

interface IProps {
    type?: ButtonType;
    getCprom: () => void;
    isHostingMesh: boolean;
}
export default function CloseCpromButton({
    type = 'default',
    getCprom,
    isHostingMesh,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const deleteCprom = useCallback(
        () => {
            Modal.confirm({
                title: '确定关闭监控服务吗？',
                content: `关闭后，${isHostingMesh ? '控制面指标' : '数据面和控制面数据指标都'}将停止采集，将影响监控告警，请谨慎操作。`,
                async onOk() {
                    try {
                        const rsp = await updateCpromApi({serviceMeshInstanceId, enabled: false});
                        if (rsp) {
                            message.success('关闭监控服务成功');
                            getCprom();
                        }
                    } catch (error) {
                        message.error('关闭监控服务失败，请重试');
                        return Promise.reject(new Error('关闭监控服务失败，请重试'));
                    }
                },
                closable: true,
            });
        },
        [serviceMeshInstanceId, getCprom, isHostingMesh]
    );

    return (
        <Button
            type={type}
            onClick={deleteCprom}
        >
            关闭服务
        </Button>
    );
}
