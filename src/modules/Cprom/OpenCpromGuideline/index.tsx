import {Image} from 'antd';
import guidelinePng from '@/assets/cprom/guideline.png';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import OpenCpromButton from '../OpenCpromButton';
import c from './index.less';

interface IProps {
    getCprom: () => void;
    isHostingMesh: boolean;
}
export default function OpenCpromGuideline({
    getCprom,
    isHostingMesh,
}: IProps) {
    return (
        <div className={c['openCpromGuideline']}>
            <Image className={c['guidelinePng']} src={guidelinePng} preview={false} />
            <div className={c['title']}>Prometheus 监控{isHostingMesh ? ' —— 控制面监控' : ''}</div>
            <div className={c['description']}>
                <p>
                    {/* eslint-disable-next-line max-len */}
                    启用后，
                    {isHostingMesh ? '可以生成服务网格控制面istiod相关的指标数据' : '可以使服务网格数据面（Sidecar代理）及控制面生成与其运行状态相关的指标数据'}
                    ，可以通过将指标采集到百度智能云Prometheus监控服务来直接查看监控报表（采集指标可能产生费用），并可以在Prometheus监控服务配置相应指标的监控告警。更多信息查看
                    <ExternalLink
                        href={urls.external.csmDocCprom.fill()}
                        value="帮助文档"
                    />、
                    <ExternalLink
                        href={urls.external.cpromCostDetail.fill()}
                        value="计费规则"
                    />
                </p>
            </div>

            <OpenCpromButton isHostingMesh={isHostingMesh} type="guideline" getCprom={getCprom} />
        </div>
    );
}
