import Card from '@baidu/icloud-ui-card';
import {useRequestCallback} from 'huse';
import {Spin} from '@osui/ui';
import {useEffect, useMemo} from 'react';
import {Al<PERSON>, Button} from 'acud';
import {OutlinedRefresh} from '@baidu/acud-icon';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import ExternalLink from '@/components/ExternalLink';
import {getCpromApi} from '@/api/cprom';
import urls from '@/urls';
import {useCurrentServiceMeshInstance} from '@/modules/ServiceMeshInstance';
import CloseCpromButton from './CloseCpromButton';
import OpenCpromGuideline from './OpenCpromGuideline';
import CpromList from './CpromList';
import c from './index.less';

export default function Cprom() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const serviceMeshInstance = useCurrentServiceMeshInstance();
    const [
        getCprom,
        {pending: getCpromLoading, data: getCpromData},
    ] = useRequestCallback(getCpromApi, {serviceMeshInstanceId});

    useEffect(
        () => {
            getCprom();
        },
        [getCprom]
    );

    const cpromList = useMemo(
        () => {
            if (!getCpromData) {
                return [];
            }

            return [getCpromData];
        },
        [getCpromData]
    );

    const monitorReason = useMemo(
        () => {
            return getCpromData?.csmMonitor?.monitorReason || '';
        },
        [getCpromData?.csmMonitor?.monitorReason]
    );

    const isHostingMesh = useMemo(
        () => {
            return serviceMeshInstance?.type === 'hosting';
        },
        [serviceMeshInstance?.type]
    );

    const monitorReasonMap = useMemo(
        () => {
            const id = getCpromData?.spec?.instanceID;

            return new Map([
                [
                    'CPromAgentException',
                    {
                        content: '采集agent未安装，为了不影响使用Prometheus监控，请去检查安装',
                        url: urls.external.cprom.cpromInstanceCluster.fill({}, {id}),
                        value: '检查安装',
                    },
                ],
                [
                    'CSMEnvoyJobDeletedException',
                    {
                        content: '采集任务已被删除，为了不影响使用Prometheus监控，请去检查更新',
                        url: urls.external.cprom.clusterCollect.fill({}, {id}),
                        value: '检查更新',
                    },
                ],
            ]);
        },
        [getCpromData?.spec?.instanceID]
    );

    return (
        getCpromLoading
            ? (
                <div className={c['cpromWrapper']}>
                    <Spin />
                </div>
            )
            : getCpromData?.csmMonitor?.enabled === false
                ? (
                    <div className={c['cpromWrapper']}>
                        <OpenCpromGuideline getCprom={getCprom} isHostingMesh={isHostingMesh} />
                    </div>
                )
                : (
                    <Card
                        className={c['cprom']}
                        title={
                            <div className={c['title-wrapper']}>
                                <div className={c['title']}>监控服务（Prometheus）</div>
                                <div className={c['subtitle']}>
                                    支持{isHostingMesh ? '控制面Istiod' : '数据面和控制面'}的监控报警，更多信息可查看
                                    <ExternalLink href={urls.external.csmDocCprom.fill()} value="帮助文档" />、
                                    <ExternalLink href={urls.external.cpromCostDetail.fill()} value="计费规则" />
                                </div>
                            </div>
                        }
                        extra={
                            <div className={c['extra']}>
                                <Button className={c['refresh']} onClick={getCprom}>
                                    <OutlinedRefresh />
                                </Button>

                                <CloseCpromButton getCprom={getCprom} isHostingMesh={isHostingMesh} />
                            </div>
                        }
                    >
                        {
                            // eslint-disable-next-line max-len
                            ['CPromAgentException', 'CSMEnvoyJobDeletedException'].includes(monitorReason)
                                ? (
                                    <Alert
                                        className={c['alert']}
                                        message={
                                            <div className={c['content']}>
                                                <div>
                                                    {monitorReasonMap.get(monitorReason)?.content}
                                                </div>
                                                <ExternalLink
                                                    href={monitorReasonMap.get(monitorReason)?.url}
                                                    value={monitorReasonMap.get(monitorReason)?.value}
                                                />
                                            </div>
                                        }
                                        type="error"
                                    />
                                )
                                : null
                        }

                        <CpromList
                            cpromList={monitorReason === 'CPromInstanceDeletedException' ? [] : cpromList}
                            getCprom={getCprom}
                        />
                    </Card>
                )
    );
}
