import ProTable, {IColumns} from '@baidu/icloud-ui-pro-table';
import urls from '@/urls';

export default function generateColumns(serviceMeshInstanceId: string): IColumns {
    return [
        {
            title: '访问地址',
            dataIndex: 'host',
            render(host) {
                return (
                    <ProTable.OperationsWrapper>
                        <ProTable.OperationLink
                            to={urls.serviceDetail.fill({serviceMeshInstanceId, host})}
                        >
                            {host}
                        </ProTable.OperationLink>
                    </ProTable.OperationsWrapper>
                );
            },
        },
        {
            title: 'K8S Service',
            dataIndex: 'k8sServiceCount',
        },
        {
            title: 'Service Entry',
            dataIndex: 'serviceEntryCount',
            renderType: 'text',
        },
    ];
}
