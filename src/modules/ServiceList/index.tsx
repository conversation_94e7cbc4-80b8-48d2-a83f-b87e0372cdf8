import React, {useCallback, useMemo} from 'react';
import ProTable, {IFilters, IOperations} from '@baidu/icloud-ui-pro-table';
import Card from '@baidu/icloud-ui-card';
import {Alert} from '@osui/ui';
import styled from 'styled-components';
import {getServiceListApi} from '@/api/service';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {SERVICE_EXAMPLE_LINK} from '@/links';
import ExternalLink from '@/components/ExternalLink';
import generateColumns from './columns';
const AlertContainer = styled(Alert)`margin-bottom: 8px;`;
export default function ServiceList() {

    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    // @todo 这个类型报警要好好看下
    const getList = useCallback(
        async params => {
            const {result, totalCount} = await getServiceListApi({
                serviceMeshInstanceId,
                ...params,
                keywordType: 'host',
                keyword: params.host,
            });
            return {result, totalCount};
        },
        [serviceMeshInstanceId]
    );
    const operations: IOperations = [
        {
            name: ['host'],
            renderType: 'search',
            props: {
                placeholder: '请输入访问地址进行搜索',
            },
        },
    ];
    const filters: IFilters = [
        {
            renderType: 'refresh',
        },
    ];
    const columns = useMemo(
        () => generateColumns(serviceMeshInstanceId),
        [serviceMeshInstanceId]
    );
    return (
        <Card title="服务列表">
            <AlertContainer
                showIcon
                message={(
                    <>
                        <div>
                            服务网格将通过同一访问地址提供服务的多个Kubernetes Service和Service Entry视为一个服务，
                            进行多集群的统一流量调度，参考
                            <ExternalLink href={SERVICE_EXAMPLE_LINK} value="服务示例" />。
                        </div>
                    </>
                )}
                type="info"
            />
            <ProTable
                rowKey="host"
                columns={columns}
                request={getList}
                operations={operations}
                filters={filters}
            />
        </Card>
    );
}
