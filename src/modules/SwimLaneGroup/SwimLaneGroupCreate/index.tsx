import {useCallback, useState} from 'react';
import {Form, Image, Modal, Steps, message} from '@osui/ui';
import {useBoolean} from 'huse';
import {cloneDeep} from 'lodash';
import {createSwimLaneGroupApi} from '@/api/swimLaneGroup';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import AddButton from '@/components/AddButton';
import guidelinePng from '@/assets/swimLane/guideline.png';
import BasicConfigForm from './BasicConfigForm';
import FooterBtn from './FooterBtn';
import BaseLaneConfigForm from './BaseLaneConfigForm';
import {baseLaneConfigFormValueDft, basicConfigFormValueDft} from './constant';
import c from './index.less';

const {Step} = Steps;

type TCreateType = 'fromList' | 'fromGuideline';
interface IProps {
    type: TCreateType;
    getSwimLaneGroupList: () => void;
}
export default function SwimLaneGroupCreate({
    type,
    getSwimLaneGroupList,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const [basicConfigFormInstante] = Form.useForm();
    const [baseLaneConfigFormInstante] = Form.useForm();

    const [isSubmitLoading, {on: onSubmitLoading, off: offSubmitLoading}] = useBoolean(false);

    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);

    const [stepIndex, setStepIndex] = useState(0);

    const clickPreStep = useCallback(
        () => {
            setStepIndex(0);
        },
        []
    );

    const clickNextStep = useCallback(
        async () => {
            try {
                await basicConfigFormInstante.validateFields();
                setStepIndex(1);
            } catch (error) {
                console.error(error);
            }
        },
        [basicConfigFormInstante]
    );

    const clickSubmit = useCallback(
        async () => {
            try {
                const basicConfigFormValues = await basicConfigFormInstante.validateFields();
                const baseLaneConfigFormValues = await baseLaneConfigFormInstante.validateFields();
                const basicConfigFormValuesReq = cloneDeep(basicConfigFormValues);
                const baseLaneConfigFormValuesReq = cloneDeep(baseLaneConfigFormValues);

                const serviceList = basicConfigFormValuesReq?.serviceList?.map((v: string) => JSON.parse(v));
                basicConfigFormValuesReq.serviceList = serviceList;
                baseLaneConfigFormValuesReq.baseLane.serviceList = serviceList;

                if (basicConfigFormValuesReq.traceHeaderChecked) {
                    basicConfigFormValuesReq.routeHeader = basicConfigFormValuesReq.traceHeader;
                }
                delete basicConfigFormValuesReq.traceHeaderChecked;

                onSubmitLoading();
                const rsp = await createSwimLaneGroupApi({
                    serviceMeshInstanceId,
                    ...basicConfigFormValuesReq,
                    ...baseLaneConfigFormValuesReq,
                });
                if (rsp) {
                    // 表单项等数据的重置
                    basicConfigFormInstante.setFieldsValue(basicConfigFormValueDft);
                    baseLaneConfigFormInstante.setFieldsValue(baseLaneConfigFormValueDft);
                    setStepIndex(0);
                    offVisible();
                    getSwimLaneGroupList();
                    message.success('新建流量泳道组成功');
                }
            } catch (error) {
                console.error(error);
                message.error('新建流量泳道组失败，请重新操作');
            } finally {
                offSubmitLoading();
            }
        },
        [
            serviceMeshInstanceId, basicConfigFormInstante, baseLaneConfigFormInstante,
            onSubmitLoading, offSubmitLoading, offVisible, getSwimLaneGroupList,
        ]
    );

    return (
        <>
            {
                type === 'fromList'
                    ? (
                        <AddButton
                            type="link"
                            action="create"
                            onClick={onVisible}
                        >
                            新建
                        </AddButton>
                    )
                    : (
                        <div className={c['createFromGuideline']}>
                            <Image className={c['guidelinePng']} src={guidelinePng} preview={false} />
                            <div className={c['title']}>你还没有创建任何流量泳道</div>
                            <div className={c['description']}>
                                {
                                    [
                                        '流量泳道是一种流量管理技术，用于将流量按照不同的需求或优先级分配到不同的服务或应用程序中。',
                                        '具体来说，流量泳道将流量划分为不同的路径或“泳道”，每个泳道对应一个特定的服务或应用程序。',
                                        '根据预设的规则和策略，流量将被动态地分配到不同的泳道中，以满足不同的业务需求和性能目标。',
                                    ].join('')
                                }
                            </div>
                            <AddButton
                                className={c['addButton']}
                                type="primary"
                                action="create"
                                onClick={onVisible}
                            >
                                立即新建
                            </AddButton>
                        </div>
                    )
            }

            <Modal
                className={c['modal']}
                width={800}
                title="新建流量泳道组"
                visible={visible}
                footer={null}
                onCancel={offVisible}
            >
                <Steps current={stepIndex}>
                    <Step title="基本配置" />
                    <Step title="基线泳道" />
                </Steps>

                <div>
                    <BasicConfigForm
                        style={{display: stepIndex === 0 ? 'block' : 'none'}}
                        basicConfigFormInstante={basicConfigFormInstante}
                    />
                    <BaseLaneConfigForm
                        stepIndex={stepIndex}
                        style={{display: stepIndex === 1 ? 'block' : 'none'}}
                        basicConfigFormInstante={basicConfigFormInstante}
                        baseLaneConfigFormInstante={baseLaneConfigFormInstante}
                    />
                </div>

                <FooterBtn
                    stepIndex={stepIndex}
                    isSubmitLoading={isSubmitLoading}
                    clickCancel={offVisible}
                    clickPreStep={clickPreStep}
                    clickNextStep={clickNextStep}
                    clickSubmit={clickSubmit}
                />
            </Modal>
        </>
    );
}
