import {Form, FormInstance, Input, Select} from '@osui/ui';
import {HTMLAttributes, useCallback, useEffect, useState} from 'react';
import {useRequestCallback} from 'huse';
import {DefaultOptionType} from 'antd/lib/select';
import {ILaneGroupLabelItem, getLaneGroupLabelListApi} from '@/api/swimLaneGroup';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {genServiceListForRequest} from '../../util';
import {
    useLabelSelectorHelp,
    labelSelectorKeyRules,
    labelSelectorValueRules,
    laneNameHelp,
    laneNameRules,
} from './constant';
import c from './index.less';

interface IProps extends HTMLAttributes<HTMLElement> {
    stepIndex: number;
    basicConfigFormInstante: FormInstance;
    baseLaneConfigFormInstante: FormInstance;
}
export default function BaseLaneConfigForm({
    stepIndex,
    basicConfigFormInstante,
    baseLaneConfigFormInstante,
    ...props
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const [laneGroupLabelList, setLaneGroupLabelList] = useState<ILaneGroupLabelItem[]>([]);
    const [key, setKey] = useState(undefined);
    const [value, setValue] = useState(undefined);
    const [keyOptions, setKeyOptions] = useState<DefaultOptionType[]>([]);
    const [valueOptions, setValueOptions] = useState<DefaultOptionType[]>([]);

    const serviceList = basicConfigFormInstante.getFieldValue(['serviceList'])
        ?.map((v: string) => JSON.parse(v)) || [];

    const [
        getLaneGroupLabelList,
        {data: laneGroupLabelListData, pending: laneGroupLabelListLoading},
    ] = useRequestCallback(
        getLaneGroupLabelListApi,
        {
            serviceMeshInstanceId,
            serviceList: genServiceListForRequest(serviceList),
        }
    );

    useEffect(
        () => {
            if (laneGroupLabelListData) {
                setLaneGroupLabelList(laneGroupLabelListData?.result ?? []);
                const keyList = [...new Set(laneGroupLabelList.map(v => v.key))];
                const valueList = [
                    ...new Set(laneGroupLabelList.map(v => v.valueList).reduce((acc, cur) => [...acc, ...cur], [])),
                ];
                setKeyOptions(keyList.map(v => ({label: v, value: v})));
                setValueOptions(valueList.map(v => ({label: v, value: v})));
            }
        },
        [laneGroupLabelList, laneGroupLabelListData]
    );

    const changeKey = useCallback(
        v => {
            setKey(v);
            const valueList = [
                ...new Set(
                    laneGroupLabelList.filter(item => !v || item.key === v)
                        .map(v => v.valueList).reduce((acc, cur) => [...acc, ...cur], [])
                ),
            ];
            const valueListOptions = valueList.map(v => ({label: v, value: v}));
            setValueOptions(valueListOptions);
        },
        [laneGroupLabelList]
    );

    const changeValue = useCallback(
        v => {
            setValue(v);
            const keyList = [
                ...new Set(
                    laneGroupLabelList.filter(item => !v || item.valueList.includes(v))
                        .map(v => v.key)
                ),
            ];
            const keyListOptions = keyList.map(v => ({label: v, value: v}));
            setKeyOptions(keyListOptions);
        },
        [laneGroupLabelList]
    );

    useEffect(
        () => {
            if (stepIndex === 1) {
                getLaneGroupLabelList();
                baseLaneConfigFormInstante.setFieldsValue({
                    baseLane: {
                        labelSelectorKey: undefined,
                        labelSelectorValue: undefined,
                    },
                });
            }
        },
        [stepIndex, baseLaneConfigFormInstante, getLaneGroupLabelList]
    );

    const labelSelectorHelp = useLabelSelectorHelp();

    return (
        <Form
            className={c['baseLaneConfigForm']}
            name="basic"
            {...props}
            labelAlign="left"
            form={baseLaneConfigFormInstante}
        >
            <Form.Item
                label="泳道名称"
                required
                rules={laneNameRules}
                name={['baseLane', 'laneName']}
                help={laneNameHelp}
            >
                <Input
                    style={{width: 338}}
                    maxLength={65}
                    showCount
                    placeholder="请输入泳道名称"
                />
            </Form.Item>

            <Form.Item
                label="标签"
                required
                help={labelSelectorHelp}
            >
                <div className={c['labelSelector']}>
                    <Form.Item
                        rules={labelSelectorKeyRules}
                        name={['baseLane', 'labelSelectorKey']}
                        valuePropName="value"
                    >
                        <Select
                            style={{width: 222}}
                            placeholder="请选择标签键"
                            loading={laneGroupLabelListLoading}
                            options={keyOptions}
                            showSearch
                            allowClear
                            value={key}
                            onChange={changeKey}
                        />
                    </Form.Item>

                    <Form.Item
                        className={c['labelSelectorValue']}
                        rules={labelSelectorValueRules}
                        name={['baseLane', 'labelSelectorValue']}
                    >
                        <Select
                            style={{width: 222}}
                            placeholder="请选择标签值"
                            loading={laneGroupLabelListLoading}
                            options={valueOptions}
                            showSearch
                            allowClear
                            value={value}
                            onChange={changeValue}
                        />
                    </Form.Item>
                </div>
            </Form.Item>

            <Form.Item
                label="泳道服务"
                help="基线泳道默认包含泳道组的所有服务"
            >
                <Form.Item
                    shouldUpdate
                    noStyle
                >
                    {
                        () => {
                            const serviceList = basicConfigFormInstante.getFieldValue(['serviceList']) ?? [];
                            return (
                                <>
                                    {
                                        serviceList?.map((v: string) => {
                                            const {serviceName, namespace} = JSON.parse(v);
                                            return `${serviceName}(${namespace})`;
                                        }).join('、')
                                    }
                                </>
                            );
                        }
                    }
                </Form.Item>
            </Form.Item>
        </Form>
    );
}
