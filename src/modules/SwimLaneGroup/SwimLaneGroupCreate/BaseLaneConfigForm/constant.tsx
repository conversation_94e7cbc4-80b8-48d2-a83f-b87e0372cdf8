import {Rule} from 'antd/lib/form';
import {useRequest} from 'huse';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getServiceMeshInstanceApi} from '@/api/serviceMesh';

export const laneNameHelp = (
    <>
        泳道名称需要与当前
        <ExternalLink
            href={urls.external.swimLaneGroup.fill()}
            value="泳道的引流请求头 value 值"
        />
        保持一致
    </>
);

export const useLabelSelectorHelp = () => {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {data} = useRequest(getServiceMeshInstanceApi, {
        serviceMeshInstanceId,
    });

    return (
        <>
            标签配置需与泳道内的服务相匹配，若没有所需标签，可以
            <ExternalLink
                href={
                    urls.external.cce.deploymentList.fill(
                        {},
                        {clusterUuid: data?.ClusterInfo?.clusterId || '', namespaceName: 'all'}
                    )
                }
                value="去创建标签"
            />
        </>
    );
};

export const laneNameRules: Rule[] = [
    {required: true, message: '请输入泳道名称'},
];

export const labelSelectorKeyRules: Rule[] = [
    {required: true, message: '请选择标签键'},
];

export const labelSelectorValueRules: Rule[] = [
    {required: true, message: '请选择标签值'},
];
