import {ILaneServiceListItem} from '@/api/swimLaneGroup';

export const columns = [
    {
        title: '服务名称',
        dataIndex: 'serviceName',
        key: 'serviceName',
        width: 100,
    },
    {
        title: '命名空间',
        dataIndex: 'namespace',
        key: 'namespace',
        width: 100,
    },
    {
        title: '集群名称/ID',
        dataIndex: 'clusterName',
        key: 'clusterName',
        width: 120,
        render: (value: string, record: ILaneServiceListItem) => {
            return `${record.clusterName} / ${record.clusterID}`;
        },
    },
];
