import {Checkbox, Form, FormInstance, Input, Popover, Spin, Tooltip} from '@osui/ui';
import {useRequestCallback} from 'huse';
import {HTMLAttributes, useCallback, useEffect, useMemo, useState} from 'react';
import {Transfer} from 'acud';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getLaneServiceListApi} from '@/api/swimLaneGroup';
import {columns} from './columns';
import {
    groupNameRules,
    routeHeaderRules,
    routeHeaderTip,
    serviceListRules,
    traceHeaderRules,
    traceHeaderTip,
} from './constant';
import c from './index.less';

interface IProps extends HTMLAttributes<HTMLElement> {
    basicConfigFormInstante: FormInstance;
}
export default function BasicConfigForm({
    basicConfigFormInstante,
    ...props
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [targetKeys, setTargetKeys] = useState<string[]>([]);
    const serviceListFromForm = basicConfigFormInstante.getFieldValue(['serviceList']);

    const changeTraceHeader = useCallback(
        e => {
            const {value = ''} = e?.target || {};
            const traceHeaderChecked = basicConfigFormInstante.getFieldValue(['traceHeaderChecked']);
            if (value && traceHeaderChecked) {
                basicConfigFormInstante.setFieldsValue({
                    routeHeader: value,
                });
            }
        },
        [basicConfigFormInstante]
    );

    const changeTraceHeaderChecked = useCallback(
        e => {
            if (e?.target?.checked) {
                basicConfigFormInstante.setFieldsValue({
                    routeHeader: basicConfigFormInstante.getFieldValue(['traceHeader']),
                });
            }
        },
        [basicConfigFormInstante]
    );

    useEffect(
        () => {
            if (!serviceListFromForm || !serviceListFromForm?.length) {
                setTargetKeys([]);
            }
        },
        [serviceListFromForm]
    );

    const onTransferChange = useCallback(
        (nextTargetKeys: string[]) => {
            setTargetKeys(nextTargetKeys);
        },
        []
    );

    const [
        getLaneServiceList,
        {pending: serviceListLoading, data: serviceList},
    ] = useRequestCallback(
        getLaneServiceListApi,
        {
            serviceMeshInstanceId,
        }
    );

    const transferDataSource = useMemo(
        () => {
            if (serviceListLoading || !serviceList?.result) {
                return [];
            }

            return serviceList.result.map(v => ({
                ...v,
                key: JSON.stringify(v),
            }));
        },
        [serviceList, serviceListLoading]
    );

    useEffect(
        () => {
            getLaneServiceList();
        },
        [getLaneServiceList]
    );

    return (
        <Form
            className={c['basicConfigForm']}
            name="basic"
            {...props}
            labelAlign="left"
            form={basicConfigFormInstante}
        >
            <Form.Item
                label="泳道组名称"
                required
                rules={groupNameRules}
                name={['groupName']}
                help={
                    <>
                        <span className={c['helpHighlight']}>创建后不支持修改，必须以字母开头，</span>
                        支持大小写字母、数字、中文以及-_ /.特殊字符
                    </>
                }
            >
                <Input
                    style={{width: 338}}
                    maxLength={65}
                    showCount
                    placeholder="请输入泳道组名称"
                />
            </Form.Item>

            <Form.Item
                label="请求头设定"
                required
                help="请保证存在一个在整体链条中透传，且每次请求值不相同的请求头(链路透传请求头)，同时指定一个用于将请求匹配至不同泳道的请求头(引流请求头）"
            >
                <div className={c['header']}>
                    <Form.Item
                        label={
                            <>
                                链路透传请求头：{
                                    <Popover content={traceHeaderTip}>
                                        <QuestionCircleOutlined />
                                    </Popover>
                                }
                            </>
                        }
                        colon={false}
                    >
                        <Form.Item
                            name={['traceHeader']}
                            rules={traceHeaderRules}
                        >
                            <Input
                                style={{width: 460}}
                                placeholder="请输入链路透传请求头"
                                onChange={changeTraceHeader}
                            />
                        </Form.Item>
                        <Form.Item
                            name={['traceHeaderChecked']}
                            valuePropName="checked"
                        >
                            <Checkbox onChange={changeTraceHeaderChecked}>将链路透传请求头同时作为引流请求头</Checkbox>
                        </Form.Item>
                    </Form.Item>

                    <Form.Item
                        shouldUpdate
                        noStyle
                    >
                        {
                            ({getFieldsValue}) => {
                                const {
                                    traceHeaderChecked,
                                    traceHeader,
                                    routeHeader,
                                } = getFieldsValue();
                                const routeHeaderValue = traceHeaderChecked ? traceHeader : routeHeader;

                                return (
                                    <Form.Item
                                        label={
                                            <>
                                                引流请求头：{
                                                    <Popover content={routeHeaderTip}>
                                                        <QuestionCircleOutlined />
                                                    </Popover>
                                                }
                                            </>
                                        }
                                        colon={false}
                                        name={['routeHeader']}
                                        rules={traceHeaderChecked ? [] : routeHeaderRules}
                                        required={false}
                                    >
                                        {
                                            traceHeaderChecked
                                                ? (
                                                    <Tooltip placement="top" title="已选择与链路透传请求头保持一致">
                                                        <Input
                                                            style={{width: 460}}
                                                            readOnly
                                                            placeholder="请输入引流请求头"
                                                            value={routeHeaderValue}
                                                        />
                                                    </Tooltip>
                                                )
                                                : (
                                                    <Input
                                                        style={{width: 460}}
                                                        placeholder="请输入引流请求头"
                                                    />
                                                )
                                        }
                                    </Form.Item>
                                );
                            }
                        }
                    </Form.Item>
                </div>
            </Form.Item>

            <Form.Item
                label="泳道服务"
                required
                name={['serviceList']}
                rules={serviceListRules}
            >
                {
                    serviceListLoading
                        ? <Spin />
                        : (
                            <Transfer
                                displayType="table"
                                columns={columns as any}
                                targetKeys={targetKeys}
                                showSearch
                                dataSource={transferDataSource}
                                leftStyle={{width: 302}}
                                rightStyle={{width: 302}}
                                onChange={onTransferChange}
                            />
                        )
                }
            </Form.Item>
        </Form>
    );
}
