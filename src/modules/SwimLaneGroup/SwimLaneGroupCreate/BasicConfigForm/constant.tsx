import {Rule} from 'antd/lib/form';

export const traceHeaderTip = '在服务链路中沿着请求路径传递的HTTP头部信息，这些header信息通常包括用于跟踪、日志记录或安全目的的数据，如跟踪ID（trace ID）。';
// eslint-disable-next-line max-len
export const routeHeaderTip = '专门用于控制和管理流量分配的HTTP头部信息，特别是在灰度发布或A/B测试中。通过设置引流header，可以根据特定的标识（如版本号或用户群组）将流量引导至不同的服务版本或实例。';

export const groupNameRules: Rule[] = [
    {required: true, message: ''},
    {pattern: /^[a-zA-Z][\w-/.\u4e00-\u9fa5]{0,64}$/, message: ''},
];

export const traceHeaderRules: Rule[] = [
    {required: true, message: '请输入链路透传请求头'},
];

export const routeHeaderRules: Rule[] = [
    {required: true, message: '请输入引流请求头'},
];

export const serviceListRules: Rule[] = [
    {required: true, message: '请至少选择1个服务'},
];
