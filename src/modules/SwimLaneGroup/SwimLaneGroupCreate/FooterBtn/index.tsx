import {Button} from '@osui/ui';
import c from './index.less';

interface IProps {
    stepIndex: number;
    isSubmitLoading: boolean;
    clickCancel: () => void;
    clickPreStep: () => void;
    clickNextStep: () => void;
    clickSubmit: () => void;
}
export default function FooterBtn({
    stepIndex,
    isSubmitLoading,
    clickCancel,
    clickPreStep,
    clickNextStep,
    clickSubmit,
}: IProps) {
    return (
        <div className={c['footerBtn']}>
            {
                stepIndex === 0
                    ? <Button onClick={clickCancel}>取消</Button>
                    : <Button onClick={clickPreStep}>上一步</Button>
            }

            {
                stepIndex === 0
                    ? <Button className={c['next']} type="primary" onClick={clickNextStep}>下一步</Button>
                    : (
                        <Button
                            className={c['confirm']}
                            type="primary"
                            loading={isSubmitLoading}
                            onClick={clickSubmit}
                        >
                            确定
                        </Button>
                    )
            }
        </div>
    );
}
