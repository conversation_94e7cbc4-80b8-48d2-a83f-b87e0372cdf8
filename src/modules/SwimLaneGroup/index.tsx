import {useEffect} from 'react';
import {useRequestCallback} from 'huse';
import {Spin} from '@osui/ui';
import {getSwimLaneGroupListApi} from '@/api/swimLaneGroup';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import SwimLaneGroupList from './SwimLaneGroupList';
import SwimLaneGroupCreate from './SwimLaneGroupCreate';
import c from './index.less';

export default function SwimLaneGroup() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const [getSwimLaneGroupList, {pending: swimLaneGroupListLoading, data: swimLaneGroupList}] = useRequestCallback(
        getSwimLaneGroupListApi,
        {serviceMeshInstanceId}
    );

    useEffect(
        () => {
            getSwimLaneGroupList();
        },
        [getSwimLaneGroupList]
    );

    return (
        <div className={c['swimLaneGroupWrapper']}>
            {
                swimLaneGroupListLoading
                    ? <Spin className={c['loading']} />
                    : swimLaneGroupList?.totalCount === 0
                        ? (
                            <div className={c['guideline']}>
                                <SwimLaneGroupCreate
                                    type="fromGuideline"
                                    getSwimLaneGroupList={getSwimLaneGroupList}
                                />
                            </div>
                        )
                        : (
                            <SwimLaneGroupList
                                swimLaneGroupList={swimLaneGroupList as any}
                                getSwimLaneGroupList={getSwimLaneGroupList}
                            />
                        )
            }
        </div>
    );
}
