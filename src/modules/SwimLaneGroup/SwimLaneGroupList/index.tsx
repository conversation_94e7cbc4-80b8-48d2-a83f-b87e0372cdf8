import {OutlinedMore} from '@baidu/acud-icon';
import {Modal, Tabs, message} from '@osui/ui';
import {useCallback, useEffect, useMemo, useState} from 'react';
import Text from '@baidu/icloud-ui-text';
import {Dropdown, Menu} from 'acud';
import {QueryResponseBase} from '@/interface';
import {ICreateSwimLaneGroupParams, deleteSwimLaneGroupApi} from '@/api/swimLaneGroup';
import {getUrlSearchByUpdateQuery, useQuery} from '@/hooks/useUrl';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import SwimLaneGroupCreate from '../SwimLaneGroupCreate';
import BasicInfo from './BasicInfo';
import SwimLane from './SwimLane';
import c from './index.less';

type TActiveKey = 'basicInfo' | 'swimLane';
interface IProps {
    swimLaneGroupList: QueryResponseBase<ICreateSwimLaneGroupParams[]>;
    getSwimLaneGroupList: () => void;
}
export default function SwimLaneGroupList({
    swimLaneGroupList,
    getSwimLaneGroupList,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {laneGroupID: laneGroupIDFromQuery} = useQuery<{ laneGroupID: string }>();
    const [laneGroupID, setLaneGroupID] = useState(laneGroupIDFromQuery);
    const [activeKey, setActiveKey] = useState<TActiveKey>('basicInfo');

    useEffect(
        () => {
            if (!laneGroupID && swimLaneGroupList?.result?.length > 0) {
                setLaneGroupID(swimLaneGroupList?.result[0].groupID || '');
            }
        },
        [laneGroupID, swimLaneGroupList]
    );


    const clickSwimLaneGroup = useCallback(
        (v: ICreateSwimLaneGroupParams) => {
            const {groupID} = v;
            if (groupID) {
                setLaneGroupID(groupID);
                const urlSearch = getUrlSearchByUpdateQuery('laneGroupID', groupID);
                window.history.replaceState(null, '', urlSearch);
            }
        },
        []
    );

    const clickDeleteSwimLaneGroup = useCallback(
        (v: ICreateSwimLaneGroupParams) => {
            Modal.confirm({
                title: '确定删除泳道组吗？',
                content: (
                    <div>
                        删除“{v.groupName}”后，删除泳道组时将会连带泳道组内泳道一并删除，如果已经在灰度进行中，不会全走灰度，流量不可控!
                    </div>
                ),
                async onOk() {
                    const rsp = await deleteSwimLaneGroupApi({serviceMeshInstanceId, laneGroupID: v.groupID || ''});
                    if (rsp) {
                        message.success(`“${v.groupName}”删除成功`);
                        getSwimLaneGroupList();
                    }
                    else {
                        message.error(`“${v.groupName}”删除失败，请重新操作。`);
                        return Promise.reject(new Error('删除失败，请重新操作。'));
                    }
                },
                closable: true,
            });
        },
        [serviceMeshInstanceId, getSwimLaneGroupList]
    );

    const tabsItems = useMemo(
        () => {
            return [
                {
                    key: 'basicInfo',
                    label: '基本信息',
                    children: activeKey === 'basicInfo'
                        && <BasicInfo swimLaneGroupList={swimLaneGroupList} laneGroupID={laneGroupID} />,
                },
                {
                    key: 'swimLane',
                    label: '流量泳道',
                    children: activeKey === 'swimLane'
                        && <SwimLane swimLaneGroupList={swimLaneGroupList} laneGroupID={laneGroupID} />,
                },
            ];
        },
        [activeKey, swimLaneGroupList, laneGroupID]
    );

    return (
        <div className={c['swimLaneGroupList']}>
            <div className={c['nameList']}>
                <div className={c['header']}>
                    <div className={c['title']}>流量泳道组</div>
                    <SwimLaneGroupCreate type="fromList" getSwimLaneGroupList={getSwimLaneGroupList} />
                </div>
                <div>
                    {
                        (swimLaneGroupList?.result || []).map(
                            v => (
                                <div
                                    key={v.groupID}
                                    className={`${c['swimLaneGroup']} ${v.groupID === laneGroupID ? c['active'] : ''}`}
                                >
                                    <div
                                        className={c['groupName']}
                                        onClick={() => clickSwimLaneGroup(v)}
                                    >
                                        {
                                            v.groupName.length > 10
                                                ? (
                                                    <Text tooltip ellipsis>
                                                        {v.groupName}
                                                    </Text>
                                                )
                                                : v.groupName
                                        }
                                    </div>

                                    <Dropdown
                                        label={<OutlinedMore className={c['more']} />}
                                        overlay={
                                            <Menu style={{width: 52}}>
                                                {/* eslint-disable-next-line react/jsx-no-bind */}
                                                <Menu.Item onClick={() => clickDeleteSwimLaneGroup(v)}>
                                                    删除
                                                </Menu.Item>
                                            </Menu>
                                        }
                                        placement="bottomCenter"
                                    />
                                </div>
                            )
                        )
                    }
                </div>
            </div>

            {
                laneGroupID
                && (
                    <div className={c['basicInfoWrapper']}>
                        <Tabs
                            items={tabsItems}
                            activeKey={activeKey}
                            onChange={setActiveKey as any}
                        />
                    </div>
                )
            }
        </div>
    );
}
