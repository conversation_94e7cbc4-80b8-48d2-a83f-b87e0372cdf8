import Card from '@baidu/icloud-ui-card';
import {Row, Col} from '@osui/ui';
import {useMemo} from 'react';
import ProTable from '@baidu/icloud-ui-pro-table';
import {ICreateSwimLaneGroupParams} from '@/api/swimLaneGroup';
import {QueryResponseBase} from '@/interface';
import {genColumns} from './columns';
import c from './index.less';

interface IProps {
    swimLaneGroupList: QueryResponseBase<ICreateSwimLaneGroupParams[]>;
    laneGroupID: string;
}
export default function BasicInfo({
    swimLaneGroupList,
    laneGroupID,
}: IProps) {
    const laneGroup = useMemo(
        () => {
            const laneGroupTmp = swimLaneGroupList?.result?.find(v => v.groupID === laneGroupID);
            return laneGroupTmp || {};
        },
        [swimLaneGroupList, laneGroupID]
    ) as ICreateSwimLaneGroupParams;

    const datasource = useMemo(
        () => {
            return laneGroup.serviceList ?? [];
        },
        [laneGroup]
    );

    const columns = useMemo(
        () => {
            return genColumns(datasource);
        },
        [datasource]
    );

    return (
        <div className={c['basicInfo']}>
            <Row className={c['groupNameWrapper']}>
                <Col span={8}>
                    <Card.Field title="泳道组名称">
                        {laneGroup.groupName ?? '--'}
                    </Card.Field>
                </Col>
                <Col span={8}>
                    <Card.Field title="链路透传请求头">
                        {laneGroup.traceHeader ?? '--'}
                    </Card.Field>
                </Col>
                <Col span={8}>
                    <Card.Field title="引流请求头">
                        {laneGroup.routeHeader ?? '--'}
                    </Card.Field>
                </Col>
            </Row>

            <Card title="泳道组服务">
                <ProTable
                    columns={columns as any}
                    dataSource={datasource}
                    requestConfig={{pageByServer: false}}
                />
            </Card>
        </div>
    );
}
