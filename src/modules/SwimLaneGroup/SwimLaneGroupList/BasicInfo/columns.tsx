import {ILaneServiceListItem} from '@/api/swimLaneGroup';
import {getRowSpan} from '@/utils/table';

export const genColumns = (datasource: ILaneServiceListItem[]) => {
    return [
        {
            title: '服务名称',
            dataIndex: 'serviceName',
            onCell: (record: ILaneServiceListItem, index: number) => {
                return {rowSpan: getRowSpan(datasource, index, record, 'serviceName')};
            },
        },
        {
            title: '命名空间',
            dataIndex: 'namespace',
            onCell: (record: ILaneServiceListItem, index: number) => {
                return {rowSpan: getRowSpan(datasource, index, record, 'namespace')};
            },
        },
        {
            title: '所属集群',
            dataIndex: 'clusterName',
            onCell: (record: ILaneServiceListItem, index: number) => {
                return {rowSpan: getRowSpan(datasource, index, record, 'clusterName')};
            },
        },
    ];
};
