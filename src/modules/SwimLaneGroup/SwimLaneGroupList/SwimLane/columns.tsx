import ProTable, {IColumns} from '@baidu/icloud-ui-pro-table';
import {Popover} from '@osui/ui';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {ICreateSwimLaneGroupParams, IRouteListItem, ISwimLane} from '@/api/swimLaneGroup';
import {matchingModeMap} from './constant';
import SwimLaneRouteManage from './SwimLaneRouteManage';
import c from './columns.less';

export const genColumns = (
    laneGroupID: string,
    laneGroup: ICreateSwimLaneGroupParams,
    swimLane: ISwimLane,
    getSwimLaneList: () => void
): IColumns => {
    return [
        {
            title: '入口服务',
            dataIndex: 'serviceName',
            width: 100,
            render(value) {
                return value;
            },
        },
        {
            title: (
                <>
                    引流规则
                    <Popover content='格式："引流规则名称 | URL匹配规则（方式) | Header匹配规则（方式) "'>
                        <QuestionCircleOutlined className={c['question']} />
                    </Popover>
                </>
            ),
            dataIndex: 'rules',
            render(value, record) {
                if (!value || value?.length === 0) {
                    return '--';
                }

                return (
                    <>
                        {
                            (record as IRouteListItem)?.rules?.map(
                                ({matchRequest}) => {
                                    const {enabled, matchingContent, matchingMode} = matchRequest.uri ?? {};
                                    const {routeName = '', headers = []} = matchRequest;
                                    return (
                                        <div key={routeName}>
                                            {
                                                [
                                                    routeName,
                                                    // eslint-disable-next-line max-len
                                                    `${enabled ? `${matchingContent}(${matchingModeMap.get(matchingMode)})` : '已关闭URL匹配'}`,
                                                    // eslint-disable-next-line max-len
                                                    headers.map(v => `${v.name}:${v.matchingContent}(${matchingModeMap.get(v.matchingMode)})`).join('；'),
                                                ].join(' | ')
                                            }
                                        </div>
                                    );
                                }
                            )
                        }
                    </>
                );
            },
        },
        {
            title: '操作',
            dataIndex: 'operation',
            width: 100,
            render(value, record) {
                return (
                    <ProTable.OperationsWrapper>
                        <SwimLaneRouteManage
                            type="edit"
                            laneGroupID={laneGroupID}
                            laneGroup={laneGroup}
                            swimLane={swimLane}
                            route={record as IRouteListItem}
                            getSwimLaneList={getSwimLaneList}
                        />
                        <SwimLaneRouteManage
                            type="delete"
                            laneGroupID={laneGroupID}
                            laneGroup={laneGroup}
                            swimLane={swimLane}
                            route={record as IRouteListItem}
                            getSwimLaneList={getSwimLaneList}
                        />
                    </ProTable.OperationsWrapper>
                );
            },
        },
    ];
};
