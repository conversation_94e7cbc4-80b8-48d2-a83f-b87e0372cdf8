.laneWrapper {
    margin: 0 16px;

    .lane {
        border: 1px solid #eee;
        border-radius: 3px;
        margin: 14px 0;

        .infoWrapper {
            padding: 12px 16px;
            background: var(--GRAY_10);

            .basicInfo {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .nameWrapper {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .laneName {
                        font-family: PingFangSC-Medium;
                        font-size: 14px;
                        color: var(--GRAY_2);
                        line-height: 22px;
                        font-weight: 500;
                    }

                    .tag {
                        margin: 0 0 0 12px;
                    }

                    .labelSelector {
                        margin-left: 12px;
                    }
                }
            }
        }

        .table {
            margin: 20px 16px;

            .emptyDescription {
                display: flex;
                justify-content: center;
            }
        }
    }
}
