import {message} from '@osui/ui';
import {IRuleListItem} from '@/api/swimLaneGroup';

export const genRuleListForRequest = (ruleList: IRuleListItem[]) => {
    return ruleList.map(
        ({matchRequest}) => {
            const {routeName, uri, headers} = matchRequest;
            return {
                matchRequest: {
                    routeName,
                    uri,
                    headers: headers.map(
                        ({name, matchingMode, matchingContent}) => ({name, matchingMode, matchingContent})
                    ),
                },
            };
        }
    );
};

export const validateRuleList = (ruleList: IRuleListItem[]) => {
    if (ruleList.some(({matchRequest}) => {
        const {routeName, uri, headers} = matchRequest;
        if (!routeName) {
            message.warning('请输入规则名称');
            return true;
        }
        else if (uri.enabled && !uri.matchingContent) {
            message.warning('请输入匹配内容');
            return true;
        }
        else if (headers.some(({name, matchingContent}) => !name || !matchingContent)) {
            message.warning('请输入 Header 匹配规则中的名称和匹配内容');
            return true;
        }
        return false;
    })) {
        return false;
    }

    return true;
};
