import {IRuleListItem} from '@/api/swimLaneGroup';
import {genUuid} from '@/utils/uuid';

export const genRule = ({
    laneGroup = {routeHeader: ''},
    swimLane = {laneName: ''},
    routeName = '',
    ruleId = genUuid(),
} = {}): IRuleListItem => {
    return {
        ruleId,
        matchRequest: {
            routeName,
            headers: [
                {
                    headerId: genUuid(),
                    name: laneGroup?.routeHeader || '',
                    matchingMode: 'exact',
                    matchingContent: swimLane?.laneName || '',
                },
            ],
            uri: {
                enabled: false,
                matchingContent: '',
                matchingMode: 'exact',
            },
        },
    };
};
