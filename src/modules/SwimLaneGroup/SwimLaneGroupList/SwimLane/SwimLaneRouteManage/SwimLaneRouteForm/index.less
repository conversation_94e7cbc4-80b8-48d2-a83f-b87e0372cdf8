.swimLaneRuleList {
    .subtitle {
        margin-left: 8px;
        color: var(--GRAY_5);
    }

    .listNavWrapper {
        display: flex;
        border: 1px solid #d4d6d9;
        border-radius: 4px;
        margin-top: 12px;

        .listWrapper {
            width: 170px;
            border-right: 1px solid #d4d6d9;

            .title {
                display: flex;
                justify-content: space-between;
                padding: 14px 12px 8px;
            }

            .list {
                .item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 40px;
                    padding: 14px 12px;

                    &:hover,
                    &.active {
                        cursor: pointer;
                        color: #2468f2;
                        background: #eef3fe;
                    }

                    .deleteBtn {
                        margin-left: auto;
                    }
                }
            }
        }

        .form {
            padding: 14px 12px;

            .matching {
                width: 420px;
                border-radius: 6px;
                background: var(--GRAY_10);
                padding: 16px 16px .01px;
            }

            .headerListWrapper {
                .createBtn {
                    margin-top: 8px;
                }
            }
        }
    }
}
