import {Button, Form, Input, Select, Switch, Tooltip} from '@osui/ui';
import {useState, useCallback, useMemo, useEffect} from 'react';
import ProTable from '@baidu/icloud-ui-pro-table';
import {OutlinedDelete} from '@baidu/acud-icon';
import AddButton from '@/components/AddButton';
import {ICreateSwimLaneGroupParams, IRuleListItem, ISwimLane} from '@/api/swimLaneGroup';
import {genUuid} from '@/utils/uuid';
import {validateRuleList} from '../util';
import {matchingModeOptions} from './constant';
import {genColumns} from './columns';
import {genRule} from './util';
import c from './index.less';

interface IProps {
    laneGroup: ICreateSwimLaneGroupParams;
    swimLane: ISwimLane;
    ruleList: IRuleListItem[];
    setRuleList: React.Dispatch<React.SetStateAction<IRuleListItem[]>>;
}
export default function SwimLaneRouteForm({
    laneGroup,
    swimLane,
    ruleList,
    setRuleList,
}: IProps) {
    const [ruleId, setRuleId] = useState<string>();

    useEffect(
        () => {
            if (ruleList.length === 1 || ruleList.filter(v => v.ruleId === ruleId).length === 0) {
                setRuleId(ruleList[0]?.ruleId || '');
            }
        },
        [ruleId, ruleList]
    );

    const rule = useMemo(
        () => {
            return ruleList.find(v => v.ruleId === ruleId) as IRuleListItem;
        },
        [ruleId, ruleList]
    );

    const validateForm = useCallback(
        () => {
            if (ruleId && rule) {
                return validateRuleList(ruleList);
            }

            return true;
        },
        [ruleId, rule, ruleList]
    );

    const changeRuleId = useCallback(
        (ruleId: string) => {
            try {
                const isValid = validateForm();
                if (!isValid) {
                    return;
                }

                setRuleId(ruleId);
            } catch (error) {
                console.error(error);
            }
        },
        [validateForm]
    );

    const addRule = useCallback(
        () => {
            const isValid = validateForm();
            if (!isValid) {
                return;
            }

            const ruleId = genUuid();
            setRuleId(ruleId);

            const rule = genRule({laneGroup, swimLane, routeName: `规则${ruleList.length + 1}`, ruleId});
            setRuleList(prev => {
                return [
                    ...prev,
                    rule,
                ];
            });
        },
        [laneGroup, ruleList.length, swimLane, validateForm, setRuleList]
    );

    const deleteRule = useCallback(
        (e, id) => {
            e?.stopPropagation();
            setRuleList(prev => {
                return prev.filter(v => v.ruleId !== id);
            });

            if (ruleId === id) {
                setRuleId('');
            }
        },
        [ruleId, setRuleList]
    );

    const changeMatchRequest = useCallback(
        (fieldName, value) => {
            setRuleList(prev => {
                return prev.map(v => {
                    if (v.ruleId === ruleId) {
                        if (fieldName === 'routeName') {
                            v.matchRequest.routeName = value;
                        }
                        else if (fieldName === 'enabled') {
                            v.matchRequest.uri.enabled = value;
                        }
                        else if (fieldName === 'matchingMode') {
                            v.matchRequest.uri.matchingMode = value;
                        }
                        else if (fieldName === 'matchingContent') {
                            v.matchRequest.uri.matchingContent = value;
                        }
                        return v;
                    }

                    return v;
                });
            });
        },
        [ruleId, setRuleList]
    );

    const changeHeaderList = useCallback(
        ({type, headerId, fieldName, value}) => {
            if (type === 'edit') {
                setRuleList(prev => {
                    return prev.map(v => {
                        if (v.ruleId === ruleId) {
                            v.matchRequest.headers = v.matchRequest.headers.map(h => {
                                if (h.headerId === headerId) {
                                    h[fieldName] = value;
                                }

                                return h;
                            });
                        }

                        return v;
                    });
                });
            }
            else if (type === 'create') {
                setRuleList(prev => {
                    return prev.map(v => {
                        if (v.ruleId === ruleId) {
                            v.matchRequest.headers = [
                                ...v.matchRequest.headers,
                                {
                                    headerId: genUuid(),
                                    name: '',
                                    matchingMode: 'exact',
                                    matchingContent: '',
                                },
                            ];
                        }

                        return v;
                    });
                });
            }
            else {
                setRuleList(prev => {
                    return prev.map(v => {
                        if (v.ruleId === ruleId) {
                            v.matchRequest.headers = v.matchRequest.headers.filter(h => h.headerId !== headerId);
                        }

                        return v;
                    });
                });
            }
        },
        [ruleId, setRuleList]
    );

    const columns = genColumns(rule, changeHeaderList);

    return (
        <div className={c['swimLaneRuleList']}>
            <div>
                <span>引流规则</span>
                <span className={c['subtitle']}>单条引流规则内的所有匹配规则为 AND 关系，引流规则之间为 OR 关系</span>
            </div>

            <div className={c['listNavWrapper']}>
                <div className={c['listWrapper']}>
                    <div className={c['title']}>
                        规则列表
                        <AddButton type="link" onClick={addRule}>添加</AddButton>
                    </div>

                    <div className={c['list']}>
                        {
                            ruleList.map(({ruleId: id, matchRequest: {routeName}}) => {
                                const disabled = ruleList.length <= 1;
                                return (
                                    <div
                                        className={`${c['item']} ${id === ruleId ? c['active'] : ''}`}
                                        key={id}
                                        onClick={() => changeRuleId(id as any)}
                                    >
                                        {routeName}
                                        <Tooltip
                                            className={c['deleteBtn']}
                                            title={disabled ? '至少创建1条引流规则' : ''}
                                        >
                                            <Button
                                                type="text"
                                                disabled={disabled}
                                                icon={<OutlinedDelete />}
                                                // eslint-disable-next-line react/jsx-no-bind
                                                onClick={e => deleteRule(e, id)}
                                            />
                                        </Tooltip>
                                    </div>
                                );
                            })
                        }
                    </div>
                </div>

                {
                    rule && (
                        <Form
                            className={c['form']}
                            name="basic"
                            labelAlign="left"
                        >
                            <Form.Item
                                label="规则名称"
                                required
                            >
                                <Input
                                    style={{width: 368}}
                                    maxLength={65}
                                    showCount
                                    placeholder="请输入规则名称"
                                    value={rule.matchRequest.routeName}
                                    // eslint-disable-next-line react/jsx-no-bind
                                    onChange={
                                        ({target: {value = ''}}) => changeMatchRequest('routeName', value)
                                    }
                                />
                            </Form.Item>

                            <Form.Item
                                label="匹配请求的URL"
                            >
                                <Form.Item>
                                    <Switch
                                        checked={rule.matchRequest.uri.enabled}
                                        // eslint-disable-next-line react/jsx-no-bind
                                        onChange={
                                            (value = false) => changeMatchRequest('enabled', value)
                                        }
                                    />
                                </Form.Item>

                                <Form.Item
                                    shouldUpdate
                                    noStyle
                                >
                                    {rule.matchRequest.uri.enabled
                                        && (
                                            <div className={c['matching']}>
                                                <Form.Item
                                                    label="匹配方式"
                                                    required
                                                >
                                                    <Select
                                                        style={{width: 96}}
                                                        placeholder="请选择匹配方式"
                                                        options={matchingModeOptions}
                                                        value={rule.matchRequest.uri.matchingMode}
                                                        // eslint-disable-next-line react/jsx-no-bind
                                                        onSelect={
                                                            (value = 'exact') =>
                                                                changeMatchRequest('matchingMode', value)
                                                        }
                                                    />
                                                </Form.Item>

                                                <Form.Item
                                                    label="匹配内容"
                                                    required
                                                >
                                                    <Input
                                                        style={{width: 260}}
                                                        maxLength={65}
                                                        showCount
                                                        placeholder="请输入匹配内容"
                                                        value={rule.matchRequest.uri.matchingContent}
                                                        // eslint-disable-next-line react/jsx-no-bind
                                                        onChange={
                                                            ({target: {value = ''}}) =>
                                                                changeMatchRequest('matchingContent', value)
                                                        }
                                                    />
                                                </Form.Item>
                                            </div>
                                        )
                                    }
                                </Form.Item>
                            </Form.Item>

                            <div className={c['headerListWrapper']}>
                                <Form.Item label="Header 匹配规则" />
                                <ProTable
                                    columns={columns}
                                    dataSource={rule.matchRequest.headers}
                                    rowKey="headerId"
                                    pagination={false}
                                />

                                <AddButton
                                    className={c['createBtn']}
                                    type="link"
                                    // eslint-disable-next-line react/jsx-no-bind
                                    onClick={
                                        () => changeHeaderList({
                                            type: 'create',
                                        })
                                    }
                                >
                                    添加
                                </AddButton>
                            </div>
                        </Form>
                    )
                }
            </div>
        </div>
    );
}
