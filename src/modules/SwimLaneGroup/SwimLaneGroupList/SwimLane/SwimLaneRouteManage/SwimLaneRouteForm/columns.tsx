import {IColumns} from '@baidu/icloud-ui-pro-table';
import {Button, Input, Select, Tooltip} from '@osui/ui';
import {IRuleListItem} from '@/api/swimLaneGroup';
import {matchingModeOptions} from './constant';

export const genColumns = (rule: IRuleListItem, changeHeaderList: (obj: object) => void): IColumns => {
    return [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            render: (value, record) => (
                <Input
                    // style={{width: 150}}
                    maxLength={65}
                    showCount
                    placeholder="请输入名称"
                    value={value}
                    // eslint-disable-next-line react/jsx-no-bind
                    onChange={
                        ({target: {value = ''}}) => changeHeaderList({
                            type: 'edit',
                            headerId: record.headerId,
                            fieldName: 'name',
                            value,
                        })
                    }
                />
            ),
        },
        {
            title: '匹配模式',
            dataIndex: 'matchingMode',
            key: 'matchingMode',
            width: 96,
            render: (value, record) => (
                <Select
                    // style={{width: 150}}
                    placeholder="请选择匹配模式"
                    options={matchingModeOptions}
                    value={value}
                    // eslint-disable-next-line react/jsx-no-bind
                    onChange={
                        (value = '') => changeHeaderList({
                            type: 'edit',
                            headerId: record.headerId,
                            fieldName: 'matchingMode',
                            value,
                        })
                    }
                />
            ),
        },
        {
            title: '匹配内容',
            dataIndex: 'matchingContent',
            key: 'matchingContent',
            width: 200,
            render: (value, record) => (
                <Input
                    maxLength={65}
                    showCount
                    placeholder="请输入匹配内容"
                    value={value}
                    // eslint-disable-next-line react/jsx-no-bind
                    onChange={
                        ({target: {value = ''}}) => changeHeaderList({
                            type: 'edit',
                            headerId: record.headerId,
                            fieldName: 'matchingContent',
                            value,
                        })
                    }
                />
            ),
        },
        {
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            width: 60,
            render: (value, record) => {
                const disabled = rule?.matchRequest?.headers?.length <= 1;

                return (
                    <Tooltip
                        title={disabled ? '至少创建1条Header 匹配规则' : ''}
                    >
                        <Button
                            type="link"
                            disabled={disabled}
                            // eslint-disable-next-line react/jsx-no-bind
                            onClick={
                                () => changeHeaderList({
                                    type: 'delete',
                                    headerId: record.headerId,
                                })
                            }
                        >
                            删除
                        </Button>
                    </Tooltip>
                );
            },
        },
    ];
};


