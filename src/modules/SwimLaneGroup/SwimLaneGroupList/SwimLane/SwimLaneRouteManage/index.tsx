import {Button, Modal, Form, Select, message} from '@osui/ui';
import {useBoolean} from 'huse';
import {useCallback, useMemo, useState} from 'react';
import {
    ICreateSwimLaneGroupParams,
    IRouteListItem,
    ISwimLane,
    createSwimLaneRouteApi,
    deleteSwimLaneRouteApi,
    updateSwimLaneRouteApi,
} from '@/api/swimLaneGroup';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {genUuid} from '@/utils/uuid';
import {initialValuesDft, serviceRules} from './constant';
import SwimLaneRouteForm from './SwimLaneRouteForm';
import {genRuleListForRequest, validateRuleList} from './util';
import {genRule} from './SwimLaneRouteForm/util';

type TType = 'create' | 'edit' | 'delete';
interface IProps {
    type: TType;
    laneGroupID: string;
    laneGroup: ICreateSwimLaneGroupParams;
    swimLane: ISwimLane;
    route?: IRouteListItem;
    getSwimLaneList: () => void;
}
export default function SwimLaneRouteManage({
    type,
    laneGroupID,
    laneGroup,
    swimLane,
    route,
    getSwimLaneList,
}: IProps) {
    const [form] = Form.useForm();
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);
    const [isConfirmLoading, {on: onConfirmLoading, off: offConfirmLoading}] = useBoolean(false);
    const typeText = useMemo(
        () => {
            return type === 'create' ? '添加' : '编辑';
        },
        [type]
    );

    const serviceListOptions = useMemo(
        () => {
            if (!swimLane || !swimLane.serviceList) {
                return [];
            }

            return swimLane.serviceList.map(({clusterID, clusterRegion, namespace, isHost, serviceName}) => ({
                label: serviceName,
                value: JSON.stringify({clusterID, clusterRegion, namespace, isHost, serviceName}),
            }));
        },
        [swimLane]
    );

    const serviceListFilterOption = useCallback(
        (input, option) => {
            return option?.label?.toLowerCase()?.includes(input.toLowerCase());
        },
        []
    );

    const initialValues = useMemo(
        () => {
            if (type === 'edit' && route) {
                const {clusterID, clusterRegion, namespace, isHost, serviceName} = route;
                return {
                    service: JSON.stringify({clusterID, clusterRegion, namespace, isHost, serviceName}),
                };
            }

            return undefined;
        },
        [route, type]
    );

    const [ruleList, setRuleList] = useState(
        route?.rules.map(v => ({
            ...v,
            ruleId: genUuid(),
            matchRequest: {
                ...v.matchRequest,
                headers: v.matchRequest.headers.map(v => ({...v, headerId: genUuid()})),
            },
        })) || [genRule({laneGroup, swimLane, routeName: '规则1', ruleId: genUuid()})]
    );

    const deleteSwimLaneRoute = useCallback(
        () => {
            Modal.confirm({
                title: '确定删除引流规则吗？',
                content: (
                    <div>
                        删除“{swimLane?.laneName}”泳道下的引流规则后，操作将不可恢复
                    </div>
                ),
                async onOk() {
                    const {clusterRegion = '', clusterID = '', namespace = '', serviceName = ''} = route || {};
                    const rsp = await deleteSwimLaneRouteApi({
                        serviceMeshInstanceId,
                        laneGroupID,
                        laneID: swimLane?.laneID,
                        clusterRegion,
                        clusterID,
                        namespace,
                        serviceName,
                    });
                    if (rsp) {
                        message.success('引流规则删除成功');
                        getSwimLaneList && getSwimLaneList();
                    }
                    else {
                        message.error('引流规则删除失败，请重新操作');
                        return Promise.reject(new Error('引流规则删除失败，请重新操作'));
                    }
                },
                closable: true,
            });
        },
        [swimLane?.laneName, swimLane?.laneID, route, serviceMeshInstanceId, laneGroupID, getSwimLaneList]
    );

    const onSubmit = useCallback(
        async () => {
            try {
                const values = await form.validateFields();
                const isValid = validateRuleList(ruleList);

                if (isValid) {
                    const body = {
                        ...JSON.parse(values.service),
                        serviceMeshInstanceId,
                        laneGroupID,
                        laneID: swimLane?.laneID,
                        rules: genRuleListForRequest(ruleList),
                    };

                    onConfirmLoading();
                    const rsp = type === 'create'
                        ? await createSwimLaneRouteApi({
                            ...JSON.parse(values.service),
                            ...body,
                        })
                        : await updateSwimLaneRouteApi({
                            ...JSON.parse(values.service),
                            ...body,
                        });

                    if (rsp) {
                        message.success(`${typeText}引流规则成功`);
                        getSwimLaneList && getSwimLaneList();
                        offVisible();
                    }
                    else {
                        message.error(`${typeText}引流规则失败，请稍后重试`);
                    }
                }
            } catch (error) {
                console.error(error);
            } finally {
                offConfirmLoading();
            }
        },
        [
            form, ruleList, serviceMeshInstanceId, laneGroupID, swimLane?.laneID, type, typeText,
            getSwimLaneList, offVisible, onConfirmLoading, offConfirmLoading,
        ]
    );

    return (
        <div>
            {
                type === 'create'
                    ? <Button type="link" onClick={onVisible}>添加引流规则</Button>
                    : type === 'edit'
                        ? <Button type="link" onClick={onVisible}>编辑</Button>
                        : <Button type="link" onClick={deleteSwimLaneRoute}>删除</Button>
            }

            <Modal
                width={800}
                title={`${typeText}引流规则`}
                visible={visible}
                onOk={onSubmit}
                onCancel={offVisible}
                confirmLoading={isConfirmLoading}
            >
                <Form
                    name="basic"
                    labelAlign="left"
                    initialValues={initialValues || initialValuesDft}
                    form={form}
                >
                    <Form.Item
                        label="泳道入口服务"
                        required
                        rules={serviceRules}
                        name={['service']}
                    >
                        <Select
                            style={{width: 368}}
                            placeholder="请选择泳道入口服务"
                            disabled={type === 'edit'}
                            options={serviceListOptions}
                            allowClear
                            showSearch
                            filterOption={serviceListFilterOption}
                        />
                    </Form.Item>

                    <SwimLaneRouteForm
                        laneGroup={laneGroup}
                        swimLane={swimLane}
                        ruleList={ruleList}
                        setRuleList={setRuleList}
                    />
                </Form>
            </Modal>
        </div>
    );
}
