import {useRequestCallback} from 'huse';
import {useCallback, useEffect, useMemo} from 'react';
import {Button, Modal, Space, Spin, Tag, Tooltip, message} from '@osui/ui';
import ProTable from '@baidu/icloud-ui-pro-table';
import {
    ICreateSwimLaneGroupParams,
    ISwimLane,
    deleteSwimLaneApi,
    getSwimLaneListApi,
    setBaseSwimLaneApi,
} from '@/api/swimLaneGroup';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {QueryResponseBase} from '@/interface';
import {genColumns} from './columns';
import SwimLaneManage from './SwimLaneManage';
import SwimLaneRouteManage from './SwimLaneRouteManage';
import c from './index.less';

interface IProps {
    swimLaneGroupList: QueryResponseBase<ICreateSwimLaneGroupParams[]>;
    laneGroupID: string;
}
export default function SwimLane({
    swimLaneGroupList,
    laneGroupID,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [
        getSwimLaneList,
        {pending: swimLaneListLoading, data: swimLaneList},
    ] = useRequestCallback(getSwimLaneListApi, {serviceMeshInstanceId, laneGroupID});

    const laneGroup = useMemo(
        () => {
            return swimLaneGroupList?.result?.find(v => v.groupID === laneGroupID);
        },
        [swimLaneGroupList, laneGroupID]
    );

    useEffect(
        () => {
            getSwimLaneList();
        },
        [getSwimLaneList]
    );

    const deleteSwimLane = useCallback(
        (v: ISwimLane) => {
            Modal.confirm({
                title: '确定删除泳道吗？',
                content: (
                    <div>
                        删除“{v.laneName}”后，操作将不可恢复
                    </div>
                ),
                async onOk() {
                    const rsp = await deleteSwimLaneApi({
                        serviceMeshInstanceId,
                        laneGroupID,
                        laneID: v.laneID,
                    });
                    if (rsp) {
                        message.success(`“${v.laneName}”删除成功`);
                        getSwimLaneList();
                    }
                    else {
                        message.error(`“${v.laneName}”删除失败，请重新操作。`);
                        return Promise.reject(new Error('删除失败，请重新操作。'));
                    }
                },
                closable: true,
            });
        },
        [serviceMeshInstanceId, laneGroupID, getSwimLaneList]
    );

    const setBaseSwimLane = useCallback(
        (v: ISwimLane) => {
            const baseLaneName = swimLaneList?.result?.find(v => v.isBaseLane)?.laneName ?? '';
            const {laneID, laneName} = v;

            Modal.confirm({
                title: `设置“${laneName}”为基线泳道`,
                content: (
                    <div>
                        原基线泳道为“{baseLaneName}”，请确定是否变更
                    </div>
                ),
                async onOk() {
                    const rsp = await setBaseSwimLaneApi({
                        serviceMeshInstanceId,
                        laneGroupID,
                        laneID,
                    });

                    if (rsp) {
                        message.success(`设置“${laneName}”为基线泳道成功`);
                        getSwimLaneList();
                    }
                    else {
                        message.error(`设置“${laneName}”为基线泳道失败，请重新操作。`);
                        return Promise.reject(new Error(''));
                    }
                },
                closable: true,
            });
        },
        [swimLaneList?.result, serviceMeshInstanceId, laneGroupID, getSwimLaneList]
    );

    return (
        <div className={c['laneWrapper']}>
            <SwimLaneManage
                type="create"
                laneGroupID={laneGroupID}
                swimLaneGroupList={swimLaneGroupList}
                getSwimLaneList={getSwimLaneList}
            />

            <div>
                {
                    swimLaneListLoading
                        ? <Spin />
                        : swimLaneList?.result?.map(
                            v => {
                                return (
                                    <div key={v.laneID} className={c['lane']}>
                                        <div className={c['infoWrapper']}>
                                            <div className={c['basicInfo']}>
                                                <div className={c['nameWrapper']}>
                                                    <div className={c['laneName']}>{v.laneName}</div>
                                                    {
                                                        v.isBaseLane && (
                                                            <Tag className={c['tag']} color="blue">基线泳道</Tag>
                                                        )
                                                    }
                                                    <div className={c['labelSelector']}>
                                                        <Tooltip placement="top" title="标签键:标签值">
                                                            {v.labelSelectorKey}:{v.labelSelectorValue}
                                                        </Tooltip>
                                                    </div>
                                                </div>

                                                <div>
                                                    <Space size="middle">
                                                        {
                                                            !v.isBaseLane
                                                            && (
                                                                <Button
                                                                    // eslint-disable-next-line max-len
                                                                    disabled={v.serviceList?.length !== swimLaneList?.result?.find(v => v.isBaseLane)?.serviceList?.length}
                                                                    type="link"
                                                                    disabledReason={
                                                                        // eslint-disable-next-line max-len
                                                                        v.serviceList?.length === swimLaneList?.result?.find(v => v.isBaseLane)?.serviceList?.length
                                                                            ? ''
                                                                            : '基线泳道必须包含泳道组中所有服务'
                                                                    }
                                                                    // eslint-disable-next-line react/jsx-no-bind
                                                                    onClick={() => setBaseSwimLane(v)}
                                                                >
                                                                    设为基线泳道
                                                                </Button>
                                                            )
                                                        }
                                                        <SwimLaneManage
                                                            type="edit"
                                                            swimLane={v}
                                                            laneGroupID={laneGroupID}
                                                            swimLaneGroupList={swimLaneGroupList}
                                                            getSwimLaneList={getSwimLaneList}
                                                        />
                                                        <SwimLaneRouteManage
                                                            type="create"
                                                            laneGroupID={laneGroupID}
                                                            laneGroup={laneGroup as ICreateSwimLaneGroupParams}
                                                            swimLane={v}
                                                            getSwimLaneList={getSwimLaneList}
                                                        />
                                                        <a onClick={() => deleteSwimLane(v)}>删除</a>
                                                    </Space>
                                                </div>
                                            </div>

                                            <div>
                                                泳道服务（命名空间）：{
                                                    v.serviceList?.map(
                                                        ({serviceName, namespace}) => {
                                                            return `${serviceName}(${namespace})`;
                                                        }
                                                    ).join('、')
                                                }
                                            </div>
                                        </div>

                                        <ProTable
                                            pagination={false}
                                            className={c['table']}
                                            columns={
                                                genColumns(
                                                    laneGroupID,
                                                    laneGroup as ICreateSwimLaneGroupParams,
                                                    v,
                                                    getSwimLaneList
                                                )
                                            }
                                            dataSource={v.routeList ?? []}
                                            emptyDescription={
                                                <div className={c['emptyDescription']}>该泳道还未添加任何引流规则，
                                                    <SwimLaneRouteManage
                                                        type="create"
                                                        laneGroupID={laneGroupID}
                                                        laneGroup={laneGroup as ICreateSwimLaneGroupParams}
                                                        swimLane={v}
                                                        getSwimLaneList={getSwimLaneList}
                                                    />
                                                </div>
                                            }
                                        />
                                    </div>
                                );
                            }
                        )
                }
            </div>
        </div>
    );
}
