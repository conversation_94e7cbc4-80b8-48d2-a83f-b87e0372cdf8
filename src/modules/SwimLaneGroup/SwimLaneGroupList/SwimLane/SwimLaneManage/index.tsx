import {Button, Form, Input, Modal, Select, message} from '@osui/ui';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {useBoolean, useRequestCallback} from 'huse';
import {DefaultOptionType} from 'antd/lib/select';
import {cloneDeep} from 'lodash';
import AddButton from '@/components/AddButton';
import {
    laneNameRules,
    labelSelectorKeyRules,
    labelSelectorValueRules,
    laneNameHelp,
    useLabelSelectorHelp,
} from '@/modules/SwimLaneGroup/SwimLaneGroupCreate/BaseLaneConfigForm/constant';
import {serviceListRules} from '@/modules/SwimLaneGroup/SwimLaneGroupCreate/BasicConfigForm/constant';
import {
    ICreateSwimLaneGroupParams,
    ICreateSwimLaneParams,
    ILaneGroupLabelItem,
    ISwimLane,
    createSwimLaneApi,
    getLaneGroupLabelListApi,
    updateSwimLane<PERSON><PERSON>,
} from '@/api/swimLaneGroup';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {QueryResponseBase} from '@/interface';
import {genServiceListForRequest} from '@/modules/SwimLaneGroup/util';
import {laneConfigFormValueDft} from './constant';
import c from './index.less';

type TType = 'create' | 'edit';
interface IProps {
    type: TType;
    swimLane?: ISwimLane;
    swimLaneGroupList: QueryResponseBase<ICreateSwimLaneGroupParams[]>;
    laneGroupID: string;
    getSwimLaneList: () => void;
}
export default function SwimLaneManage({
    type,
    swimLane,
    swimLaneGroupList,
    laneGroupID,
    getSwimLaneList,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);
    const [isConfirmLoading, {on: onConfirmLoading, off: offConfirmLoading}] = useBoolean(false);
    const [laneGroupLabelList, setLaneGroupLabelList] = useState<ILaneGroupLabelItem[]>([]);
    const [key, setKey] = useState('');
    const [value, setValue] = useState('');
    const [keyOptions, setKeyOptions] = useState<DefaultOptionType[]>([]);
    const [valueOptions, setValueOptions] = useState<DefaultOptionType[]>([]);
    const [serviceListValue, setServiceListValue] = useState<string[]>([]); // serviceList
    const [laneConfigFormInstante] = Form.useForm();

    const typeText = useMemo(
        () => {
            return type === 'create' ? '新建' : '编辑';
        },
        [type]
    );

    const initialValues = useMemo(
        () => {
            if (type === 'create' || !swimLane) {
                return undefined;
            }

            const {laneName = '', labelSelectorKey = '', labelSelectorValue = '', serviceList = []} = swimLane;
            return {
                laneName,
                labelSelectorKey,
                labelSelectorValue,
                serviceList: serviceList?.map(v => JSON.stringify(v)),
            };
        },
        [type, swimLane]
    );

    const serviceList = useMemo(
        () => {
            return swimLaneGroupList?.result?.filter(v => v.groupID === laneGroupID)?.[0]?.serviceList ?? [];
        },
        [laneGroupID, swimLaneGroupList?.result]
    );

    const serviceListOptions = useMemo(
        () => {
            return serviceList.map(v => ({label: v.serviceName, value: JSON.stringify(v)}));
        },
        [serviceList]
    );

    const serviceListFilterOption = useCallback(
        (input, option) => {
            return option?.label?.toLowerCase()?.includes(input.toLowerCase());
        },
        []
    );

    const changeServiceListValue = useCallback(
        v => {
            setServiceListValue(v);
        },
        [setServiceListValue]
    );

    const [
        getLaneGroupLabelList,
        {data: laneGroupLabelListData, pending: laneGroupLabelListLoading},
    ] = useRequestCallback(
        getLaneGroupLabelListApi,
        {
            serviceMeshInstanceId,
            serviceList: genServiceListForRequest(serviceList),
        }
    );

    useEffect(
        () => {
            if (visible) {
                getLaneGroupLabelList();
            }
        },
        [visible, getLaneGroupLabelList]
    );

    useEffect(
        () => {
            if (laneGroupLabelListData) {
                setLaneGroupLabelList(laneGroupLabelListData?.result ?? []);
                const keyList = [...new Set(laneGroupLabelList.map(v => v.key))];
                const valueList = [
                    ...new Set(laneGroupLabelList.map(v => v.valueList).reduce((acc, cur) => [...acc, ...cur], [])),
                ];
                setKeyOptions(keyList.map(v => ({label: v, value: v})));
                setValueOptions(valueList.map(v => ({label: v, value: v})));
            }
        },
        [laneGroupLabelList, laneGroupLabelListData]
    );

    const changeKey = useCallback(
        v => {
            setKey(v);
            const valueList = [
                ...new Set(
                    laneGroupLabelList.filter(item => !v || item.key === v)
                        .map(v => v.valueList).reduce((acc, cur) => [...acc, ...cur], [])
                ),
            ];
            const valueListOptions = valueList.map(v => ({label: v, value: v}));
            setValueOptions(valueListOptions);
        },
        [laneGroupLabelList]
    );

    const changeValue = useCallback(
        v => {
            setValue(v);
            const keyList = [
                ...new Set(
                    laneGroupLabelList.filter(item => !v || item.valueList.includes(v))
                        .map(v => v.key)
                ),
            ];
            const keyListOptions = keyList.map(v => ({label: v, value: v}));
            setKeyOptions(keyListOptions);
        },
        [laneGroupLabelList]
    );

    const onSubmit = useCallback(
        async () => {
            try {
                const laneConfigFormValues = await laneConfigFormInstante.validateFields();
                const laneConfigFormValuesReq: ICreateSwimLaneParams = cloneDeep(laneConfigFormValues);
                const serviceList = laneConfigFormValues?.serviceList?.map((v: string) => JSON.parse(v));
                laneConfigFormValuesReq.serviceList = serviceList;

                // 区分创建和编辑
                let rsp = false;
                onConfirmLoading();
                if (type === 'create') {
                    rsp = await createSwimLaneApi({
                        ...laneConfigFormValuesReq,
                        serviceMeshInstanceId,
                        laneGroupID,
                    });
                }
                else {
                    laneConfigFormValuesReq.laneID = swimLane?.laneID ?? '';
                    rsp = await updateSwimLaneApi({
                        ...laneConfigFormValuesReq,
                        laneID: swimLane?.laneID,
                        laneGroupID,
                        serviceMeshInstanceId,
                    } as any);
                }

                if (rsp) {
                    message.success(`泳道“${laneConfigFormValuesReq.laneName}”${typeText}成功`);
                    laneConfigFormInstante.setFieldsValue(laneConfigFormValueDft);
                    offVisible();
                    getSwimLaneList();
                }
                else {
                    message.error(`泳道“${laneConfigFormValuesReq.laneName}”${typeText}失败，请重新操作。`);
                }
            } catch (error) {
                console.error(error);
            } finally {
                offConfirmLoading();
            }
        },
        [
            laneConfigFormInstante, type, serviceMeshInstanceId, laneGroupID, swimLane?.laneID, typeText,
            offVisible, getSwimLaneList, onConfirmLoading, offConfirmLoading,
        ]
    );

    const labelSelectorHelp = useLabelSelectorHelp();

    return (
        <>
            {
                type === 'create'
                    ? <AddButton onClick={onVisible}>新建</AddButton>
                    : <Button type="link" onClick={onVisible}>编辑</Button>
            }

            <Modal
                className={c['modal']}
                width={600}
                title={`${typeText}泳道`}
                visible={visible}
                onOk={onSubmit}
                onCancel={offVisible}
                confirmLoading={isConfirmLoading}
            >
                <Form
                    name="basic"
                    labelAlign="left"
                    initialValues={initialValues || laneConfigFormValueDft}
                    form={laneConfigFormInstante}
                >
                    <Form.Item
                        label="泳道名称"
                        required
                        rules={laneNameRules}
                        help={laneNameHelp}
                        name={['laneName']}
                    >
                        <Input
                            style={{width: 338}}
                            maxLength={65}
                            showCount
                            placeholder="请输入泳道名称"
                            disabled={type === 'edit'}
                        />
                    </Form.Item>

                    <Form.Item
                        label="标签"
                        required
                        help={labelSelectorHelp}
                    >
                        <div className={c['labelSelector']}>
                            <Form.Item
                                rules={labelSelectorKeyRules}
                                name={['labelSelectorKey']}
                            >
                                <Select
                                    style={{width: 200}}
                                    placeholder="请选择标签键"
                                    loading={laneGroupLabelListLoading}
                                    options={keyOptions}
                                    showSearch
                                    allowClear
                                    value={key}
                                    onChange={changeKey}
                                />
                            </Form.Item>

                            <Form.Item
                                className={c['labelSelectorValue']}
                                rules={labelSelectorValueRules}
                                name={['labelSelectorValue']}
                            >
                                <Select
                                    style={{width: 200}}
                                    placeholder="请选择标签值"
                                    loading={laneGroupLabelListLoading}
                                    options={valueOptions}
                                    showSearch
                                    allowClear
                                    value={value}
                                    onChange={changeValue}
                                />
                            </Form.Item>
                        </div>
                    </Form.Item>

                    <Form.Item
                        label="泳道服务"
                        required
                        name={['serviceList']}
                        rules={serviceListRules}
                    >
                        <Select
                            style={{width: 424}}
                            placeholder="请选择泳道服务"
                            options={serviceListOptions}
                            allowClear
                            filterOption={serviceListFilterOption}
                            mode="multiple"
                            value={serviceListValue}
                            onChange={changeServiceListValue}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
}
