import {IOperations} from '@baidu/icloud-ui-pro-table';
import {Button} from '@osui/ui';
import {useBoolean} from 'huse';
import AddButton from '@/components/AddButton';
import urls from '@/urls';
import ExternalLink from '@/components/ExternalLink';
import DomainManage from './DomainManage';

// 操作区
export const useOperations = (): IOperations => {
    const [isModalOpen, {on: onModalOpen, off: offModalOpen}] = useBoolean(false);

    return [
        {
            render(props, {refresh}) {
                return (
                    <>
                        <AddButton action="create" onClick={onModalOpen}>添加域名监听</AddButton>
                        <DomainManage
                            type="create"
                            isModalOpen={isModalOpen}
                            offModalOpen={offModalOpen}
                            refresh={refresh}
                        />
                    </>
                );
            },
        },
        {
            render() {
                return (
                    <Button>
                        <ExternalLink
                            href={urls.external.iam.certList.fill()}
                            value="证书管理"
                        />
                    </Button>
                );
            },
        },
    ];
};
