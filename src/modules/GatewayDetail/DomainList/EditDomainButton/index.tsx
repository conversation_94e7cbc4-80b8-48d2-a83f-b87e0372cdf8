import {Button} from '@osui/ui';
import {useBoolean} from 'huse';
import {useMemo} from 'react';
import _ from 'lodash';
import {IDomainListItem} from '@/api/gateway';
import DomainManage from '../DomainManage';


interface IProps {
    record: IDomainListItem;
    refresh: () => void;
}
export default function EditDomainButton({record, refresh}: IProps) {
    const [isModalOpen, {on: onModalOpen, off: offModalOpen}] = useBoolean(false);

    const recordUpdate = useMemo(
        () => {
            const recordNew = _.cloneDeep(record);
            recordNew.port.preNumber = record.port.number;
            return recordNew;
        },
        [record]
    );

    return (
        <>
            <Button
                type="link"
                onClick={onModalOpen}
            >
                编辑
            </Button>

            {
                isModalOpen
                && (
                    <DomainManage
                        type="edit"
                        record={recordUpdate}
                        isModalOpen={isModalOpen}
                        offModalOpen={offModalOpen}
                        refresh={refresh}
                    />
                )
            }
        </>
    );
}
