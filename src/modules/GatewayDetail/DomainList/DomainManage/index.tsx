// 域名监听的添加、编辑
import {Form, Input, message, Modal, Select, Switch} from '@osui/ui';
import {useCallback, useMemo} from 'react';
import {useParams} from 'react-router-dom';
import {useBoolean} from 'huse';
import _ from 'lodash';
import {domainProtocolDict} from '@/dicts/gateway';
import {createDomainApi, TDomainProtocol, IDomainListItem, updateDomainApi} from '@/api/gateway';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import CertSelect from './CertSelect';
import {certRules, domainsRules, portNameRules, portNumberRules, portProtocolRules} from './rules';

import c from './index.less';

export type TDomainManage = 'create' | 'edit';

const initialValuesDefault = {
    domains: '',
    port: {
        name: '',
        number: 80,
        preNumber: 80,
        protocol: 'HTTP',
    },
    cert: {
        id: '',
    },
    isForceHttps: false,
};

const protocolToPortNumberMap = new Map<TDomainProtocol, number>([
    ['HTTP', 80],
    ['HTTPS', 443],
]);

interface IProps {
    type: TDomainManage;
    // type = 'edit'时，record 为必传字段
    record?: IDomainListItem;
    isModalOpen: boolean;
    offModalOpen: () => void;
    refresh: () => void;
}
export default function DomainManage({
    type = 'create',
    record,
    isModalOpen,
    offModalOpen,
    refresh,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{gatewayId: string}>();
    const [form] = Form.useForm();
    const title = `${type === 'create' ? '添加' : '编辑'}域名监听`;

    const [submitting, {on: onSubmitting, off: offSubmitting}] = useBoolean(false);

    const protocolOptions = useMemo(
        () => {
            return Object.entries(domainProtocolDict).map(([value, label]) => ({label, value}));
        },
        []
    );

    const initialValues = useMemo(
        () => {
            // 编辑弹窗，需对数据进行预处理
            if (type === 'edit' && record) {
                const recordNew = _.cloneDeep(record);
                const {domains} = record;
                if (record.cert) {
                    const {cert: {id, name}} = record;
                    recordNew.cert = {
                        id: JSON.stringify({id, name}),
                        name,
                    };
                }
                recordNew.domains = (domains as string[]).join(',');

                return recordNew;
            }

            return initialValuesDefault;
        },
        [type, record]
    );

    const onSubmit = useCallback(
        async () => {
            try {
                onSubmitting();
                const {domains, port, cert, isForceHttps} = await form.validateFields();
                const {id, name} = JSON.parse(cert?.id || '{}') ?? {};
                const domainParams = {
                    serviceMeshInstanceId,
                    gatewayId,
                    port: {...port, number: parseInt(port.number, 10)},
                    domains: domains?.split(','),
                    isForceHttps,
                };
                // 注：选了证书，需要传 cert 字段
                if (id) {
                    Object.assign(domainParams, {
                        cert: {
                            id,
                            name,
                        },
                    });
                }

                const res = await (type === 'create' ? createDomainApi(domainParams) : updateDomainApi(domainParams));
                if (res) {
                    message.success(`${title}成功`);
                    if (type === 'create') {
                        form.setFieldsValue(initialValuesDefault);
                    }
                    offModalOpen();
                    refresh();
                }
                else {
                    message.error(`${title}失败，请重试`);
                }
            } catch (error) {
                // console.log(error);
            } finally {
                offSubmitting();
            }
        },
        [type, title, serviceMeshInstanceId, gatewayId, form, offModalOpen, offSubmitting, onSubmitting, refresh]
    );

    Form.useLabelLayout('basic', 0);

    return (
        <Modal
            className={c['modal']}
            title={title}
            confirmLoading={submitting}
            visible={isModalOpen}
            onCancel={offModalOpen}
            onOk={onSubmit}
        >
            <Form
                name="basic"
                labelAlign="left"
                initialValues={initialValues}
                form={form}
            >
                <Form.Item
                    noStyle
                    name={['port', 'preNumber']}
                />

                <Form.Item
                    required
                    label="域名"
                    name={['domains']}
                    rules={domainsRules}
                >
                    <Input
                        disabled={type === 'edit'}
                        className={c['domains']}
                        placeholder="支持输入多个域名信息，用“,”隔开"
                    />
                </Form.Item>

                <Form.Item
                    required
                    label="端口名称"
                    name={['port', 'name']}
                    rules={portNameRules}
                >
                    <Input
                        placeholder="请输⼊端口名称"
                    />
                </Form.Item>

                <Form.Item
                    shouldUpdate
                    noStyle
                >
                    {({setFieldsValue}) => {
                        const changePortProtocol = (protocol: TDomainProtocol) => {
                            const portNumber = protocolToPortNumberMap.get(protocol);
                            setFieldsValue({port: {number: portNumber}});
                        };

                        return (
                            <Form.Item
                                required
                                label="协议"
                                name={['port', 'protocol']}
                                rules={portProtocolRules}
                            >
                                <Select options={protocolOptions} onChange={changePortProtocol} />
                            </Form.Item>
                        );
                    }}
                </Form.Item>

                <Form.Item
                    required
                    label="端口"
                    name={['port', 'number']}
                    rules={portNumberRules}
                >
                    <Input
                        type="number"
                        disabled
                    />
                </Form.Item>

                <Form.Item
                    shouldUpdate
                    noStyle
                >
                    {({getFieldValue}) => {
                        const protocol: TDomainProtocol = getFieldValue(['port', 'protocol']);
                        const isHTTPS = protocol === 'HTTPS';

                        return (
                            isHTTPS && (
                                <>
                                    <Form.Item
                                        label="证书"
                                        name={['cert', 'id']}
                                        required={isHTTPS}
                                        rules={isHTTPS ? certRules : undefined}
                                    >
                                        <CertSelect form={form} type="edit" />
                                    </Form.Item>

                                    <Form.Item
                                        label="强制HTTPS"
                                        name={['isForceHttps']}
                                        valuePropName="checked"
                                    >
                                        <Switch checkedChildren="开" unCheckedChildren="关" />
                                    </Form.Item>
                                </>
                            )
                        );
                    }}
                </Form.Item>
            </Form>
        </Modal>
    );
}
