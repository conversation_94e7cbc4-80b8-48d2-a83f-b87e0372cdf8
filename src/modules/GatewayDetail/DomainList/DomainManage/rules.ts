import {Rule} from 'antd/lib/form';

export const domainsRules: Rule[] = [
    {required: true, message: '域名不可为空'},
];

export const portNameRules: Rule[] = [
    {required: true, message: '端口名称不可为空'},
];

export const portNumberRules: Rule[] = [
    {required: true, message: '端口不可为空'},
];

export const portProtocolRules: Rule[] = [
    {required: true, message: '协议不可为空'},
];

export const certRules: Rule[] = [
    {required: true, message: '证书不可为空'},
];
