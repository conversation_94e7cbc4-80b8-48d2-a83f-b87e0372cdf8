import {Button, FormInstance, Select} from '@osui/ui';
import {useRequestCallback} from 'huse';
import {useEffect, useMemo, useState} from 'react';
import {OutlinedRefresh} from '@baidu/acud-icon';
import {getCertificateListApi} from '@/api/gateway';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {IS_QASANDBOX} from '@/dicts';
import {TDomainManage} from '..';
import c from './index.less';

const mockCertList = [
    {
        certId: 'cert-0n3pjjjue21g',
        certName: 'helloworld',
    },
    {
        certId: 'cert-ixk3c00hmdt1',
        certName: 'httpbin',
    },
];
interface IProps {
    form: FormInstance;
    type: TDomainManage;
}
export default function CertSelect({form, type, ...props}: IProps) {
    // TODO 获取全量数据 —— 抽成公共方法。
    const [getList, data] = useRequestCallback(getCertificateListApi, {pageNo: 1, pageSize: 1000});
    const [idStrPre, setIdStrPre] = useState('');
    const [nameStrPre, setNameStrPre] = useState('');

    useEffect(
        () => {
            const {id: idObjStrPre} = form.getFieldValue('cert') || {};
            const {id: idStrPre, name: nameStrPre} = JSON.parse(idObjStrPre || '{}');
            setIdStrPre(idStrPre);
            setNameStrPre(nameStrPre);
        },
        [form]
    );

    const options = useMemo(
        () => {
            // 沙盒环境塞入测试数据
            // TODO 不同的环境有不同的逻辑等时，需抽离处理
            const resultNew = IS_QASANDBOX
                ? mockCertList
                : [];

            if (data && data.data?.result) {
                const {result} = data.data;
                resultNew.push(...result);
            }

            return resultNew.map(({certId: id, certName: name}) => (
                {label: name, value: JSON.stringify({id, name})}
            ));
        },
        [data]
    );


    useEffect(
        () => {
            if (
                data.pending
                || (type === 'create' && (idStrPre === '' || nameStrPre === ''))
                // 边界：沙盒环境下，接口异常、会拉不到数据，所以需要写成 (data.data?.result || mockCertList)
                || (data.data?.result || mockCertList).every(v => v.certId !== idStrPre)
            ) {
                form.setFieldsValue({
                    cert: {
                        id: '',
                    },
                });
            }
            else {
                form.setFieldsValue({
                    cert: {
                        id: JSON.stringify({id: idStrPre, name: nameStrPre}),
                    },
                });
            }
        },
        [data.data?.result, data.pending, form, idStrPre, nameStrPre, type]
    );

    useEffect(
        () => {
            getList();
        },
        [getList]
    );

    return (
        <div className={c['select-wrapper']}>
            <Select
                {...props}
                loading={data.pending}
                disabled={data.pending}
                options={options}
            />
            <OutlinedRefresh className={c['refresh']} onClick={getList} />
            <Button type="link">
                <ExternalLink
                    href={urls.external.iam.certList.fill()}
                    value="证书管理"
                />
            </Button>
        </div>
    );
}
