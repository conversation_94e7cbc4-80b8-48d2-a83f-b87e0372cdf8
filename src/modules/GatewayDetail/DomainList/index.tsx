import ProTable from '@baidu/icloud-ui-pro-table';
import {useParams} from 'react-router-dom';
import {useCallback} from 'react';
import {getDomainListApi, IGetDomainListParams} from '@/api/gateway';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {formatMultiValueQuery} from '@/utils';
import {columns} from './columns';

import {useOperations} from './operations';
import c from './index.less';

export default function DomainList() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{ gatewayId: string }>();

    const getList = useCallback(
        (params: IGetDomainListParams) => {
            const newParams = formatMultiValueQuery(params, ['protocol', 'isForceHttps']);
            return getDomainListApi({...newParams, serviceMeshInstanceId, gatewayId});
        },
        [gatewayId, serviceMeshInstanceId]
    );

    const operations = useOperations();

    return (
        <div
            className={c['domain-list']}
        >
            <ProTable
                operations={operations}
                request={getList}
                columns={columns}
            />
        </div>
    );
}
