import {Button, message, Modal} from '@osui/ui';
import {useParams} from 'react-router-dom';
import {useCallback, useMemo} from 'react';
import {deleteDomainApi, IDomainListItem} from '@/api/gateway';
import {useCurrentServiceMeshIdSafe} from '@/hooks';

const {confirm} = Modal;

interface IProps {
    record: IDomainListItem;
    refresh: () => void;
}
export default function DeleteDomainButton({record, refresh}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{ gatewayId: string }>();

    const clickModalOk = useCallback(
        async () => {
            try {
                if (record) {
                    const {domains, cert} = record;
                    const deleteDomainParams = {
                        serviceMeshInstanceId,
                        gatewayId,
                        domains,
                    };
                    // 预处理：有证书时的删除，才需传 cert 字段
                    if (cert && cert.id) {
                        Object.assign(deleteDomainParams, {cert});
                    }

                    const res = await deleteDomainApi(deleteDomainParams);
                    if (res) {
                        refresh && refresh();
                        message.success('删除域名监听成功');
                    }
                    else {
                        message.error('删除域名监听失败，请重试');
                        throw new Error('');
                    }
                }
            } catch (error) {
                throw new Error('');
            }
        },
        [serviceMeshInstanceId, gatewayId, record, refresh]
    );

    const config = useMemo(
        () => {
            return {
                title: '删除域名监听',
                content: (
                    <>删除后envoy将丢弃此域名的请求流量，你确定要删除吗？</>
                ),
                onOk: () => {
                    return new Promise(async (resolve, reject) => {
                        try {
                            await clickModalOk();
                            resolve(true);
                        } catch (error) {
                            reject(error);
                        }
                    });
                },
                closable: true,
            };
        },
        [clickModalOk]
    );

    const deleteDomain = useCallback(
        () => {
            confirm(config);
        },
        [config]
    );

    return (
        <Button
            type="link"
            onClick={deleteDomain}
        >
            删除
        </Button>
    );
}
