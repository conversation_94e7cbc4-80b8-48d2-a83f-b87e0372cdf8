import {IColumns} from '@baidu/icloud-ui-pro-table';
import {domainProtocolDict} from '@/dicts/gateway';
import {IDomainListItem, TDomainProtocol} from '@/api/gateway';
import DeleteDomainButton from './DeleteDomainButton';
import EditDomainButton from './EditDomainButton';

const domainProtocolFilterOptions = Object.entries(domainProtocolDict)
    .map(([value, key]) => ({value, text: key}));
const isForceHttpsFilterOptions = [
    {text: '开', value: true},
    {text: '关', value: false},
];

export const columns: IColumns = [
    {
        title: '名称/端口',
        dataIndex: 'port',
        width: 200,
        render(port) {
            const {name = '--', number = '--'} = port ?? {};
            return `${name} ${number}`;
        },
    },
    {
        title: '协议',
        dataIndex: 'protocol',
        filterType: 'single',
        filters: domainProtocolFilterOptions,
        width: 200,
        render(value, record) {
            const {protocol = '--'} = record?.port ?? {};
            return protocol;
        },
    },
    {
        title: '域名',
        dataIndex: 'domains',
        width: 200,
        render(domains) {
            return domains?.map((v: string) => (
                <div key={v}>
                    {v}
                </div>
            ));
        },
    },
    {
        title: '证书',
        dataIndex: 'cert',
        width: 200,
        render(cert) {
            const {name} = cert ?? {};
            return name || '--';
        },
    },
    {
        title: '强制HTTPS',
        dataIndex: 'isForceHttps',
        width: 200,
        filterType: 'single',
        filters: isForceHttpsFilterOptions,
        render(isForceHttps, record) {
            const protocol: TDomainProtocol = record?.port?.protocol ?? '';
            return protocol === 'HTTP'
                ? isForceHttps
                    ? '开'
                    : '关'
                : '--';
        },
    },
    {
        title: '更新时间',
        width: 200,
        dataIndex: 'updateTime',
        renderType: 'dateTime',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        width: 200,
        render(value, record, index, {refresh}) {
            return (
                <>
                    <EditDomainButton record={record as IDomainListItem} refresh={refresh} />
                    <DeleteDomainButton record={record as IDomainListItem} refresh={refresh} />
                </>
            );
        },
    },
];
