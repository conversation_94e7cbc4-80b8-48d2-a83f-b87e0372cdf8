import {OutlinedRight} from '@baidu/acud-icon';
import {Breadcrumb, Tabs} from '@osui/ui';
import {Link, useHistory, useParams} from 'react-router-dom';
import {useBoolean} from 'huse';
import {useCallback, useEffect, useState} from 'react';
import * as Layout from '@/components/Layout';
import urls from '@/urls';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getGatewayBlbList, IGatewayBlbItem} from '@/api/gateway';
import BlbList from './BlbList';
import DomainList from './DomainList';
import InstanceDetail from './InstanceDetail';

import c from './index.less';

type TTabsActiveKey = 'basicConfig' | 'domainManage' | 'logSearch';

export default function GatewayDetail() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{ gatewayId: string }>();

    const [blbListLoading, {on: onBlbListLoading, off: offBlbListLoading}] = useBoolean(false);
    const [blbList, setBlbList] = useState<IGatewayBlbItem[]>([]);
    const getBlbList = useCallback(
        async () => {
            try {
                onBlbListLoading();
                const res = await getGatewayBlbList({
                    serviceMeshInstanceId,
                    gatewayId,
                });
                setBlbList(res.result);
            } catch (error) {
                // console.log(error);
            } finally {
                offBlbListLoading();
            }
        },
        [gatewayId, offBlbListLoading, onBlbListLoading, serviceMeshInstanceId]
    );

    const history = useHistory();
    const {tabId = 'basicConfig'} = useParams<{tabId: TTabsActiveKey}>();
    const [activeKey, setActiveKey] = useState<TTabsActiveKey>(tabId);
    const onChangeActiveKey = useCallback(
        (activeKey: string) => {
            if (activeKey === 'logSearch') {
                return;
            }

            setActiveKey(activeKey as TTabsActiveKey);
            history.replace(urls.gateway.detail.fill({serviceMeshInstanceId, gatewayId, activeKey}));
        },
        [history, serviceMeshInstanceId, gatewayId]
    );

    useEffect(
        () => {
            if (activeKey === 'basicConfig') {
                getBlbList();
            }
        },
        [activeKey, getBlbList]
    );

    return (
        <>
            <Layout.Header>
                <Breadcrumb separator="">
                    <Breadcrumb.Item>
                        <Link to={urls.gateway.list.fill({serviceMeshInstanceId})}>
                            网关管理
                        </Link>
                    </Breadcrumb.Item>
                    <Breadcrumb.Separator>
                        <OutlinedRight />
                    </Breadcrumb.Separator>
                    <Breadcrumb.Item>
                        {gatewayId}
                    </Breadcrumb.Item>
                </Breadcrumb>
            </Layout.Header>

            <Tabs
                className={c['gateway-detail-tabs']}
                onChange={onChangeActiveKey}
                activeKey={activeKey}
                items={[
                    {
                        label: (
                            <Link
                                to={urls.gateway.detail.fill({serviceMeshInstanceId, gatewayId, tabId: 'basicConfig'})}
                            >
                                基础配置
                            </Link>
                        ),
                        key: 'basicConfig',
                    },
                    {
                        label: (
                            <Link
                                to={urls.gateway.detail.fill({serviceMeshInstanceId, gatewayId, tabId: 'domainManage'})}
                            >
                                域名管理
                            </Link>
                        ),
                        key: 'domainManage',
                    },
                    // TODO 后端未ready
                    // {
                    //     label: (
                    //         <ExternalLink
                    //             value={<>日志<OutlinedLink className={c['log-search-link-icon']} /></>}
                    //             href={urls.external.bls.logSearch.fill()}
                    //         />
                    //     ),
                    //     key: 'logSearch',
                    // },
                ]}
            />
            {
                activeKey === 'basicConfig'
                    ? (
                        <>
                            <InstanceDetail />
                            <BlbList blbList={blbList} loading={blbListLoading} getBlbList={getBlbList} />
                        </>
                    )
                    : activeKey === 'domainManage'
                        ? (
                            <DomainList />
                        )
                        : null
            }
        </>
    );
}
