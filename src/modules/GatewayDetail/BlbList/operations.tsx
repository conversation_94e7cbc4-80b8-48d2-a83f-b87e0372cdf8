import {IOperations} from '@baidu/icloud-ui-pro-table';
import {Link} from 'react-router-dom';
import AddButton from '@/components/AddButton';
import urls from '@/urls';
import {IGatewayBlbItem} from '@/api/gateway';

export const genOperations = (
    serviceMeshInstanceId: string,
    gatewayId: string,
    blbList: IGatewayBlbItem[],
    blbListLoading: boolean
): IOperations => {
    return [
        {
            render: () => {
                // TODO 本期暂不支持 绑定 BLB 。
                return (
                    false
                        && (
                            <Link to={urls.gateway.blb.add.fill({serviceMeshInstanceId, gatewayId})}>
                                <AddButton
                                    action="create"
                                    disabled={blbListLoading || blbList?.length > 0}
                                >
                                    绑定 BLB
                                </AddButton>
                            </Link>
                        )
                );
            },
        },
    ];
};
