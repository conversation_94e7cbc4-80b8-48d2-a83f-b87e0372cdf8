import {IFilters} from '@baidu/icloud-ui-pro-table';

// 筛选区
export const filters: IFilters = [
    {
        renderType: 'groupSearch',
        props: {
            options: [
                {
                    label: 'BLB名称',
                    value: 'name',
                },
                {
                    label: 'BLB ID',
                    value: 'shortId',
                },
            ],
        },
        name: ['keywordType', 'keyword'],
        defaultValue: ['shortId', ''],
    },
];
