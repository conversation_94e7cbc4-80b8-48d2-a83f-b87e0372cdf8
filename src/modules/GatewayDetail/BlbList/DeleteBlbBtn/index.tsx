import ProTable from '@baidu/icloud-ui-pro-table';
import {Checkbox, Form, message, Modal} from '@osui/ui';
import {useBoolean} from 'huse';
import {useParams} from 'react-router-dom';
import {useCallback, useMemo} from 'react';
import {deleteGatewayBlbApi, IGatewayBlbItem, IDeleteGatewayBlbParams} from '@/api/gateway';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import c from './index.less';

interface IProps {
    blbItem: IGatewayBlbItem;
    getBlbList: () => void;
}
export default function DeleteBlbBtn({blbItem, getBlbList}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{gatewayId: string}>();
    const {shortId: blbId, eipId} = blbItem;
    const initialValues: IDeleteGatewayBlbParams = useMemo(
        () => {
            return {
                instanceId: serviceMeshInstanceId,
                gatewayId,
                isReleaseBlb: false,
                isReleaseEip: false,
                blbId,
                eipId,
            };
        },
        [blbId, eipId, gatewayId, serviceMeshInstanceId]
    );

    const [form] = Form.useForm();
    const [isModalOpen, {on: onIsModalOpen, off: offIsModalOpen}] = useBoolean(false);
    const [isConfirmLoading, {on: onIsConfirmLoading, off: offIsConfirmLoading}] = useBoolean(false);
    const clickModalOk = useCallback(
        async () => {
            try {
                onIsConfirmLoading();
                const deleteGatewayBlbParams = await form.validateFields();
                await deleteGatewayBlbApi({...initialValues, ...deleteGatewayBlbParams});
                message.success('解绑BLB成功');
                offIsModalOpen();
                getBlbList();
            } catch (error) {
                // console.log(error);
            } finally {
                offIsConfirmLoading();
            }
        },
        [form, initialValues, offIsConfirmLoading, offIsModalOpen, onIsConfirmLoading, getBlbList]
    );

    Form.useLabelLayout('basic', 0);

    return (
        <>
            <ProTable.Operation
                onClick={onIsModalOpen}
            >
                解绑
            </ProTable.Operation>
            <Modal
                className={c['modal']}
                title="解绑BLB"
                open={isModalOpen}
                onOk={clickModalOk}
                confirmLoading={isConfirmLoading}
                onCancel={offIsModalOpen}
            >
                <p>BLB {blbId} 已绑定以下资源，你确定要解绑吗？</p>
                <Form
                    name="basic"
                    labelAlign="left"
                    initialValues={initialValues}
                    form={form}
                    autoComplete="off"
                >
                    <div className={c['blb']}>
                        <Form.Item label={null} name="isReleaseBlb" valuePropName="checked">
                            <Checkbox />
                        </Form.Item>
                        <span className={c['text']}>解绑并删除BLB</span>
                        <ExternalLink
                            value={blbId}
                            href={urls.external.blbDetail.fill(
                                {},
                                {appblbId: blbId}
                            )}
                        />
                    </div>

                    <div className={c['eip']}>
                        <Form.Item label={null} name="isReleaseEip" valuePropName="checked">
                            <Checkbox />
                        </Form.Item>
                        <span className={c['text']}>释放已绑定的 EIP</span>
                        <ExternalLink
                            value={eipId}
                            href={urls.external.eipDetail.fill(
                                {},
                                {eip: eipId, eipType: 'normal'}
                            )}
                        />
                    </div>
                </Form>
            </Modal>
        </>
    );
}
