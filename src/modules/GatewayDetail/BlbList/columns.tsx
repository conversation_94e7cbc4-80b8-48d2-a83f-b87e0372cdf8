import {IColumns} from '@baidu/icloud-ui-pro-table';
import StateTag from '@baidu/icloud-ui-state-tag';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {IBlblistenerItem, TBlbStatus} from '@/api/gateway';
import {blbStatusDict} from '@/dicts/gateway';
import CopyDataWhenHover from '@/components/CopyDataWhenHover';

export const genColumns = (): IColumns => {
    return [
        {
            title: 'BLB名称/ID',
            dataIndex: 'name',
            width: 200,
            render(value, record) {
                const {name, shortId} = record;
                return (
                    <div>
                        <ExternalLink
                            value={name}
                            href={urls.external.blbDetail.fill(
                                {},
                                {appblbId: shortId}
                            )}
                        />
                        <CopyDataWhenHover copyValue={shortId} />
                    </div>
                );
            },
        },
        {
            title: '公⽹地址',
            dataIndex: 'publicIp',
            width: 200,
            render(value) {
                return value || '--';
            },
        },
        {
            title: '内⽹地址',
            dataIndex: 'internalIp',
            width: 200,
        },
        {
            title: '服务器组监听协议／端口',
            dataIndex: 'listenerList',
            width: 200,
            render(value: IBlblistenerItem[]) {
                if (!Array.isArray(value) || value.length === 0) {
                    return '--';
                }

                // TODO 后端暂时无法返回 status 字段。
                return value.map(({type, port}) => {
                    return (
                        <div key={`${type}: ${port}`}>
                            {type}: {port}
                        </div>
                        // <StateTag
                        //     key={`${type}: ${port}`}
                        //     type={blblistenerItemStatusDict[status].type}
                        // >
                        //     {type}: {port}
                        // </StateTag>
                    );
                });
            },
        },
        {
            title: 'BLB 状态',
            dataIndex: 'status',
            width: 200,
            render(value: TBlbStatus) {
                return (
                    <StateTag
                        type={blbStatusDict[value].type}
                    >
                        {blbStatusDict[value].text}
                    </StateTag>
                );
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            renderType: 'dateTime',
            width: 200,
        },
        // TODO 暂不支持操作
        // {
        //     title: '操作',
        //     dataIndex: 'operation',
        //     render(_value, record) {
        //         return (
        //             <ProTable.OperationsWrapper>
        //                 <DeleteBlbBtn blbItem={record as IGatewayBlbItem} getBlbList={getBlbList} />
        //             </ProTable.OperationsWrapper>
        //         );
        //     },
        // },
    ];

};
