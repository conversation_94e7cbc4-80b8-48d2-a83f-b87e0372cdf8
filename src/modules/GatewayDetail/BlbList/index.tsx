import Card from '@baidu/icloud-ui-card';
import ProTable from '@baidu/icloud-ui-pro-table';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {useParams} from 'react-router-dom';
import {Pagination} from '@osui/ui';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {IGatewayBlbItem} from '@/api/gateway';
import {genColumns} from './columns';
import {genOperations} from './operations';
import {filters} from './filters';

interface IProps {
    blbList: IGatewayBlbItem[];
    loading: boolean;
    getBlbList: () => void;
}
export default function BlbList({blbList, loading, getBlbList}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{ gatewayId: string }>();

    const operations = useMemo(
        () => {
            return genOperations(serviceMeshInstanceId, gatewayId, blbList, loading);
        },
        [blbList, gatewayId, loading, serviceMeshInstanceId]
    );
    const columns = genColumns(getBlbList);

    const [dataSource, setDataSource] = useState<IGatewayBlbItem[]>([]);
    const [current, setCurrent] = useState(1);
    const [total, setTotal] = useState(0);
    useEffect(
        () => {
            setDataSource(blbList);
            setTotal(blbList.length);
        },
        [blbList]
    );

    const onFilterChange = useCallback(
        e => {
            const {keywordType, keyword} = e;
            const dataSourceNew = blbList.filter(
                v => (v[`${keywordType}`] as string).toLowerCase().includes((keyword as string).toLowerCase())
            );

            setDataSource(dataSourceNew);
            setTotal(dataSourceNew.length);
            setCurrent(1);
        },
        [blbList]
    );
    const changePager = useCallback(
        pageNo => {
            const pageSize = 10;
            const dataSourceNew = blbList.slice((pageNo - 1) * pageSize, pageNo * pageSize);
            setDataSource(dataSourceNew);
            setCurrent(pageNo);
        },
        [blbList]
    );

    return (
        <Card
            title="网关入口"
        >
            <ProTable
                rowKey="shortId"
                loading={loading}
                dataSource={dataSource.slice(0, 10)}
                columns={columns}
                filters={filters}
                operations={operations}
                requestConfig={
                    {
                        filterByServer: false,
                        pageByServer: false,
                        columnFilterByServer: false,
                    }
                }
                pagination={false}
                onFilterChange={onFilterChange}
            />
            <Pagination
                current={current}
                total={total}
                showQuickJumper
                onChange={changePager}
            />
        </Card>
    );
}
