import Card from '@baidu/icloud-ui-card';
import {Col, Row, Spin} from '@osui/ui';
import {useRequestCallback} from 'huse';
import {useParams} from 'react-router-dom';
import StateTag from '@baidu/icloud-ui-state-tag';
import {createContext, useEffect} from 'react';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getGatewayApi, TGatewayBillingModel, TGatewayStatus} from '@/api/gateway';
import {
    gatewayBillingModelDict,
    gatewayDeployModeDict,
    gatewayGatewayTypeDict,
    gatewayStatusDict,
} from '@/dicts/gateway';
import HpaCardField from '@/modules/GatewayCreate/OrderConfirm/HpaCardField';
import LogCardField from '@/modules/GatewayCreate/OrderConfirm/LogCardField';
import MonitorCardField from '@/modules/GatewayCreate/OrderConfirm/MonitorCardField';
import TlsAccCardField from '@/modules/GatewayCreate/OrderConfirm/TlsAccCardField';
import ResourceQuotaCardField from '@/modules/GatewayCreate/OrderConfirm/ResourceQuotaCardField';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import IngressStatus from './IngressStatus';

export const Context = createContext({getGatewayRequest: () => { }});
export default function InstanceDetail() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{ gatewayId: string }>();
    const [request, {pending, data}] = useRequestCallback(getGatewayApi, {serviceMeshInstanceId, gatewayId});

    useEffect(
        () => {
            request();
        },
        [request]
    );

    return (
        <Context.Provider value={{getGatewayRequest: request}}>
            <Card
                title="实例详情"
            >
                {
                    pending
                    && (
                        <Spin />
                    )
                }
                {
                    !pending && data
                    && (
                        <Row gutter={[48, 16]}>
                            <Col span={8}>
                                <Card.Field title="⽹关名称">
                                    {data.basicConfig.gatewayName}
                                </Card.Field>
                            </Col>

                            <Col span={8}>
                                <Card.Field title="实例ID">
                                    {data.basicConfig.gatewayId}
                                </Card.Field>
                            </Col>

                            <Col span={8}>
                                <Card.Field title="运⾏状态">
                                    <StateTag
                                        type={gatewayStatusDict[data.basicConfig.status as TGatewayStatus].type}
                                    >
                                        {gatewayStatusDict[data.basicConfig.status as TGatewayStatus].text}
                                    </StateTag>
                                </Card.Field>
                            </Col>

                            <Col span={8}>
                                <Card.Field title="付费⽅式">
                                    {gatewayBillingModelDict[data.basicConfig.billingModel as TGatewayBillingModel]}
                                </Card.Field>
                            </Col>

                            <Col span={8}>
                                <Card.Field title="部署⽅式">
                                    {gatewayDeployModeDict[data.basicConfig.deployMode]}
                                </Card.Field>
                            </Col>

                            <Col span={8}>
                                <Card.Field title="⽹关类型">
                                    {gatewayGatewayTypeDict[data.basicConfig.gatewayType]}
                                </Card.Field>
                            </Col>

                            <Col span={8}>
                                <ResourceQuotaCardField resourceQuota={data.basicConfig.resourceQuota} isEdit />
                            </Col>

                            <Col span={8}>
                                <Card.Field title="副本数">
                                    {data.basicConfig.replicas}
                                </Card.Field>
                            </Col>

                            <Col span={8}>
                                <HpaCardField basicConfig={data.basicConfig} isEdit />
                            </Col>

                            <Col span={8}>
                                <LogCardField basicConfig={data.basicConfig} isEdit />
                            </Col>

                            <Col span={8}>
                                <MonitorCardField
                                    source="request"
                                    monitor={data.basicConfig.monitor}
                                    isEdit
                                />
                            </Col>

                            <Col span={8}>
                                <TlsAccCardField tlsAcc={data.basicConfig.tlsAcc} isEdit />
                            </Col>

                            <Col span={8}>
                                <Card.Field title="VPC">
                                    <ExternalLink
                                        href={
                                            urls.external.instanceDetail.fill(
                                                {},
                                                {vpcId: data.networkConfig.networkType.vpcNetworkId}
                                            )
                                        }
                                        value={
                                            // eslint-disable-next-line max-len
                                            `${data.networkConfig.networkType.vpcNetworkName}（${data.networkConfig.networkType.vpcNetworkCidr}）`
                                        }
                                    />
                                </Card.Field>
                            </Col>

                            <Col span={8}>
                                <Card.Field title="⼦⽹">
                                    <ExternalLink
                                        href={
                                            urls.external.subnetDetail.fill(
                                                {},
                                                {subnetId: data.networkConfig.networkType.subnetId}
                                            )
                                        }
                                        value={
                                            // eslint-disable-next-line max-len
                                            `${data.networkConfig.networkType.subnetName}（${data.networkConfig.networkType.subnetCidr}）`
                                        }
                                    />
                                </Card.Field>
                            </Col>

                            <Col span={8}>
                                <IngressStatus />
                            </Col>
                        </Row>
                    )
                }
            </Card>
        </Context.Provider>
    );
}
