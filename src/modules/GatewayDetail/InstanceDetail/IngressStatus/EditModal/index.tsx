import {Form, FormInstance, Modal, Transfer} from '@osui/ui';
import React, {useCallback, useEffect, useState} from 'react';
import {IGatewayIngressItem} from '@/api/gateway';
import c from './index.less';

interface IProps {
    visible: boolean;
    form: FormInstance;
    clusterList: IGatewayIngressItem[];
    confirmLoading: boolean;
    onCancel: () => void;
    onOk: () => void;
}
export default function EditModal({
    visible,
    form,
    clusterList,
    confirmLoading,
    onCancel,
    onOk,
}: IProps) {
    Form.useLabelLayout('basic', 0);

    const [dataSource, setDataSource] = useState<IGatewayIngressItem[]>([]);
    const [targetKeys, setTargetKeys] = useState<string[]>([]);
    const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

    useEffect(
        () => {
            setDataSource((clusterList).map(v => ({...v, key: JSON.stringify(v)})));
            setTargetKeys((clusterList).filter(v => v.enabled).map(v => JSON.stringify(v)));
        },
        [clusterList]
    );


    const render = useCallback(
        (v: IGatewayIngressItem) => `${v.clusterName}/${v.clusterId}`,
        []
    );

    const filterOption = useCallback(
        (inputValue: string, option: IGatewayIngressItem) => {
            return [option.clusterId, option.clusterName]
                .map(v => v?.toLowerCase())
                .some(v => v?.includes(inputValue.toLowerCase()));
        },
        []
    );

    const onChange = useCallback(
        (nextTargetKeys: string[]) => {
            setTargetKeys(nextTargetKeys);
        },
        []
    );

    const onSelectChange = useCallback(
        (sourceSelectedKeys: string[], targetSelectedKeys: string[]) => {
            setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
        },
        [setSelectedKeys]
    );

    const onClickOk = useCallback(
        () => {
            form.setFieldValue(
                'clusterList',
                clusterList.filter(
                    v => targetKeys.includes(JSON.stringify(v))).map(v => ({...v, enabled: true})
                )
            );
            onOk();
        },
        [clusterList, form, onOk, targetKeys]
    );

    return (
        <Modal
            title="ingress 同步"
            visible={visible}
            confirmLoading={confirmLoading}
            onCancel={onCancel}
            onOk={onClickOk}
        >
            <p className={c['tip']}>请选择需要同步ingress配置的集群</p>
            <Form
                name="basic"
                labelAlign="left"
                form={form}
                autoComplete="off"
                colon={false}
            >
                <Form.Item
                    name={['clusterList']}
                >
                    <Transfer
                        showSearch
                        oneWay
                        locale={{searchPlaceholder: '请输入集群名称/ID'}}
                        listStyle={{width: 400, height: 200}}
                        filterOption={filterOption}
                        dataSource={dataSource}
                        targetKeys={targetKeys}
                        selectedKeys={selectedKeys}
                        render={render}
                        pagination={false}
                        onChange={onChange}
                        onSelectChange={onSelectChange}
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
}
