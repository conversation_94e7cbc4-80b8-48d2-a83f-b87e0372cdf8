import {Tooltip} from '@osui/ui';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {IGatewayIngressItem} from '@/api/gateway';

interface IProps {
    clusterList: IGatewayIngressItem[];
    gatewayIngressStatusText: string;
}
export default function ClusterListTooltip({
    clusterList,
    gatewayIngressStatusText,
}: IProps) {
    return (
        <Tooltip
            placement="top"
            title={
                clusterList.filter(v => v.enabled)
                    .map(
                        ({region, clusterId, clusterName}) => (
                            <div
                                key={clusterId}
                            >
                                <ExternalLink
                                    href={
                                        urls.external.clusterDetail.fill(
                                            {},
                                            {clusterUuid: clusterId, clusterName, region}
                                        )
                                    }
                                    value={`${clusterName}/${clusterId}`}
                                />
                            </div>
                        ))
            }
        >
            {gatewayIngressStatusText.slice(0, 30) + '...'}
        </Tooltip>
    );
}
