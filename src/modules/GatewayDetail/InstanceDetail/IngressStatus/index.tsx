import Card from '@baidu/icloud-ui-card';
import {Button, Form, Spin, message} from '@osui/ui';
import {useParams} from 'react-router-dom';
import {useCallback, useEffect, useMemo} from 'react';
import {useBoolean, useRequestCallback} from 'huse';
import {OutlinedEditingSquare} from '@baidu/acud-icon';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getGatewayIngressStatusApi, updateGatewayIngressStatusApi} from '@/api/gateway';
import EditModal from './EditModal';
import ClusterListTooltip from './ClusterListTooltip';
import c from './index.less';

export default function IngressStatus() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {gatewayId} = useParams<{ gatewayId: string }>();

    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);
    const [form] = Form.useForm();
    const [confirmLoading, {on: onConfirmLoading, off: offConfirmLoading}] = useBoolean(false);

    const [
        getGatewayIngressStatus,
        {
            pending: isGatewayIngressStatusLoading,
            data: gatewayIngressStatus,
        },
    ] = useRequestCallback(getGatewayIngressStatusApi, {serviceMeshInstanceId, gatewayId});

    const onOk = useCallback(
        async () => {
            try {
                const values = await form.validateFields();
                onConfirmLoading();
                const rsp = await updateGatewayIngressStatusApi({
                    serviceMeshInstanceId,
                    gatewayId,
                    ...values,
                });
                if (rsp) {
                    message.success('同步成功');
                    offVisible();
                    getGatewayIngressStatus();
                }
                else {
                    message.error('同步失败，请重新操作');
                }
            } catch (error) {
                console.error(error);
            } finally {
                offConfirmLoading();
            }
        },
        [
            form, gatewayId, serviceMeshInstanceId,
            offConfirmLoading, getGatewayIngressStatus, offVisible, onConfirmLoading,
        ]
    );

    useEffect(
        () => {
            getGatewayIngressStatus();
        },
        [getGatewayIngressStatus]
    );
    useEffect(
        () => {
            form.setFieldsValue(gatewayIngressStatus || {});
        },
        [form, gatewayIngressStatus]
    );

    const gatewayIngressStatusText = useMemo(
        () => {
            return (gatewayIngressStatus?.clusterList || []).filter(v => v.enabled)
                .map(v => `${v.clusterName}/${v.clusterId}`)
                .join('、');
        },
        [gatewayIngressStatus]
    );

    return (
        <Card.Field title="ingress 同步" className={c['ingress-status']}>
            {
                isGatewayIngressStatusLoading
                    ? <Spin size="small" />
                    : (
                        <>
                            {
                                gatewayIngressStatusText
                                    ? (
                                        <ClusterListTooltip
                                            clusterList={gatewayIngressStatus?.clusterList || []}
                                            gatewayIngressStatusText={gatewayIngressStatusText}
                                        />
                                    )
                                    : (
                                        <Button disabled type="link">
                                            未配置
                                        </Button>
                                    )
                            }

                            <Button disabled={isGatewayIngressStatusLoading} type="link" onClick={onVisible}>
                                <OutlinedEditingSquare className={c['edit-icon']} />
                            </Button>

                            <EditModal
                                visible={visible}
                                form={form}
                                clusterList={gatewayIngressStatus?.clusterList || []}
                                confirmLoading={confirmLoading}
                                onCancel={offVisible}
                                onOk={onOk}
                            />
                        </>
                    )
            }
        </Card.Field>
    );
}
