import {IColumns} from '@baidu/icloud-ui-pro-table';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';

export const columns: IColumns = [
    {
        title: '日志集名称',
        dataIndex: 'name',
    },
    {
        title: '存储周期',
        dataIndex: 'retention',
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        renderType: 'dateTime',
    },
    {
        title: '保存时间',
        dataIndex: 'updateTime',
        renderType: 'dateTime',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        render() {
            return (
                <ExternalLink href={urls.external.bls.logSearch.fill()} value="查询" />
            );
        },
    },
];
