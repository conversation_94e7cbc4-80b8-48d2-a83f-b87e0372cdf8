import ProTable from '@baidu/icloud-ui-pro-table';
import {QueryResponseBase} from '@/interface';
import {IBlsLogListItem} from '@/api/blsLog';
import CloseBlsLogButton from '../CloseBlsLogButton';
import OpenBlsLogButton from '../OpenBlsLogButton';
import {columns} from './columns';

interface IProps {
    blsLogList: QueryResponseBase<IBlsLogListItem[]>;
    getBlsLogList: () => void;
}
export default function BlsLogList({
    blsLogList,
    getBlsLogList,
}: IProps) {
    return (
        <ProTable
            rowKey="name"
            columns={columns}
            dataSource={blsLogList?.result || []}
            pagination={false}
            emptyDescription={
                <div>
                    <div>日志集存在异常或已被删除</div>
                    <div>
                        {/* eslint-disable-next-line max-len */}
                        你可以 <OpenBlsLogButton type="reselect" getBlsLogList={getBlsLogList} /> 或 <CloseBlsLogButton type="link" getBlsLogList={getBlsLogList} />
                    </div>
                </div>
            }
        />
    );
}
