import {Image} from 'antd';
import guidelinePng from '@/assets/blsLog/guideline.png';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import OpenBlsLogButton from '../OpenBlsLogButton';
import c from './index.less';

interface IProps {
    getBlsLogList: () => void;
}
export default function OpenBlsLogGuideline({
    getBlsLogList,
}: IProps) {
    return (
        <div className={c['openBlsLogGuideline']}>
            <Image className={c['guidelinePng']} src={guidelinePng} preview={false} />
            <div className={c['title']}>还未开启日志中心（BLS）</div>
            <div className={c['description']}>
                <p>
                    {/* eslint-disable-next-line max-len */}
                    CSM Sidecar 会将访问日志输出至容器标准日志，你可以在当前页查看和检索日志内容。日志服务会按照您的实际使用情况计费，付费信息查看 <ExternalLink href={urls.external.doc.bls.help.fill()} value="计费规则" /> 。
                </p>
                <p>
                    开启日志中心需安装日志组件 CCE Log Operator ，请确保组件安装。
                </p>
            </div>

            <OpenBlsLogButton type="guideline" getBlsLogList={getBlsLogList} />
        </div>
    );
}
