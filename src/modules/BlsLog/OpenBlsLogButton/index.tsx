import {Select} from 'antd';
import {useCallback, useMemo} from 'react';
import {Button, Form, Modal, Spin, message} from '@osui/ui';
import {useBoolean, useRequestCallback} from 'huse';
import AddButton from '@/components/AddButton';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {createBlsLogTaskApi, getBlsAgentCheckApi, getBlsLogStoreListApi} from '@/api/blsLog';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {nameRules} from './constant';
import c from './index.less';

interface IProps {
    type: 'guideline' | 'reselect';
    getBlsLogList: () => void;
}
export default function OpenBlsLogButton({
    type = 'guideline',
    getBlsLogList,
}: IProps) {
    const [form] = Form.useForm();
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);
    const [confirmLoading, {on: onConfirmLoading, off: offConfirmLoading}] = useBoolean(false);

    const [
        getBlsLogStoreList,
        {pending: getBlsLogStoreListLoading, data: getBlsLogStoreListData},
    ] = useRequestCallback(getBlsLogStoreListApi, undefined);

    const [checkLoading, {on: onCheckLoading, off: offCheckLoading}] = useBoolean(false);

    const blsLogStoreOptions = useMemo(
        () => {
            return (getBlsLogStoreListData?.result || []).map(v => {
                return {
                    label: `${v.name}（${v.retention}天）`,
                    value: v.name,
                };
            });
        },
        [getBlsLogStoreListData?.result]
    );

    const openBlsLog = useCallback(
        async () => {
            try {
                onCheckLoading();
                const rsp = await getBlsAgentCheckApi({serviceMeshInstanceId});
                const {isExist, clusterId: clusterUuid} = rsp || {};
                if (isExist) {
                    onVisible();
                    getBlsLogStoreList();
                }
                else {
                    Modal.confirm({
                        title: '还未安装日志组件',
                        content: <>开启日志服务前，需要安装日志组件（CCE Log Operator），请先安装。</>,
                        closable: true,
                        cancelText: '取消',
                        okText: (
                            <ExternalLink
                                value="去安装"
                                href={urls.external.cce.installComponent.fill({}, {clusterUuid})}
                            />
                        ),
                    });
                }
            } catch (error) {
                console.error(error);
            } finally {
                offCheckLoading();
            }
        },
        [getBlsLogStoreList, offCheckLoading, onCheckLoading, onVisible, serviceMeshInstanceId]
    );

    const onClickOK = useCallback(
        async () => {
            try {
                const {name = ''} = await form.validateFields();
                onConfirmLoading();
                const rsp = await createBlsLogTaskApi({serviceMeshInstanceId, name});
                offConfirmLoading();
                if (rsp) {
                    offVisible();
                    message.success('日志中心开启成功');
                    getBlsLogList();
                }
                else {
                    message.error('日志中心开启失败');
                    return Promise.reject(new Error('日志中心开启失败'));
                }
            } catch (error) {
                console.error(error);
            }
        },
        [form, getBlsLogList, offConfirmLoading, offVisible, onConfirmLoading, serviceMeshInstanceId]
    );

    return (
        <div className={c['openBlsLogButton']}>
            {
                checkLoading
                    ? <Spin />
                    : type === 'guideline'
                        ? (
                            <AddButton
                                type="primary"
                                action="create"
                                icon={null}
                                onClick={openBlsLog}
                            >
                                立即开启
                            </AddButton>
                        )
                        : <Button type="link" onClick={openBlsLog}>重新选择</Button>
            }

            <Modal
                title={type === 'guideline' ? '开启日志服务' : '重新选择日志集'}
                visible={visible}
                confirmLoading={confirmLoading}
                onCancel={offVisible}
                onOk={onClickOK}
            >
                <Form
                    name="basic"
                    labelAlign="left"
                    form={form}
                >
                    <Form.Item
                        label="选择日志集"
                        required
                        rules={nameRules}
                        name={['name']}
                        help={
                            <>
                                <div>建议使用存储时长≥30天的日志集</div>
                                没有合适的日志集，可以<ExternalLink href={urls.external.bls.logList.fill()} value="去创建日志集" />
                            </>
                        }
                    >
                        <Select
                            showSearch
                            placeholder="请选择日志集"
                            loading={getBlsLogStoreListLoading}
                            options={blsLogStoreOptions}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
}
