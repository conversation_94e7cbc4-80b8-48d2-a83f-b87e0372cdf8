import {Button, Modal, message} from '@osui/ui';
import {useCallback} from 'react';
import {ButtonType} from 'antd/lib/button';
import {deleteBlsLogApi} from '@/api/blsLog';
import {useCurrentServiceMeshIdSafe} from '@/hooks';

interface IProps {
    type?: ButtonType;
    getBlsLogList: () => void;
}
export default function CloseBlsLogButton({
    type = 'default',
    getBlsLogList,
}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const deleteBlsLog = useCallback(
        () => {
            Modal.confirm({
                title: '确定关闭日志服务吗？',
                content: '关闭后数据面 Sidecar 日志将无法持久化存储，请谨慎操作。',
                async onOk() {
                    try {
                        const rsp = await deleteBlsLogApi({serviceMeshInstanceId});
                        if (rsp) {
                            message.success('关闭数据面日志成功');
                            getBlsLogList();
                        }
                    } catch (error) {
                        message.error('关闭数据面日志失败，请重试');
                        return Promise.reject(new Error('删除失败，请重新操作。'));
                    }
                },
                closable: true,
            });
        },
        [serviceMeshInstanceId, getBlsLogList]
    );

    return (
        <Button
            type={type}
            onClick={deleteBlsLog}
        >
            关闭服务
        </Button>
    );
}
