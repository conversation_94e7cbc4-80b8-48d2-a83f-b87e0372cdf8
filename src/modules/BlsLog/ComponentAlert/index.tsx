import {Alert} from 'acud';
import {useEffect, useMemo} from 'react';
import {useRequestCallback} from 'huse';
import {Spin} from '@osui/ui';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getBlsAgentCheckApi} from '@/api/blsLog';
import c from './index.less';

export default function ComponentAlert() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const [
        getBlsAgentCheck,
        {pending: getBlsAgentCheckLoading, data: getBlsAgentCheckData},
    ] = useRequestCallback(getBlsAgentCheckApi, {serviceMeshInstanceId});

    useEffect(
        () => {
            getBlsAgentCheck();
        },
        [getBlsAgentCheck]
    );

    const isExist = useMemo(
        () => {
            return getBlsAgentCheckData?.isExist;
        },
        [getBlsAgentCheckData?.isExist]
    );

    const clusterUuid = useMemo(
        () => {
            return getBlsAgentCheckData?.clusterId || '';
        },
        [getBlsAgentCheckData?.clusterId]
    );

    return getBlsAgentCheckLoading
        ? <Spin />
        : isExist
            ? null
            : (
                <Alert
                    className={c['alert']}
                    message={
                        <div className={c['content']}>
                            <div>日志组件（CCE Log Operator）未安装，为了不影响使用日志服务，请去安装组件</div>
                            <ExternalLink
                                href={urls.external.cce.installComponent.fill({}, {clusterUuid})}
                                value="安装组件"
                            />
                        </div>
                    }
                    type="error"
                />
            );
}
