import Card from '@baidu/icloud-ui-card';
import {useRequestCallback} from 'huse';
import {Spin} from '@osui/ui';
import {useEffect} from 'react';
import {OutlinedRefresh} from '@baidu/acud-icon';
import {Button} from 'acud';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getBlsLogListApi} from '@/api/blsLog';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import BlsLogList from './BlsLogList';
import CloseBlsLogButton from './CloseBlsLogButton';
import OpenBlsLogGuideline from './OpenBlsLogGuideline';
import c from './index.less';
import ComponentAlert from './ComponentAlert';

export default function BlsLog() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const [
        getBlsLogList,
        {pending: getBlsLogListLoading, data: blsLogList},
    ] = useRequestCallback(getBlsLogListApi, {serviceMeshInstanceId});

    useEffect(
        () => {
            getBlsLogList();
        },
        [getBlsLogList]
    );

    return (
        getBlsLogListLoading
            ? (
                <div className={c['blsLogWrapper']}>
                    <Spin />
                </div>
            )
            : blsLogList?.status === 'Close'
                ? (
                    <div className={c['blsLogWrapper']}>
                        <OpenBlsLogGuideline getBlsLogList={getBlsLogList} />
                    </div>
                )
                : (
                    <Card
                        className={c['blsLog']}
                        title={
                            <div className={c['title-wrapper']}>
                                <div className={c['title']}>日志中心</div>
                                <div className={c['subtitle']}>
                                    支持容器日志持久化存储，更多信息查看
                                    <ExternalLink href={urls.external.doc.bls.help.fill()} value="帮助文档" />、
                                    <ExternalLink href={urls.external.doc.bls.price.fill()} value="计费规则" />
                                </div>
                            </div>
                        }
                        extra={
                            <div className={c['extra']}>
                                <Button className={c['refresh']} onClick={getBlsLogList}>
                                    <OutlinedRefresh />
                                </Button>

                                <CloseBlsLogButton getBlsLogList={getBlsLogList} />
                            </div>
                        }
                    >
                        <ComponentAlert />

                        <BlsLogList
                            blsLogList={blsLogList as any}
                            getBlsLogList={getBlsLogList}
                        />
                    </Card>
                )
    );
}
