import {IColumns} from '@baidu/icloud-ui-pro-table';
import {Link} from 'react-router-dom';
import urls from '@/urls';
import {CrdType} from '@/dicts/crd';

export function entryColumns(serviceMeshInstanceId: string) {
    const columns: IColumns = [
        {
            title: '名称',
            dataIndex: 'name',
            tooltip: true,
            render(value, record) {
                const kind = CrdType.ServiceEntry;
                const {name, namespace} = record;
                return (
                    <Link
                        to={
                            urls.crd.view.fill(
                                {serviceMeshInstanceId, namespace: namespace || '*'},
                                {kind, name}
                            )
                        }
                    >
                        {value}
                    </Link>
                );
            },
        },
        {
            title: '所在命名空间',
            dataIndex: 'namespace',
        },
        {
            title: '生效命名空间',
            dataIndex: 'exportTo',
            renderType: 'text',
        },
    ];

    return columns;
}
