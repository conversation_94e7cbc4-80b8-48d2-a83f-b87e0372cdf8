import {IFilters} from '@baidu/icloud-ui-pro-table';
export const k8sFilters: IFilters = [
    {
        name: ['field', 'inputValue'],
        renderType: 'groupSearch',
        onFilter: (value, record) => {
            const [field, inputValue] = value || [];
            return !inputValue || (field && record[field].includes(inputValue));
        },
        defaultValue: ['clusterName', ''],
        props: {
            options: [
                {
                    label: '集群名称',
                    value: 'clusterName',
                },
                {
                    label: '集群ID',
                    value: 'clusterId',
                },
                {
                    label: '服务名称',
                    value: 'serviceName',
                },
            ],
        },
    },
    {
        renderType: 'refresh',
    },
];
export const entryFilters: IFilters = [
    {
        name: ['name'],
        renderType: 'search',
        onFilter: (value, record) => {
            const [field, inputValue] = value || [];
            return !inputValue || (field && record[field].includes(inputValue));
        },
        props: {
            placeholder: '请输入名称进行搜索',
        },
    },
    {
        renderType: 'refresh',
    },
];
