import Card from '@baidu/icloud-ui-card';
import ProTable from '@baidu/icloud-ui-pro-table';
import {useParams, Link} from 'react-router-dom';
import {useRequest} from 'huse';
import {Breadcrumb} from '@osui/ui';
import styled from 'styled-components';
import {OutlinedRight} from '@baidu/acud-icon';
import * as Layout from '@/components/Layout';
import {getServiceDetailsApi} from '@/api/service';
import urls from '@/urls';
import {k8sColumns} from './k8sColumns';
import {entryColumns} from './entryColumns';
import c from './index.less';
import {entryFilters, k8sFilters} from './tableConfig';

const BreadcrumbStyle = styled(Breadcrumb)`
    .ant-breadcrumb-separator {
        margin: 0 8px;
        vertical-align: 1px
    }
`;

export default function ServiceDetails() {
    const {host, serviceMeshInstanceId} = useParams<{host: string, serviceMeshInstanceId: string}>();

    const {data, pending} = useRequest(getServiceDetailsApi,
        {
            serviceMeshInstanceId: serviceMeshInstanceId,
            host: host,
        }
    );
    if (pending || !data) {
        return null;
    }
    const {k8sServiceDetailInfoList, serviceEntryDetailInfoList} = data;
    return (
        <div>
            <Layout.Header>
                <BreadcrumbStyle separator="">
                    <Breadcrumb.Item className={c.textColor1}>
                        <Link to={urls.serviceList.fill({serviceMeshInstanceId})}>
                            服务列表
                        </Link>
                    </Breadcrumb.Item>
                    <Breadcrumb.Separator>
                        <OutlinedRight />
                    </Breadcrumb.Separator>
                    <Breadcrumb.Item className={c.textColor2}>
                        {host}
                    </Breadcrumb.Item>
                </BreadcrumbStyle>
            </Layout.Header>
            <Card title="K8S Service" className={c.serviceDetailTableTools}>
                <ProTable
                    rowKey="clusterId"
                    filters={k8sFilters}
                    columns={k8sColumns}
                    dataSource={k8sServiceDetailInfoList || []}
                    pagination={false}
                    requestConfig={{
                        pageByServer: false,
                        filterByServer: false,
                    }}
                />
            </Card>
            <Card title="Service Entry" className={c.serviceDetailTableTools}>
                <ProTable
                    rowKey="name"
                    filters={entryFilters}
                    columns={entryColumns(serviceMeshInstanceId)}
                    dataSource={serviceEntryDetailInfoList || []}
                    pagination={false}
                    requestConfig={{
                        pageByServer: false,
                        filterByServer: false,
                    }}
                />
            </Card>
        </div>
    );
}
