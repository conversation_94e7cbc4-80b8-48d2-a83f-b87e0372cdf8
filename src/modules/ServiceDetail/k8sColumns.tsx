import {IColumns} from '@baidu/icloud-ui-pro-table';
import Region from '@/components/Region';
import urls from '@/urls';
import ExternalLink from '@/components/ExternalLink';
export const k8sColumns: IColumns = [
    {
        title: '集群名称/ID',
        dataIndex: 'clusterId',
        render(_, record) {
            return (
                <>
                    <div>{record.clusterName}</div>
                    <div>{record.clusterId}</div>
                </>
            );
        },
    },
    {
        title: '地域',
        dataIndex: 'region',
        render(value) {
            return <Region value={value} />;
        },
    },
    {
        title: '服务名称',
        dataIndex: 'serviceName',
        render(value, record) {
            const {clusterId: clusterUuid, namespace: namespaceName, serviceName} = record;
            return (
                <ExternalLink
                    href={urls.external.serviceDetail.fill({}, {clusterUuid, namespaceName, serviceName})}
                    value={value}
                />
            );
        },
    },
    // TODO 后端缺了几列（相应的外链后面再继续加）
];
