
import {useCallback} from 'react';
import {Checkbox} from '@osui/ui';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import c from './index.less';

interface IProps {
    isAgree: boolean; // 初始值
    onAgreeChange: (v: boolean) => void;
}
export default function AgreeProtocol({
    isAgree,
    onAgreeChange,
}: IProps) {
    const onCheckboxChange = useCallback(
        e => {
            onAgreeChange(e.target.checked);
        },
        [onAgreeChange]
    );

    return (
        <div className={c['agree-protocol']}>
            <Checkbox checked={isAgree} onChange={onCheckboxChange} />
            <span>同意<ExternalLink href={urls.external.cloudAgreement.fill()} value="《百度智能云协议》" /></span>
        </div>
    );
}
