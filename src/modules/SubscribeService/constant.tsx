import urls from '@/urls';
import bg1920URL from '@/assets/launch/<EMAIL>';
import bg1680URL from '@/assets/launch/<EMAIL>';
import bg1440URL from '@/assets/launch/<EMAIL>';
import bg1280URL from '@/assets/launch/<EMAIL>';

export const bgURL = {
    1280: bg1280URL,
    1440: bg1440URL,
    1680: bg1680URL,
    1920: bg1920URL,
};

export const processData = [{
    stepName: '创建网格实例',
    stepContent: '通过控制台快速创建实例，实例与原生 Istio & Envoy api完全兼容',
    link: urls.external.csmDocCreateMeshInstance.path(),
    linkText: '了解更多>>',
}, {
    stepName: '纳管 Kubernets集群',
    stepContent: '多个 Kubernets集群的添加，支持发现、跨集群流量调度操作',
    link: urls.external.csmDocClusterManage.path(),
    linkText: '了解更多>>',
}, {
    stepName: '配置 Sidecar注入策略',
    stepContent: 'Sidecar自动注入和移除，自动完成服务的网格化改造',
    link: urls.external.csmDocSidecarInjection.path(),
    linkText: '了解更多>>',
}, {
    stepName: '配置治理规则',
    stepContent: '通过 Istio CRD灵活配置，实现丰富的流量治理规则',
    link: urls.external.csmDocIstioManage.path(),
    linkText: '了解更多>>',
}, {
    stepName: '运维监控网格',
    stepContent: '通过服务网格内置仪表盘和自定义的监控配置，了解服务网格和所治理的服务运行状态',
    link: urls.external.csmDocCprom.path(),
    linkText: '了解更多>>',
}];
