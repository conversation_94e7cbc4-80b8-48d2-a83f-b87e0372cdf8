import {But<PERSON>, Popover} from '@osui/ui';
import {useHistory} from 'react-router-dom';
import {useCallback, useMemo, useState} from 'react';
import {message} from 'antd';
import urls from '@/urls';
import {setAuthorizeState} from '@/utils/authorize';
import {subScribeServiceApi, subScribeServiceParams} from '@/api/authorize';
import {
    POLICY_ID,
    QASANDBOX_POLICY_ID,
    QASANDBOX_SERVICE_ID,
    ROLE_NAME,
    SERVICE_ID,
    ROLE_NAME_BLB,
    POLICY_ID_BLB,
    SERVICE_ID_BLB,
    QASANDBOX_POLICY_ID_BLB,
    QASANDBOX_SERVICE_ID_BLB,
} from '@/dicts/authorize';
import {IS_QASANDBOX} from '@/dicts';
import {useFramework} from '@/hooks';
import c from './index.less';

interface IProps {
    isAgree: boolean;
}
export default function SubscribeButton({
    isAgree,
}: IProps) {
    const history = useHistory();
    const {account: {accountId, verifyStatus}} = useFramework(); // 是否实名认证了
    const [loading, setLoading] = useState(false);

    const subscribeButtonHelp = useMemo(
        () => {
            if (!verifyStatus) {
                return '请先完成实名认证';
            }
            if (!isAgree) {
                return '请先同意《百度智能云协议》';
            }
            return '';
        },
        [verifyStatus, isAgree]
    );
    const handleSubscribeService = useCallback( // 开通协议
        async () => {
            try {
                const params: subScribeServiceParams = {
                    accountId,
                    policyId: '',
                    roleName: ROLE_NAME,
                    serviceId: '',
                };
                const paramsBlb: subScribeServiceParams = {
                    accountId,
                    policyId: '',
                    roleName: ROLE_NAME_BLB,
                    serviceId: '',
                };

                if (IS_QASANDBOX) {
                    params.policyId = QASANDBOX_POLICY_ID;
                    params.serviceId = QASANDBOX_SERVICE_ID;
                    paramsBlb.policyId = QASANDBOX_POLICY_ID_BLB;
                    paramsBlb.serviceId = QASANDBOX_SERVICE_ID_BLB;
                } else {
                    params.policyId = POLICY_ID;
                    params.serviceId = SERVICE_ID;
                    paramsBlb.policyId = POLICY_ID_BLB;
                    paramsBlb.serviceId = SERVICE_ID_BLB;
                }

                setLoading(true);
                const [res, resBlb] = await Promise.all([
                    subScribeServiceApi(params),
                    subScribeServiceApi(paramsBlb),
                ]);
                // 根据cfc沙盒环境注册的账号开看，好像注册成功后，只返回 {success: true, status: 200} 并没有 result
                if (res === undefined && resBlb === undefined) {
                    setAuthorizeState(true);
                    message.success('开通成功');
                    setLoading(false);
                    history.push(urls.home.fill()); // 跳转到首页
                } else {
                    setLoading(false);
                }
            } catch (err) {
                message.error('开通失败');
                setLoading(false);
            }
        },
        [history, accountId]
    );

    return (
        <div className={c['subscribe-button']}>
            {
                (verifyStatus && isAgree)
                    ? (
                        <Button type="primary" loading={loading} onClick={handleSubscribeService}>申请开通</Button>
                    )
                    : (
                        <Popover content={subscribeButtonHelp}>
                            <Button disabled={!verifyStatus || !isAgree} type="primary">申请开通</Button>
                        </Popover>
                    )
            }
        </div>
    );
}
