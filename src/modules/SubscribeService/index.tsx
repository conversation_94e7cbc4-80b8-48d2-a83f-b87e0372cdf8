import ActivationPage from '@baidu/icloud-ui-activation-page';
import {useCallback, useState} from 'react';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import productLogo from '@/assets/launch/productLogo.png';
import AgreeProtocol from './AgreeProtocol';
import {bgURL, processData} from './constant';
import SubscribeButton from './SubscribeButton';
import c from './index.less';

const {OpenModule, ProcessModule} = ActivationPage;
export default function SubscribeService() {
    const [isAgree, setIsAgree] = useState(false);

    const onAgreeChange = useCallback(
        v => {
            setIsAgree(v);
        },
        []
    );

    return (
        <ActivationPage>
            <OpenModule
                backgroundSrc={bgURL}
                logoSrc={productLogo}
                title="服务网格 CSM——多语言统一微服务治理平台"
                // eslint-disable-next-line max-len
                content="服务网格 CSM，多语言统一微服务治理平台。全面兼容Istio服务网格，提供丰富的负载均衡、路由转发、超时重试、熔断限流、加密鉴权策略，支持跨集群、多协议、精细化服务治理，使服务间调用更加便捷、稳定、安全、易于追踪、便于管理。"
                tip={
                    <>
                        开通 CSM服务网格产品，为方便使用，将自动在 IAM中为您创建服务角色并授权相关数据调用权限。并同时为您默认开通
                        <ExternalLink
                            className={c['blb-doc-link']}
                            href={urls.external.blbDoc.path()}
                            value="负载均衡BLB"
                        />、
                        <ExternalLink
                            className={c['cce-doc-link']}
                            href={urls.external.cceDoc.path()}
                            value="容器引擎CCE"
                        />、
                        <ExternalLink
                            className={c['vpc-doc-link']}
                            href={urls.external.vpcDoc.path()}
                            value="私有网络VPC"
                        />
                        2个云产品。
                    </>
                }
                agreement={<AgreeProtocol isAgree={isAgree} onAgreeChange={onAgreeChange} />}
                button={<SubscribeButton isAgree={isAgree} />}
            />

            <ProcessModule title="流程引导" data={processData} />
        </ActivationPage>
    );
}
