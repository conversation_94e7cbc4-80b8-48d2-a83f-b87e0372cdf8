import {IColumns} from '@baidu/icloud-ui-pro-table';
import StateTag from '@baidu/icloud-ui-state-tag';
import {Link} from 'react-router-dom';
import {Button} from '@osui/ui';
import {RegistractionStatusDict} from '@/dicts/registration';
import urls from '@/urls';
import CopyDataWhenHover from '@/components/CopyDataWhenHover';
import DeleteRegistration from '@/components/DeleteRegistration';

export default function getColumns(regionList: any) {
    const baseColumns: IColumns = [{
        title: '名称/ID',
        dataIndex: 'name',
        width: 200,
        render(_, record) {
            const {id: registrationId, name} = record;
            return (
                <>
                    <div>
                        <Link to={urls.registrationBaseInfo.fill({registrationId},
                            {registrationId, name}
                        )}
                        >
                            {name}
                        </Link>
                    </div>
                    <CopyDataWhenHover copyValue={record.id} />
                </>
            );
        },
    }, {
        title: '地域',
        dataIndex: 'region',
        render(value: string) {
            return regionList[value] || '-';
        },
    }, {
        title: '状态',
        dataIndex: 'status',
        render(value: number) {
            const data = RegistractionStatusDict.get(value);
            return data ? <StateTag type={data.value}>{data.text}</StateTag> : null;
        },
    }, {
        title: '创建时间',
        dataIndex: 'createTime',
        renderType: 'dateTime',
    }, {
        title: '操作',
        render(_, record, index, {refresh}) {
            return (
                <div className="flex">
                    <DeleteRegistration record={record} callback={refresh}>
                        <Button
                            type="link"
                        >
                            删除
                        </Button>
                    </DeleteRegistration>
                </div>
            );
        },
    }];
    return baseColumns;
}
