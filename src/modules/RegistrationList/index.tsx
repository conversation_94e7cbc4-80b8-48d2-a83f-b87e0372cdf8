import React, {useCallback} from 'react';
import ProTable, {IOperations, ITools} from '@baidu/icloud-ui-pro-table';
import styled from 'styled-components';
import {Link} from 'react-router-dom';

import * as Layout from '@/components/Layout';
import {LayoutHeaderWrap} from '@/components/HeaderWrap';
import urls from '@/urls';
import AddButton from '@/components/AddButton';
import {useListTotal} from '@/hooks/useList';
import {formatMultiValueQuery} from '@/utils';
import {getRegistrationListApi} from '@/api/registration';
import {useFramework} from '@/hooks';
import {useApiWithRegion} from '../../hooks/useApiWithRegion';
import getColumns from './baseColumns';


const ListHeaderWrap = styled(LayoutHeaderWrap)`
    .ant-page-header.osui-page-header {
        padding: 12px 16px;
    }
`;

const tools: ITools = [
    {
        renderType: 'refresh',
    },
];

const RegistrationList = () => {
    const {region: {regionList}} = useFramework();


    const {request} = useListTotal(useApiWithRegion(getRegistrationListApi));
    const getList = useCallback(
        params => {
            const newParams = formatMultiValueQuery(params, ['instanceStatus']);
            return request(newParams);
        },
        [request]
    );

    const operations: IOperations = [
        {
            render() {
                return (
                    <Link to={urls.createRegistration.fill()}>
                        <AddButton>新建</AddButton>
                    </Link>
                );
            },
        },
    ];

    return (
        <>
            <ListHeaderWrap>
                <Layout.Header
                    title="注册中心实例列表"
                />
            </ListHeaderWrap>
            <Layout.MainContent>
                <ProTable
                    rowKey="instanceId"
                    tools={tools}
                    columns={getColumns(regionList)}
                    operations={operations}
                    request={getList}
                />
            </Layout.MainContent>
        </>
    );
};

export default RegistrationList;
