import {Switch, Route} from 'react-router-dom';
import RegistrationInstanceDetail from '@/modules/RegistrationBaseInfo';
import RegistrationNamespace from '@/modules/RegistrationNamespace';
import RegistrationInstanceMonitor from '@/modules/RegistrationMonitor';
import RegistrationInstanceParams from '@/modules/RegistrationParams';
import urls from '@/urls';

export const routes = (
    <Switch>
        <Route path={urls.registrationBaseInfo.path()} component={RegistrationInstanceDetail} />
        <Route path={urls.registrationNamespaces.path()} component={RegistrationNamespace} />
        <Route path={urls.registrationMonitor.path()} component={RegistrationInstanceMonitor} />
        <Route path={urls.registrationParams.path()} component={RegistrationInstanceParams} />
    </Switch>
);
