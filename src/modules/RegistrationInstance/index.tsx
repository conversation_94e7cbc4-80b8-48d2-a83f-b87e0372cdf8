import {Link, useHistory, useLocation} from 'react-router-dom';
import React, {createContext, useCallback, useContext, useEffect} from 'react';
import {useRequestCallback} from 'huse';
import {Menu} from '@osui/ui';
import styled from 'styled-components';
import {getRegistrationInstanceApi} from '@/api/registration';
import {useFramework, useCurRegistrationId} from '@/hooks';
import {R4} from '@/styles';
import urls from '@/urls';
import PageLoading from '@/components/PageLoading';
import {REGISTRATION_LIST} from '@/links';
import useVisibilityChange from '@/hooks/useVisibilityChange';
import {routes} from './routes';
import RegistrationInstanceHeader from './RegistrationInstanceHeader';

const Main = styled.div`
    background-color: white;
    border-radius: ${R4};
    overflow: hidden;
    margin: 16px;
    min-height: calc(100vh - 50px - 48px - 32px);
`;

const MenuContainerStyled = styled.div`
    height: inherit;
    border-right: 1px solid #f2f2f4;
`;
// @todo 没写完
const defaultValue = {
    name: '',
    id: '',
    region: '',
    status: 0,
    monitorInstanceId: '',
    createTime: '',
    loadBalanceList: [],
    updateTime: '',
    serverPort: '',
    serverProtocol: '',
};
export const CurrentRegistrationInstanceContext = createContext({data: defaultValue, getRequest: () => { }});
const CurrentRegistrationInstanceContextProvider = CurrentRegistrationInstanceContext.Provider;
export const useCurrentRegistrationInstance = () => {
    return useContext(CurrentRegistrationInstanceContext).data;
};
export default function ServiceInstance() {
    const history = useHistory();
    const {pathname} = useLocation();
    const registrationId = useCurRegistrationId();

    const [request, {data}] = useRequestCallback(getRegistrationInstanceApi, {
        registrationhInstanceId: registrationId,
    });
    const {region: {onRegionChange, unRegionChange}} = useFramework();
    const goToServiceMeshList = useCallback(
        () => {
            history.push(REGISTRATION_LIST);
        },
        [history]
    );
    useEffect(
        () => {
            onRegionChange(goToServiceMeshList);
            return () => unRegionChange(goToServiceMeshList);
        },
        [goToServiceMeshList, onRegionChange, unRegionChange]
    );

    useEffect(
        () => {
            request();
        },
        [request]
    );
    useVisibilityChange({onShow: request});

    const SERVICE_REGISTRATION_INFO_PATH = urls.registrationBaseInfo.fill({registrationId});
    const SERVICE_NAMESPACE_PATH = urls.registrationNamespaces.fill({registrationId});
    const REGISTRATION_MONITOR_PATH = urls.registrationMonitor.fill({registrationId});
    const REGISTRATION_PARAMS_PATH = urls.registrationParams.fill({registrationId});

    return (
        <CurrentRegistrationInstanceContextProvider value={{data: data, getRequest: request}}>
            <div>
                <RegistrationInstanceHeader />
                <Main className="flex">
                    <MenuContainerStyled className="flex-none">
                        <Menu
                            items={
                                [
                                    {
                                        label: <Link to={SERVICE_REGISTRATION_INFO_PATH}>基本信息</Link>,
                                        key: SERVICE_REGISTRATION_INFO_PATH,
                                    },
                                    {
                                        label: <Link to={SERVICE_NAMESPACE_PATH}>命名空间</Link>,
                                        key: SERVICE_NAMESPACE_PATH,
                                    },
                                    {
                                        label: <Link to={REGISTRATION_MONITOR_PATH}>Prometheus 监控</Link>,
                                        key: REGISTRATION_MONITOR_PATH,
                                    },
                                    {
                                        label: <Link to={REGISTRATION_PARAMS_PATH}>参数配置</Link>,
                                        key: REGISTRATION_PARAMS_PATH,
                                    },
                                ]
                            }
                            selectedKeys={[pathname]}
                            className="secondary-menu"
                            mode="inline"
                        />
                    </MenuContainerStyled>

                    <div className="flex-auto">
                        {data ? routes : <PageLoading />}
                    </div>
                </Main>
            </div>
        </CurrentRegistrationInstanceContextProvider>
    );
}
