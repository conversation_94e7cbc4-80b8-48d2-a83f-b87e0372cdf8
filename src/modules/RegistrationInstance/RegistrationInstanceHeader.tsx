import {Spin} from '@osui/ui';
import {useHistory} from 'react-router-dom';
import {useCallback} from 'react';
import styled from 'styled-components';
import StateTag from '@baidu/icloud-ui-state-tag';
import * as Layout from '@/components/Layout';
import {useCurrentRegistrationInstance} from '@/modules/RegistrationInstance';
import {REGISTRATION_LIST} from '@/links';
import {RegistractionStatusDict} from '@/dicts/registration';
import ProductDocLink from '@/components/ProductDocLink';

const TitleAreaStyled = styled.div`
    display: flex;
`;
const TitleStyled = styled.section`
    margin-right: 12px;
    font-size: var(--font-size-large);
`;

interface TitleAreaProps{
    title: string;
    status: number;
}
function TitleArea({title, status}: TitleAreaProps) {
    const data = RegistractionStatusDict.get(status) ?? {};
    return (
        <TitleAreaStyled>
            <TitleStyled>{title}</TitleStyled>
            <StateTag type={data.value}>{data.text}</StateTag>
        </TitleAreaStyled>
    );
}

export default function RegistrationInstanceHeader() {
    const data = useCurrentRegistrationInstance();

    const history = useHistory();
    const onHeaderBack = useCallback(
        () => {
            history.push(REGISTRATION_LIST);
        },
        [history]
    );

    return (
        <Layout.Header
            backIcon
            onBack={onHeaderBack}
            title={
                data?.name
                    ? (
                        <TitleArea
                            title={`${data.name}（${data.id}）`}
                            status={data.status}
                        />
                    )
                    : <Spin size="small" />
            }
            extra={
                <ProductDocLink />
            }
        />
    );
}
