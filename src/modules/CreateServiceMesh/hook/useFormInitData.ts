import {useMemo, useState, useEffect} from 'react';
import {Form} from '@osui/ui';
import {useRequestCallback} from 'huse';
import {useFramework} from '@/hooks';
import {getClusterListApi} from '@/api/createServiceMesh';
import {useCCEClusterRole} from '@/hooks/useCCEClusterRole';
import {ERole} from '@/dicts/authorize';
import {serviceMeshTypeData, blbTypeData} from '../constants';
import {traceInfoDft} from './constant';

const apis = () => Promise.all([
    getClusterListApi(),
]);
export const useFormInitData = () => {
    const {region: {datasource, currentRegion: region}} = useFramework();
    const [formDataInstante] = Form.useForm(); // 获取 表单的实例
    // 1. 首先获取到 网格的类型
    const serviceMeshType = serviceMeshTypeData[0].value;
    // 2. 再然后根据网格类型 确定 当前的地域数据
    const [regionData] = useState(datasource);
    // 3. 请求 获取 istio 和 cluster 的列表数据
    const [request, {data, pending: apisLoading}] = useRequestCallback(apis, undefined);
    // 4. 获取 有权限的集群数据
    const {
        data: clusterRbac, pending: rbacClusterLoading, getClusterRbac,
    } = useCCEClusterRole(ERole.CCE_ADMIN); // 获取有权限的集群数据
    // 所有请求是否加载完毕
    const loading = useMemo(
        () => {
            return apisLoading || rbacClusterLoading;
        },
        [apisLoading, rbacClusterLoading]
    );
    // 6. 表单的初始化数据
    const formDataInitial = useMemo(
        () => {
            return {
                type: serviceMeshType,
                region,
                serviceMeshInstanceName: '',
                istioVersion: undefined,
                installationClusterId: undefined,
                blbType: blbTypeData[0].value,
                controlPlaneBlb: blbTypeData[0].value,
                networkType: {
                    vpcNetworkId: undefined,
                    subnetId: undefined,
                    securityGroupId: [''],
                },
                elasticPublicNetwork: {
                    // TODO 后端ready后，enabled 置为 true
                    enabled: false,
                    type: 'BUY',
                },
                discoverySelector: {
                    enabled: false,
                    matchLabels: {'': ''},
                },
                monitor: {
                    enabled: false,
                },
                multiProtocol: false,
                configCluster: 'EXTERNAL',
                apiServerEip: false,
                traceInfo: traceInfoDft,
            };
        },
        [region, serviceMeshType]
    );
    // 在导航地域更改的时候，重新请求
    useEffect(
        () => {
            formDataInstante.setFieldsValue({
                // 表单中的地域随之进行切换
                region,
                istioVersion: undefined,
                installationClusterId: undefined,
            });
            request();
            getClusterRbac();
        },
        [request, getClusterRbac, region, formDataInstante]
    );

    const clusterData = useMemo(
        () => {
            return (data?.[0] ?? []).map(
                // 仅保留前2位的大版本号
                v => ({...v, clusterVersion: v.clusterVersion.split('.').slice(0, 2).join('.')})
            );
        },
        [data]
    );

    return {
        // 数据
        serviceMeshTypeData, // 网格类型
        regionData, // 地域
        clusterData, // 集群列表
        clusterRbac, // 有权限的集群数据
        // loading
        loading,
        // form
        formDataInitial, // form 初始化数据
        formDataInstante, // form 实例类型
    };
};
