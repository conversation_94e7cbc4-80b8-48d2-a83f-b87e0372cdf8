export type TServiceMeshType = 'standalone' | 'hosting';

export type TElasticPublicNetworkType = 'BUY' | 'BIND';
type TypeLabel = string;
type TypeValue = string;

export interface IServiceMeshTypeData { // 网格类型
    label: TypeLabel;
    value: TServiceMeshType;
}
export interface IIstioVersionData {
    label: TypeLabel;
    value: TypeValue;
}
export interface IBlbTypeData {
    label: TypeLabel;
    value: TypeValue;
}
