import ExternalLink from '@/components/ExternalLink';
import {CCE_CLUSTER_RBAC_LINK} from '@/links';
import urls from '@/urls';
import {TConfigCluster} from '@/api/createServiceMesh';
import {IServiceMeshTypeData, IBlbTypeData} from './type';

// 托管网格，仅开放 bj、gz 地域。
export const hostingMeshRegionList = ['bj', 'gz'];
// 独立网格，仅开放 bj、bd、gz、su 地域。
export const standaloneMeshRegionList = ['bj', 'bd', 'gz', 'su'];
export const serviceMeshTypeData: IServiceMeshTypeData[] = [ // 网格类型
    {label: '托管网格', value: 'hosting'},
    {label: '独立网格', value: 'standalone'},
];

export const meshNameHelp = <>支持大小写字母、数字、中文以及-_ /.特殊字符，必须以字母开头，长度不超过65个字符</>;
export const istioTip = (
    <>
        1. <ExternalLink
            href={urls.external.istio.prerequisites.fill()}
            value="前置条件"
        />
        <br />
        2. <ExternalLink
            href={urls.external.istio.supportStatusOfIstioReleases.fill()}
            value="Istio与集群版本的配对表"
        />
        <br />
    </>
);

export const clusterHelp = (
    <>
        若没有合适的集群，可以前往 <ExternalLink href={urls.external.cceClusterList.fill()} value="创建CCE集群" /> 。<br />
        安装CSM需要CCE集群有2个可用资源大于2核4G的Worker节点；<br />
        仅能选择有管理员权限的CCE，如果您没有CCE管理员权限，请前往 <ExternalLink href={CCE_CLUSTER_RBAC_LINK} value="权限管理" /> 申请。
    </>
);
export const elasticPublicNetworkTip = '公网地址将绑定至控制面BLB，以便远程通过Kubectl提交lstio资源。';
export const clusterTip = (
    <>
        {istioTip}
        3. 仅可选择当前用户有管理员权限的CCE集群，不能选择正在被其它CSM实例管理的CCE集群，如CCE集群中安装过服务网格，会被覆盖安装
    </>
);

export const blbTypeData: IBlbTypeData[] = [
    {label: '普通型BLB', value: '普通型BLB'},
];

export const configClusterData: IBlbTypeData[] = [
    {label: '控制面托管集群', value: 'EXTERNAL'},
    {label: '数据面集群', value: 'REMOTE'},
];

export const configClusterHelpMap = new Map<TConfigCluster, string>([
    ['EXTERNAL', 'Istio资源将提交至托管网格所在集群中'],
    ['REMOTE', 'Istio资源将提交至首个被纳管的数据面集群中'],
]);

export const blbHelp = '用于提供服务网格部署集群外的访问入口，便于跨集群服务治理和控制面管理。';

export const discoverySelectorTip = '开启后，CSM仅会监视和处理CCE集群匹配标签规则的命名空间中的服务和工作负载，提升服务发现和配置下发的效率。';
export const namespaceLabelTip = (
    <>
        有效的标签键有两个部分：可选的前缀和名称，由斜杠 ( / ) 分隔。<br />
        名称段是必需的，并且必须为 63 个字符或更少，以字母数字字符 ( [a-z0-9A-Z] ) 开头和结尾，中间有破折号 ( - )、下划线 ( _ )、点 ( . ) 和字母数字。<br />
        前缀是可选的。如果指定，前缀必须是 DNS 子域：由点 ( . ) 分隔的一系列 DNS 标签，总共不超过 253 个字符，后跟一个斜线 ( / )。<br /><br />
        有效标签值：<br />
        1.不得超过 63 个字符（可以为空），<br />
        2.[a-z0-9A-Z]除非为空，否则必须以字母数字字符 ( )开头和结尾，<br />
        3.可以包含破折号 ( -)、下划线 ( _)、点 ( .) 和字母数字。
    </>
);
export const monitorTip = (
    <>
        开启后，CProm会通过CSM采集微服务间请求数据指标。
    </>
);
export const monitorHelp = (
    <>
        请确保主集群已使用所选CProm实例采集监控指标，可前往 <ExternalLink
            href={urls.external.cpromInstanceList.fill()}
            value="CProm控制台"
        /> 。<br />
        多集群监控指标采集配置，参考  <ExternalLink href={urls.external.csmObservability.fill()} value="可观测配置" /> 。
    </>
);
