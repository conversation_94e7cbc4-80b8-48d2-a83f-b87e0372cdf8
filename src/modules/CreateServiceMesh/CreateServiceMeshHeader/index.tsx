import {useCallback} from 'react';
import {useHistory} from 'react-router-dom';
import {Header} from '@/components/Layout';
import urls from '@/urls';

export default function CreateServiceMeshHeader() {
    const history = useHistory();
    const toBack = useCallback(
        () => {
            history.push(urls.serviceMeshList.fill());
        },
        [history]
    );

    return (
        <Header
            backIcon
            onBack={toBack}
            title="创建网格"
        />
    );
}
