import {Rule} from 'antd/lib/form';

export const istioVersionValidate: Rule[] = [
    {required: true, message: '请选择Istio版本'},
];

export const securityGroupIdValidate: Rule[] = [
    {required: true, message: '请选择安全组'},
];

export const vpcIdValidate: Rule[] = [
    {required: true, message: '请选择VPC网络'},
];

export const subNetIdValidate: Rule[] = [
    {required: true, message: '请选择子网'},
];

export const meshNameValidate: Rule[] = [
    {required: true, message: ''},
    {pattern: /^[a-zA-Z][\w-/.\u4e00-\u9fa5]{0,64}$/, message: ''},
];

export const istioValidate: Rule[] = [
    {required: true, message: ''},
];

const checkLabelValue = (labelValue = '') => {
    if (!labelValue) {
        return '请输入标签值';
    }

    const len = labelValue.length;
    const firstChar = labelValue[0];
    const lastChar = labelValue[len - 1];

    if (len > 63) {
        return '最大长度为 63 个字符';
    }
    if (!/^[a-zA-Z0-9-_.]{0,63}$/.test(labelValue)) {
        return '只能包含字母、数字、- 、_ 和 . ';
    }
    if (!/^[a-zA-Z0-9]$/.test(firstChar) || !/^[a-zA-Z0-9]$/.test(lastChar)) {
        return '首尾只能是字母或数字';
    }
};

// 有效的标签键有两个部分：可选的前缀和名称，由斜杠 ( /) 分隔。
// 名称段是必需的，并且必须为 63 个字符或更少，以字母数字字符 ( [a-z0-9A-Z]) 开头和结尾，中间有破折号 ( -)、下划线 ( _)、点 ( .) 和字母数字。
// 前缀是可选的。如果指定，前缀必须是 DNS 子域：由点 ( .) 分隔的一系列 DNS 标签，总共不超过 253 个字符，后跟一个斜线 ( /)。
// eslint-disable-next-line complexity
const checkLabelKey = (labelKey = '') => {
    if (!labelKey) {
        return '请输入标签名称';
    }

    const firstSlashCharIndex = labelKey.indexOf('/');
    const lastSlashCharIndex = labelKey.lastIndexOf('/');

    if (firstSlashCharIndex !== lastSlashCharIndex) {
        return '至多含1个 / 符号';
    }
    if (firstSlashCharIndex > -1) {
        const [dns = '', keyName = ''] = labelKey.split('/');
        const dnsReg = /^(?=^.{3,253}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;

        if (dns.length > 253) {
            return '标签前缀的长度不可超过 253 个字符';
        }
        if (!dnsReg.test(dns)) {
            return '标签前缀的格式不正确，请检查';
        }

        const len = keyName.length;
        const firstChar = keyName[0];
        const lastChar = keyName[len - 1];
        if (len < 1 || len > 63) {
            return '标签名称的长度范围为 1-63 个字符';
        }
        if (!/^[a-zA-Z0-9-_.]{1,63}$/.test(keyName)) {
            return '标签名称只能包含字母、数字、- 、_ 和 . ';
        }
        if (!/^[a-zA-Z0-9]$/.test(firstChar) || !/^[a-zA-Z0-9]$/.test(lastChar)) {
            return '标签名称的首尾只能是字母或数字';
        }
    }
};

export const labelsKeyValidate: Rule[] = [
    {required: true, message: ''},
    {
        validator: (rule, value) => {
            const msg = checkLabelKey(value);
            return msg ? Promise.reject(msg) : Promise.resolve();
        },
    },
];

export const labelsValueValidate: Rule[] = [
    {required: true, message: ''},
    {
        validator: (rule, value) => {
            const msg = checkLabelValue(value);
            return msg ? Promise.reject(msg) : Promise.resolve();
        },
    },
];

export const instancesValidate: Rule[] = [
    {required: true, message: ''},
];
