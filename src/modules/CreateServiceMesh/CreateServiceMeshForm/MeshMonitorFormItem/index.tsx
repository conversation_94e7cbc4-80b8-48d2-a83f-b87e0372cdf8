import {Form, FormInstance, Popover, Select, Switch, Tooltip} from '@osui/ui';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {useCallback, useEffect, useMemo} from 'react';
import {useRequestCallback} from 'huse';
import {monitorTip} from '@/modules/CreateServiceMesh/constants';
import {isStandaloneMesh} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/util';
import ExternalLink from '@/components/ExternalLink';
import {instanceRules} from '@/modules/Cprom/OpenCpromButton/constant';
import {getMonitorInstanceListApi} from '@/api/createServiceMesh';
import urls from '@/urls';

interface IProps {
    formDataInstante: FormInstance;
}
export default function MeshMonitorFormItem({
    formDataInstante,
}: IProps) {
    const clusterObjStr = formDataInstante.getFieldValue('installationClusterId') || '{}';
    const enabled = formDataInstante.getFieldValue(['monitor', 'enabled']) || false;

    const clusterId = useMemo(
        () => {
            const {clusterId = ''} = JSON.parse(clusterObjStr) || {};
            return clusterId;
        },
        [clusterObjStr]
    );

    const [
        getMonitorInstanceList,
        {pending: getMonitorInstanceListLoading, data: getMonitorInstanceListData},
    ] = useRequestCallback(getMonitorInstanceListApi, {clusterId});

    const monitorInstanceOptions = useMemo(
        () => {
            return (getMonitorInstanceListData?.instances || []).map(v => {
                return {
                    label: (
                        <Tooltip
                            placement="right"
                            trigger={v.isInstallCPromAgent ? '' : 'hover'}
                            title={
                                <>
                                    该 CProm 关联的 CCE 集群没有安装 Agent。
                                    <ExternalLink
                                        href={urls.external.cprom.cpromInstanceCluster.fill({}, {id: v.id})}
                                        value="去安装"
                                    />
                                </>
                            }
                        >
                            {v.name}/{v.id}
                        </Tooltip>
                    ),
                    value: JSON.stringify(v),
                    disabled: !v.isInstallCPromAgent,
                };
            });
        },
        [getMonitorInstanceListData]
    );

    const getMonitorInstanceListCallback = useCallback(
        () => {
            formDataInstante.setFieldsValue({
                monitor: {
                    instances: [],
                },
            });

            getMonitorInstanceList();
        },
        [formDataInstante, getMonitorInstanceList]
    );

    const changeMonitorEnabled = useCallback(
        (checked: boolean) => {
            if (checked && clusterId) {
                getMonitorInstanceListCallback();
            }
        },
        [clusterId, getMonitorInstanceListCallback]
    );

    useEffect(
        () => {
            if (!clusterId) {
                formDataInstante.setFieldsValue({
                    monitor: {
                        instances: [],
                    },
                });
                return;
            }

            if (enabled) {
                getMonitorInstanceListCallback();
            }
        },
        [clusterId, enabled, formDataInstante, getMonitorInstanceListCallback]
    );

    return (
        <Form.Item
            shouldUpdate
            noStyle
        >
            {
                ({getFieldValue}) => isStandaloneMesh(getFieldValue('type'))
                    && (
                        <>
                            <Form.Item
                                label={
                                    <>
                                        监控指标采集：{
                                            <Popover content={monitorTip}>
                                                <QuestionCircleOutlined />
                                            </Popover>
                                        }
                                    </>
                                }
                                colon={false}
                            >

                                <Form.Item
                                    name={['monitor', 'enabled']}
                                    valuePropName="checked"
                                >
                                    <Switch onChange={changeMonitorEnabled} />
                                </Form.Item>
                            </Form.Item>

                            <Form.Item
                                shouldUpdate
                                noStyle
                            >
                                {({getFieldValue}) => getFieldValue(['monitor', 'enabled'])
                                    && (
                                        <Form.Item
                                            label="选择实例"
                                            required
                                            rules={instanceRules}
                                            name={['monitor', 'instances']}
                                            help={
                                                <>
                                                    {/* eslint-disable-next-line max-len */}
                                                    没有合适的监控实例，可以<ExternalLink href={urls.external.cpromInstanceList.fill()} value="去创建实例" />
                                                </>
                                            }
                                        >
                                            <Select
                                                style={{width: '320px'}}
                                                showSearch
                                                placeholder={clusterId ? '请选择' : '请先选择集群'}
                                                loading={getMonitorInstanceListLoading}
                                                options={monitorInstanceOptions}
                                            />
                                        </Form.Item>
                                    )
                                }
                            </Form.Item>
                        </>
                    )
            }
        </Form.Item>
    );
}
