import {useCallback} from 'react';
import {useHistory} from 'react-router-dom';
import {useBoolean} from 'huse';
import styled from 'styled-components';
import {Button, FormInstance, Space} from '@osui/ui';
import {createServiceMeshApi, IClusterData} from '@/api/createServiceMesh';
import PageFooter from '@/components/PageFooter';
import urls from '@/urls';
import {TElasticPublicNetworkType} from '../type';
import {getSubmittedBls, getSubmittedDiscoverySelector, getSubmittedMonitor, isStandaloneMesh} from './util';

const PriceBox = styled(Space)`
    margin-left: 10px;
`;

const PageFooterTopBox = styled.div`
    height: 60px;
`;

interface IProps {
    elasticPublicNetworkType: TElasticPublicNetworkType;
    formDataInstante: FormInstance;
    clusterData: IClusterData[];
    loading: boolean;
}

export default function FormPageFooter(
    {
        // elasticPublicNetworkType,
        formDataInstante,
        clusterData,
        loading: propLoading,
    }: IProps) {
    const history = useHistory();
    const [loading, {on, off}] = useBoolean(false);
    const onCancel = useCallback( // 返回网格列表页
        () => {
            history.push(urls.serviceMeshList.fill());
        },
        [history]
    );
    const onSubmit = useCallback( // 表单提交事件
        async () => {
            try {
                // 验证表单 并拿到表单中的值
                const {
                    type,
                    serviceMeshInstanceName,
                    istioVersion,
                    networkType,
                    securityGroupId,
                    // TODO elasticPublicNetwork 表单项隐藏，传给后端时值设为 { enabled: false, }
                    elasticPublicNetwork = {
                        enabled: false,
                    },
                    installationClusterId,
                    discoverySelector,
                    bls,
                    monitor,
                    multiProtocol,
                    configCluster,
                    apiServerEip,
                    traceInfo,
                } = await formDataInstante.validateFields(); // 验证表单 并拿到表单中的值
                const {
                    clusterId = '',
                } = JSON.parse(installationClusterId || '{}') as IClusterData;
                const tempCluster = clusterData.find(v => v.clusterId === clusterId); // 拿到选中的集群信息
                // standalone需要选主集群
                if (isStandaloneMesh(type) && !tempCluster) {
                    return;
                }

                on();
                // 格式转换
                const submittedMonitor = getSubmittedMonitor(monitor);
                const submittedBls = getSubmittedBls(bls);
                const submittedDiscoverySelector = getSubmittedDiscoverySelector(discoverySelector);
                const res = await createServiceMeshApi({ // 请求创建
                    type,
                    serviceMeshInstanceName,
                    istioVersion,
                    networkType,
                    securityGroupId,
                    elasticPublicNetwork,
                    installationClusterId: clusterId,
                    installationClusterName: tempCluster?.clusterName ?? '',
                    discoverySelector: submittedDiscoverySelector,
                    bls: submittedBls,
                    monitor: submittedMonitor,
                    multiProtocol,
                    configCluster,
                    apiServerEip,
                    traceInfo,
                });
                if (res) {
                    off();
                    onCancel(); // 返回网格列表页
                }
            } catch (err) {
                off();
            }
        },
        [formDataInstante, off, on, onCancel, clusterData]
    );

    return (
        <>
            <PageFooterTopBox />
            <PageFooter onConfirm={onSubmit} onCancel={onCancel} confirmButton={{disabled: loading || propLoading}}>
                {/* TODO 暂时贴定价的链接 */}
                <PriceBox>
                    <Button type="link" href={urls.external.price.blb.fill()} target="_blank">查看BLB计费规则&gt;</Button>
                </PriceBox>
                {/* TODO 支持苏州地域，无需展示EIP的定价 */}
                {/* <PriceBox>
                    <Button type="link" href={urls.external.price.eip.fill()} target="_blank">查看计费EIP规则&gt;</Button>
                </PriceBox> */}
                {/* <CostItemList elasticPublicNetworkType={elasticPublicNetworkType} /> */}
            </PageFooter>
        </>
    );
}
