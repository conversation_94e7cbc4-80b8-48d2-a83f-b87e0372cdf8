import {FormInstance, Select} from '@osui/ui';
import {useEffect, useMemo} from 'react';
import styled from 'styled-components';
import {getIstioListApi} from '@/api/createServiceMesh';
import {useSafeRequestAndCallBack} from '@/hooks';
import {TServiceMeshType} from '../../type';
import {isStandaloneMesh} from '../util';

const SelectStyled = styled(Select)`
    width: 180px !important;
`;

interface IProps {
    formDataInstante: FormInstance;
    type: TServiceMeshType;
    datasource: string[];
}

export default function IstioVersion({
    formDataInstante,
    type,
    datasource,
    ...props
}: IProps) {
    useEffect(
        () => {
            formDataInstante.setFieldsValue({
                istioVersion: undefined,
            });
        },
        // formDataInstante 是不会变的
        [formDataInstante, type]
    );

    const {data: {pending, data}} = useSafeRequestAndCallBack(getIstioListApi, {type}, !!type);
    const options = useMemo(
        () => {
            let datasourceRes = data || [];
            if (isStandaloneMesh(type)) {
                datasourceRes = datasource;
            }

            return datasourceRes?.sort().reverse().map(v => ({label: v, value: v}));
        },
        [data, datasource, type]
    );

    return (
        <SelectStyled
            {...props}
            allowClear={isStandaloneMesh(type)}
            loading={pending}
            disabled={pending}
            options={options}
            placeholder="请选择Istio版本"
        />
    );
}
