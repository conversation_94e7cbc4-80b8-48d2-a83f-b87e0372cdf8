import {Select} from '@osui/ui';
import {useMemo} from 'react';
import styled from 'styled-components';
import {getSubnetListApi} from '@/api/createServiceMesh';
import {useSafeRequestAndCallBack} from '@/hooks';

const SubnetSelectStyled = styled(Select)`
    width: 240px !important;
`;

interface IProps {
    vpcId: string;
}

export default function SubnetSelect({vpcId, ...props}: IProps) {
    const {data: {pending, data}} = useSafeRequestAndCallBack(getSubnetListApi, {vpcId}, !!vpcId);
    const subnetOptions = useMemo(
        () => {
            return data?.map(({subnetId, name, cidr}) => (
                {
                    label: `${name}（${cidr}）`,
                    value: subnetId,
                }
            )) ?? [];
        },
        [data]
    );

    return (
        <SubnetSelectStyled
            {...props}
            disabled={pending}
            loading={pending}
            placeholder={vpcId ? '请选择子网' : '请先选择VPC网络'}
            options={subnetOptions}
        />
    );
}
