import {Form, Select} from '@osui/ui';
import {useEffect, useMemo} from 'react';
import {useRequestCallback} from 'huse';
import styled from 'styled-components';
import {getVpcNetworkListApi} from '@/api/createServiceMesh';
import {useFramework} from '@/hooks';
import {subNetIdValidate, vpcIdValidate} from '../formValidate';
import {hostingMeshRegionList} from '../../constants';
import SubnetSelect from './SubnetSelect';

const VpcSelectStyled = styled(Select)`
    width: 240px !important;
`;

interface IProps {
    isUserChangeRegion: boolean;
    offUserChangeRegion: () => void;
    className?: string;
}

export default function NetworkType({isUserChangeRegion, offUserChangeRegion, className}: IProps) {
    const {region: {currentRegion}} = useFramework();
    const [request, {pending: vpcLoading, data: vpcList}] = useRequestCallback(
        getVpcNetworkListApi,
        {region: currentRegion}
    );

    useEffect(
        () => {
            if (isUserChangeRegion) {
                if (hostingMeshRegionList.includes(currentRegion)) {
                    request();
                }
                offUserChangeRegion();
            }
        },
        [currentRegion, isUserChangeRegion, offUserChangeRegion, request]
    );
    const vpcOptions = useMemo(
        () => {
            return vpcList?.map(({vpcId, name, cidr}) => (
                {
                    label: `${name}（${cidr}）`,
                    value: vpcId,
                }
            )) ?? [];
        },
        [vpcList]
    );

    return (
        <div className={className}>
            <Form.Item
                shouldUpdate
                noStyle
            >
                {({setFieldsValue}) => {
                    const changeVpcId = vpcId => {
                        setFieldsValue(
                            {
                                networkType: {vpcNetworkId: vpcId, subnetId: undefined},
                                securityGroupId: undefined,
                            }
                        );
                    };
                    return (
                        <Form.Item label={null} name={['networkType', 'vpcNetworkId']} rules={vpcIdValidate}>
                            <VpcSelectStyled
                                disabled={vpcLoading}
                                loading={vpcLoading}
                                placeholder="请选择VPC网络"
                                options={vpcOptions}
                                onChange={changeVpcId}
                            />
                        </Form.Item>
                    );
                }}
            </Form.Item>

            <Form.Item
                shouldUpdate
                noStyle
            >
                {({getFieldValue}) => {
                    const vpcNetworkId = getFieldValue(['networkType', 'vpcNetworkId']);
                    return (
                        <Form.Item
                            label={null}
                            name={['networkType', 'subnetId']}
                            rules={subNetIdValidate}
                        >
                            <SubnetSelect vpcId={vpcNetworkId} />
                        </Form.Item>
                    );
                }}
            </Form.Item>
        </div>
    );
}
