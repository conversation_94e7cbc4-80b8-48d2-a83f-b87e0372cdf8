import {Rule} from 'antd/lib/form';
import {BaseOptionType} from 'antd/lib/select';

export const traceTip = '链路追踪开启后，可将网格数据面内产生的tracing数据上报到第三方Jaeger/Zipkin服务，帮助您理解服务间的依赖关系、分析链路情况或快速排障。';
export const samplingRateTip = '网格采集并持久化tracing的采样比例，范围0-100。sidecar采集、上报数据消耗的资源与带宽和采样率成正比，请按需配置。';
export const serviceOptions: BaseOptionType[] = [
    {
        label: '第三方 Jaeger/Zipkin 服务',
        value: 'J<PERSON>ger/Zipkin',
    },
];

export const serviceMap = new Map([
    ['Jaeger/Zipkin', '第三方 Jaeger/Zipkin 服务'],
]);

export const samplingRateRules: Rule[] = [
    {required: true, message: '采样率不可为空'},
];


export const serviceRules: Rule[] = [
    {required: true, message: '使用服务不可为空'},
];

export const addressRules: Rule[] = [
    {required: true, message: '服务地址不可为空'},
    // TODO 补充校验
];
