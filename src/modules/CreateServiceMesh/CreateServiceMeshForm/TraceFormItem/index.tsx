import {Form, FormInstance, Input, InputNumber, Popover, Radio, Switch} from '@osui/ui';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {useCallback} from 'react';
import {addressRules, samplingRateRules, samplingRateTip, serviceOptions, serviceRules, traceTip} from './constant';
import c from './index.less';

type TType = 'create' | 'update';
interface IProps {
    formDataInstante: FormInstance;
    type: TType;
}
export default function TraceFormItem({
    formDataInstante,
    type = 'create',
}: IProps) {
    const changeSamplingRate = useCallback(
        (value: any) => {
            formDataInstante.setFieldsValue({
                traceInfo: {
                    samplingRate: value,
                },
            });
        },
        [formDataInstante]
    );

    return (
        <Form.Item
            className={c['trace-form-item']}
            label={
                type === 'create' && (
                    <>
                        链路追踪：
                        <Popover content={traceTip}>
                            <QuestionCircleOutlined />
                        </Popover>
                    </>
                )
            }
            colon={false}
        >
            {
                type === 'create' && (
                    <Form.Item
                        name={['traceInfo', 'traceEnabled']}
                        valuePropName="checked"
                    >
                        <Switch />
                    </Form.Item>
                )
            }

            <Form.Item
                shouldUpdate
                noStyle
            >
                {({getFieldValue}) => getFieldValue(['traceInfo', 'traceEnabled'])
                    && (
                        <>
                            <Form.Item
                                label={
                                    <>
                                        采样率：
                                        <Popover content={samplingRateTip}>
                                            <QuestionCircleOutlined />
                                        </Popover>
                                    </>
                                }
                                required
                                name={['traceInfo', 'samplingRate']}
                                rules={samplingRateRules}
                            >
                                <InputNumber
                                    min={0}
                                    max={100}
                                    step={1}
                                    precision={2}
                                    value={getFieldValue(['traceInfo', 'samplingRate'])}
                                    onChange={changeSamplingRate}
                                /> %
                            </Form.Item>

                            <Form.Item
                                label="使用服务"
                                required
                                name={['traceInfo', 'service']}
                                rules={serviceRules}
                            >
                                <Radio.Group options={serviceOptions} />
                            </Form.Item>

                            <Form.Item
                                className={c['address']}
                                label="服务地址"
                                required
                                name={['traceInfo', 'address']}
                                rules={addressRules}
                            >
                                <Input placeholder="请输入第三方服务地址，例如{host}:{port}" />
                            </Form.Item>
                        </>
                    )}
            </Form.Item>
        </Form.Item>
    );
}
