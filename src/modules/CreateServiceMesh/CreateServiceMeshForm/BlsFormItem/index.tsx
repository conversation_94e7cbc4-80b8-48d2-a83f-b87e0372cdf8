import {Form, FormInstance, Modal, Select, Spin, Switch, message} from '@osui/ui';
import {useBoolean, useRequestCallback} from 'huse';
import {useCallback, useMemo} from 'react';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {getBlsAgentCheckByClusterIdApi, getBlsLogStoreListApi} from '@/api/blsLog';
import {isStandaloneMesh} from '../util';
import {nameRules} from './constant';

interface IProps {
    formDataInstante: FormInstance;
}
export default function BlsFormItem({
    formDataInstante,
}: IProps) {
    const clusterStrObj = formDataInstante.getFieldValue('installationClusterId') || '{}';

    const clusterId: string = useMemo(
        () => {
            const {clusterId = ''} = JSON.parse(clusterStrObj);
            return clusterId;
        },
        [clusterStrObj]
    );

    const [
        getBlsLogStoreList,
        {pending: getBlsLogStoreListLoading, data: getBlsLogStoreListData},
    ] = useRequestCallback(getBlsLogStoreListApi, undefined);

    const [loading, {on: onLoading, off: offLoading}] = useBoolean(false);

    const getBlsLogStoreListOptions = useMemo(
        () => {
            return (getBlsLogStoreListData?.result || []).map(v => {
                return {
                    label: `${v.name}（${v.retention}天）`,
                    value: JSON.stringify(v),
                };
            });
        },
        [getBlsLogStoreListData?.result]
    );

    const changeEnabled = useCallback(
        async (enabled: boolean) => {
            if (enabled) {
                formDataInstante.setFieldsValue({
                    bls: {
                        enabled: false,
                        instances: undefined,
                    },
                });
                if (!clusterId) {
                    message.warning('请先选择主集群');
                    return;
                }

                try {
                    onLoading();
                    const rsp = await getBlsAgentCheckByClusterIdApi({clusterId});
                    const {isExist} = rsp || {};
                    if (isExist) {
                        formDataInstante.setFieldsValue({
                            bls: {
                                enabled: true,
                                instances: undefined,
                            },
                        });
                        getBlsLogStoreList();
                    }
                    else {
                        Modal.confirm({
                            title: '还未安装日志组件',
                            content: <>开启日志服务前，需要安装日志组件（CCE Log Operator），请先安装。</>,
                            closable: true,
                            cancelText: '取消',
                            okText: (
                                <ExternalLink
                                    value="去安装"
                                    href={urls.external.cce.installComponent.fill({}, {clusterUuid: clusterId})}
                                />
                            ),
                        });
                    }
                } catch (error) {
                    console.error(error);
                } finally {
                    offLoading();
                }
            }
        },
        [clusterId, formDataInstante, getBlsLogStoreList, offLoading, onLoading]
    );

    return (
        <>
            <Form.Item
                shouldUpdate
                noStyle
            >
                {({getFieldValue}) => {
                    const type = getFieldValue('type');
                    return isStandaloneMesh(type) && (
                        <>
                            <Form.Item
                                label="日志服务"
                            >
                                {
                                    loading
                                        ? <Spin />
                                        : (
                                            <Form.Item
                                                name={['bls', 'enabled']}
                                                valuePropName="checked"
                                            >
                                                <Switch onChange={changeEnabled} />
                                            </Form.Item>
                                        )
                                }
                            </Form.Item>
                            <Form.Item
                                shouldUpdate
                                noStyle
                            >
                                {({getFieldValue}) => getFieldValue(['bls', 'enabled'])
                                    && (
                                        <Form.Item
                                            label="选择日志集"
                                            required
                                            name={['bls', 'instances']}
                                            rules={nameRules}
                                            help={
                                                <>
                                                    <div>建议使用存储时长≥30天的日志集</div>
                                                    {/* eslint-disable-next-line max-len */}
                                                    没有合适的日志集，可以<ExternalLink href={urls.external.bls.logList.fill()} value="去创建日志集" />
                                                </>
                                            }
                                        >
                                            <Select
                                                showSearch
                                                style={{width: '160px'}}
                                                placeholder="请选择日志集"
                                                loading={getBlsLogStoreListLoading}
                                                options={getBlsLogStoreListOptions}
                                                required
                                            />
                                        </Form.Item>
                                    )
                                }
                            </Form.Item>
                        </>
                    );
                }}
            </Form.Item>
        </>
    );
}
