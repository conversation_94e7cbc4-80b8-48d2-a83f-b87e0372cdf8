import React from 'react';
import styled from 'styled-components';
import {GRAY_11, RED} from '@/styles';
import {FlexItemsCenterStyled} from '@/components/CommonStyled';
import {TElasticPublicNetworkType} from '../../type';

const CostItemStyled = styled.div`
    margin: 0 40px;
`;
const ContentStyled = styled.span`
    color: ${RED};
    font-size: 20px;
    font-weight: 500;
`;
const TagStyled = styled.span`
    color: ${GRAY_11};
    padding: 2px 8px;
    border-radius: 4px;
    margin-left: 8px;
    background-color: ${RED};
`;

interface ICostItemProps {
    label: string;
    content: React.ReactNode;
    tag?: string;
}
function CostItem({label, content, tag}: ICostItemProps) {
    return (
        <CostItemStyled>
            <div>{label}：</div>
            <FlexItemsCenterStyled>
                <ContentStyled>{content}</ContentStyled>
                {
                    tag && <TagStyled>{tag}</TagStyled>
                }
            </FlexItemsCenterStyled>
        </CostItemStyled>
    );
}

interface IProps {
    elasticPublicNetworkType: TElasticPublicNetworkType;
}
export default function CostItemList({elasticPublicNetworkType}: IProps) {
    return (
        <FlexItemsCenterStyled>
            <CostItem
                label="实例配置费用"
                content="免费"
                tag="公测期"
            />
            <CostItem label="BLB" content="¥0.00333/分钟" />
            {
                elasticPublicNetworkType && (
                    <CostItem label="弹性公网IP" content="¥0.00333/分钟" />
                )
            }
        </FlexItemsCenterStyled>
    );
}
