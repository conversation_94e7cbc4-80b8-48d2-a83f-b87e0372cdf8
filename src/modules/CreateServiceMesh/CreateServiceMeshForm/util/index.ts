import {IBlsItem, IDiscoverySelector} from '@/api/createServiceMesh';
import {TServiceMeshType} from '../../type';

export const isHostingMesh = (type: TServiceMeshType) => {
    return type === 'hosting';
};
export const isStandaloneMesh = (type: TServiceMeshType) => {
    return type === 'standalone';
};
export const getSubmittedBls = (bls: IBlsItem) => {
    const {instances = '{}'} = bls || {};
    const {region = '', name = ''} = JSON.parse(instances as string);
    return {...bls, instances: {region, name}};
};
export const getSubmittedMonitor = (monitor: {enabled: boolean, instances: string[] | string}) => {
    if (!monitor) {
        return {};
    }

    if (Array.isArray(monitor.instances)) {
        const [region = '', idObjStr = '{}'] = monitor?.instances ?? [];
        const instances = [{region, id: JSON.parse(idObjStr).id}];
        return Object.assign({}, monitor, {instances});
    }

    return {...monitor, instances: [JSON.parse(monitor?.instances || '{}')]};
};

export const getSubmittedDiscoverySelector = (discoverySelector: IDiscoverySelector) => {
    let matchLabels = discoverySelector?.matchLabels ?? [];
    matchLabels = matchLabels.reduce((acc = {}, cur = []) => {
        acc[cur[0]] = cur[1];
        return acc;
    }, {}) ?? {};
    return Object.assign({}, discoverySelector, {matchLabels});
};
