import {Select} from '@osui/ui';
import {useMemo} from 'react';
import styled from 'styled-components';
import {getSecurityGroupListApi} from '@/api/createServiceMesh';
import {useSafeRequestAndCallBack} from '@/hooks';

const SelectStyled = styled(Select)`
    width: 180px !important;
`;

interface IProps {
    vpcId: string;
}

export default function SecurityGroupSelect({vpcId, ...props}: IProps) {
    const {data: {pending, data}} = useSafeRequestAndCallBack(getSecurityGroupListApi, {vpcId}, !!vpcId);
    const options = useMemo(
        () => {
            return data?.map(({id, name}) => ({
                label: name,
                value: id,
            })) ?? [];
        },
        [data]
    );

    return (
        <SelectStyled
            {...props}
            disabled={pending}
            loading={pending}
            options={options}
            placeholder="请选择安全组"
        />
    );
}
