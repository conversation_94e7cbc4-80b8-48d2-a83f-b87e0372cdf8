import {Form, Input, Popover, Space, Switch} from '@osui/ui';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {discoverySelectorTip, namespaceLabelTip} from '../../constants';
import {labelsKeyValidate, labelsValueValidate} from '../formValidate';

export default function DiscoverySelectorFormItem() {
    return (
        <>
            <Form.Item
                label={
                    <>
                        服务发现范围配置：{
                            <Popover content={discoverySelectorTip}>
                                <QuestionCircleOutlined />
                            </Popover>
                        }
                    </>
                }
                colon={false}
            >
                <Form.Item
                    name={['discoverySelector', 'enabled']}
                    valuePropName="checked"
                >
                    <Switch />
                </Form.Item>
            </Form.Item>
            <Form.Item
                shouldUpdate
                noStyle
            >
                {({getFieldValue}) => getFieldValue(['discoverySelector', 'enabled'])
                    && (
                        <Form.Item
                            required
                            label={
                                <>
                                    命名空间标签：{
                                        <Popover content={namespaceLabelTip}>
                                            <QuestionCircleOutlined />
                                        </Popover>
                                    }
                                </>
                            }
                            colon={false}
                        >
                            <Form.Item
                                shouldUpdate
                                noStyle
                            >
                                {({getFieldValue}) =>
                                    getFieldValue(['discoverySelector', 'matchLabels'])
                                        .map((_item, index) => {
                                            return (
                                                <>
                                                    <Space
                                                        // eslint-disable-next-line react/no-array-index-key
                                                        key={index}
                                                        align="baseline"
                                                    >
                                                        <Form.Item
                                                            rules={labelsKeyValidate}
                                                            name={[
                                                                'discoverySelector',
                                                                'matchLabels',
                                                                index,
                                                                0,
                                                            ]}
                                                        >
                                                            <Input
                                                                width={160}
                                                                maxLength={63}
                                                                showCount
                                                                placeholder="请输入标签名称"
                                                            />
                                                        </Form.Item>
                                                        <div>:</div>
                                                        <Form.Item
                                                            rules={labelsValueValidate}
                                                            name={[
                                                                'discoverySelector',
                                                                'matchLabels',
                                                                index,
                                                                1,
                                                            ]}
                                                        >
                                                            <Input
                                                                width={160}
                                                                maxLength={63}
                                                                showCount
                                                                placeholder="请输入标签值"
                                                            />
                                                        </Form.Item>
                                                    </Space>
                                                </>
                                            );
                                        })
                                }
                            </Form.Item>
                        </Form.Item>
                    )
                }
            </Form.Item>
        </>
    );
}
