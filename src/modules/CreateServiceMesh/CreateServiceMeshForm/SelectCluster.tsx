import {useCallback, useEffect, useState} from 'react';
import {Select, Tooltip} from '@osui/ui';
import {IClusterData} from '@/api/createServiceMesh';
import {IClusterRbac} from '@/hooks/useCCEClusterRole';
import {IS_QASANDBOX} from '@/dicts';
import ExternalLink from '@/components/ExternalLink';
import {CCE_CLUSTER_RBAC_LINK} from '@/links';
import NoDataSelectCluster from './NoDataSelectCluster';

interface IProps {
    value?: string;
    datasource: IClusterData[];
    clusterRbac: IClusterRbac;
    onChange?: (clusterId: string) => any;
}
export default function CreateMeshSelectCluster({value = undefined, onChange, datasource = [], clusterRbac}: IProps) {
    const [selectValue, setSelectValue] = useState<string>();
    const triggerChange = useCallback( // 将clusterId传递出去给表单
        (clusterId: string) => {
            onChange?.(clusterId);
        },
        [onChange]
    );
    const onSelectCluster = useCallback( // 下拉框的change事件
        value => {
            setSelectValue(value);
            triggerChange(value);
        },
        [triggerChange]
    );
    const onClear = useCallback(
        () => onSelectCluster(undefined),
        [onSelectCluster]
    );
    useEffect(
        () => {
            setSelectValue(undefined); // 下拉列表的数据改变的话，需要重新选择数据
        },
        [datasource]
    );
    const disabledReason = useCallback(
        (v: IClusterData) => {
            if (v.istioInstalledStatus) {
                return '该CCE集群已被使用';
            }

            if (!IS_QASANDBOX && !(clusterRbac?.[v.clusterId])) {
                return (
                    <>
                        您没有CCE管理员权限，请前往 <ExternalLink href={CCE_CLUSTER_RBAC_LINK} value="权限管理" /> 申请。
                    </>
                );
            }
        },
        [clusterRbac]
    );
    return (
        <Select
            allowClear
            value={selectValue || value}
            onChange={onSelectCluster}
            onClear={onClear}
            placeholder="请选择"
            className="select-cluster"
            notFoundContent={<NoDataSelectCluster />}
        >
            {
                datasource?.map(v => (
                    <Select.Option
                        key={v.clusterId}
                        value={JSON.stringify(v)}
                        disabled={
                            disabledReason(v)
                        }
                    >
                        <Tooltip placement="right" title={disabledReason(v)}>
                            {v.clusterName}/{v.clusterId}
                        </Tooltip>
                    </Select.Option>
                ))
            }
        </Select>
    );
}
