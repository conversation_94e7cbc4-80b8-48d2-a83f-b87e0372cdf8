import {Form, Switch, Radio, CheckboxOptionType} from '@osui/ui';

const options: CheckboxOptionType[] = [
    {label: '购买弹性公网 IP（公网带宽：100 Mbps)', value: 'BUY'},
    // TODO 后端暂不支持 绑定
    {disabled: true, label: '绑定已有弹性公网 IP', value: 'BIND'},
];

export default function ElasticPublicNetwork() {
    return (
        <>
            <Form.Item
                name={['elasticPublicNetwork', 'enabled']}
                valuePropName="checked"
            >
                {/* TODO 后端ready后，去掉 disabled  */}
                <Switch disabled />
            </Form.Item>

            <Form.Item
                shouldUpdate
                noStyle
            >
                {
                    ({getFieldValue}) => {
                        const enabled = getFieldValue(['elasticPublicNetwork', 'enabled']);
                        return enabled && (
                            <Form.Item
                                name={['elasticPublicNetwork', 'type']}
                            >
                                <Radio.Group optionType="default" options={options} />
                            </Form.Item>
                        );
                    }
                }
            </Form.Item>
        </>
    );
}
