import {useCallback, useEffect, useMemo, useState} from 'react';
import {Form, Input, Popover, Radio, Spin, Checkbox} from '@osui/ui';
import {QuestionCircleOutlined} from '@ant-design/icons';
import _ from 'lodash';
import {useBoolean, useRequest} from 'huse';
import {useFramework} from '@/hooks';
import MultiProtocolFormItem from '@/components/MultiProtocolFormItem';
import {IClusterData, getIstioSupportK8sVersionListApi} from '@/api/createServiceMesh';
import {
    meshNameHelp,
    clusterHelp,
    clusterTip,
    blbTypeData,
    blbHelp,
    hostingMeshRegionList,
    istioTip,
    standaloneMeshRegionList,
    configClusterData,
    configClusterHelpMap,
} from '../constants';
import {useFormInitData} from '../hook/useFormInitData';
import {TServiceMeshType} from '../type';
import {
    meshNameValidate,
    istioValidate,
    istioVersionValidate,
} from './formValidate';
import SelectCluster from './SelectCluster';
import FormPageFooter from './FormPageFooter';
import NetworkType from './NetworkType';
import IstioVersion from './IstioVersion';
import {isHostingMesh, isStandaloneMesh} from './util';
import c from './index.less';
import SecurityGroupCheckbox from './SecurityGroupCheckbox';
import DiscoverySelectorFormItem from './DiscoverySelectorFormItem';
import BlsFormItem from './BlsFormItem';
import MeshMonitorFormItem from './MeshMonitorFormItem';
import TraceFormItem from './TraceFormItem';

export default function CreateServiceMeshForm() {
    const {region: {currentRegion, setRegion}} = useFramework();
    const [isUserChangeRegion, {on: onUserChangeRegion, off: offUserChangeRegion}] = useBoolean(true);

    const {
        formDataInitial, formDataInstante, // 表单的初始数据 和 实例
        serviceMeshTypeData, regionData, clusterData, clusterRbac,
        loading, // 请求状态
    } = useFormInitData(); // 获取到 表单所需要的一些数据

    const {
        data: istioSupportK8sVersionList,
    } = useRequest(getIstioSupportK8sVersionListApi, undefined);

    const [clusterDatasource, setClusterDatasource] = useState(clusterData);
    useEffect(
        () => {
            setClusterDatasource(clusterData);
        },
        [clusterData]
    );
    const [istioDatasource, setIstioDatasource] = useState<string[]>([]);
    useEffect(
        () => {
            setIstioDatasource((istioSupportK8sVersionList || []).map(v => v.istioVersion));
        },
        [currentRegion, istioSupportK8sVersionList]
    );
    useEffect(
        () => {
            setIstioDatasource(() => (istioSupportK8sVersionList || []).reduce(
                (acc: string[], cur) => {
                    acc.push(cur.istioVersion);
                    return acc;
                },
                [])
            );
        },
        [istioSupportK8sVersionList]
    );

    const [regionOptions, setRegionOptions] = useState(regionData);
    const updateRegionAndNetworkType = useCallback(
        () => {
            setRegionOptions(
                regionData.map(
                    v => ({...v, disabled: !hostingMeshRegionList.includes(v.value)})
                )
            );

            if (hostingMeshRegionList.includes(currentRegion)) {
                onUserChangeRegion();
            }
            else {
                setRegion('bj').then(() => {
                    formDataInstante.setFieldsValue({
                        region: 'bj',
                        networkType: {vpcNetworkId: undefined, subnetId: undefined},
                    });
                    onUserChangeRegion();
                });
            }
        },
        [currentRegion, formDataInstante, onUserChangeRegion, regionData, setRegion]
    );
    useEffect(
        () => {
            updateRegionAndNetworkType();
        },
        []
    );
    const onValuesChange = useCallback(
        // eslint-disable-next-line complexity
        changedValues => {
            if (Object.prototype.hasOwnProperty.call(changedValues, 'type')) {
                const {type} = changedValues;
                if (isHostingMesh(type)) {
                    updateRegionAndNetworkType();
                }
                else {
                    setRegionOptions(regionData.map(
                        v => ({...v, disabled: !standaloneMeshRegionList.includes(v.value)})
                    ));
                }
            }
            else if (
                isStandaloneMesh(formDataInstante.getFieldValue('type'))
            ) {
                const {istioVersion, installationClusterId} = formDataInstante.getFieldsValue();

                // 若当前所选的 istioVersion 和 clusterVersion 不匹配的话，则需重置数据
                if (Object.prototype.hasOwnProperty.call(changedValues, 'istioVersion')) {
                    const {istioVersion, installationClusterId} = changedValues;
                    const {
                        clusterVersion = '',
                    } = JSON.parse(installationClusterId || '{}') as IClusterData;
                    const supportedClusterVersionList = (istioSupportK8sVersionList || []).reduce(
                        (acc: string[], cur) => {
                            if (!istioVersion || cur.istioVersion === istioVersion) {
                                acc.push(...cur.supportedClusterVersionList);
                            }
                            return acc;
                        },
                        []
                    );

                    setClusterDatasource(() => clusterData.filter(
                        v => supportedClusterVersionList.includes(v.clusterVersion))
                    );
                    if (
                        installationClusterId
                        && !supportedClusterVersionList.includes(clusterVersion)
                    ) {
                        formDataInstante.setFieldsValue({
                            installationClusterId: undefined,
                        });
                    }
                }
                else if (Object.prototype.hasOwnProperty.call(changedValues, 'installationClusterId')) {
                    const {
                        clusterVersion = '',
                    } = JSON.parse(installationClusterId || '{}') as IClusterData;
                    const supportedIstioVersionList = (istioSupportK8sVersionList || []).reduce(
                        (acc: string[], cur) => {
                            if (!clusterVersion || cur.supportedClusterVersionList.includes(clusterVersion)) {
                                acc.push(cur.istioVersion);
                            }
                            return acc;
                        },
                        []
                    );

                    setIstioDatasource(supportedIstioVersionList);
                    if (
                        istioVersion
                        && !supportedIstioVersionList.includes(istioVersion)
                    ) {
                        formDataInstante.setFieldsValue({
                            istioVersion: undefined,
                        });
                    }
                }
            }
        },
        [formDataInstante, regionData, istioSupportK8sVersionList, clusterData, updateRegionAndNetworkType]
    );

    const formattedFormDataInitial = useMemo(
        () => {
            const formattedFormDataInitial = _.cloneDeep(formDataInitial);
            const matchLabels = formattedFormDataInitial.discoverySelector.matchLabels;
            formattedFormDataInitial.discoverySelector.matchLabels = Object.entries(matchLabels);
            return formattedFormDataInitial;
        },
        [formDataInitial]
    );

    const onRegionChange = useCallback( // 事件 -- 表单地区切换事件 设置导航的地域
        e => {
            setRegion(e.target.value).then(() => {
                onUserChangeRegion();
                formDataInstante.setFieldsValue({
                    installationClusterId: undefined,
                    networkType: {vpcNetworkId: undefined, subnetId: undefined},
                    securityGroupId: undefined,
                    monitor: {instances: []},
                });
            });
        },
        [formDataInstante, onUserChangeRegion, setRegion]
    );

    Form.useLabelLayout('basic', 0);

    return (
        <div className={c.createMeshForm}>
            <div className={c.createMeshFormWrap}>
                <Form
                    name="basic"
                    labelAlign="left"
                    initialValues={formattedFormDataInitial}
                    form={formDataInstante}
                    onValuesChange={onValuesChange}
                    autoComplete="off"
                >
                    <Form.Item label="实例类型" name="type">
                        <Radio.Group optionType="button" options={serviceMeshTypeData} disabled={loading} />
                    </Form.Item>
                    <Form.Item label="当前地域" name="region">
                        <Radio.Group
                            optionType="button"
                            options={regionOptions}
                            onChange={onRegionChange}
                            disabled={loading}
                        />
                    </Form.Item>
                    <Form.Item
                        label="网格名称"
                        help={meshNameHelp}
                        rules={meshNameValidate}
                        name="serviceMeshInstanceName"
                    >
                        <Input
                            className="mesh-name"
                            maxLength={65}
                            showCount
                            placeholder="网格名称不支持修改，请谨慎输入"
                            disabled={loading}
                        />
                    </Form.Item>

                    <Form.Item
                        shouldUpdate
                        noStyle
                    >
                        {({getFieldValue}) => {
                            const type = getFieldValue('type');
                            return (
                                <Form.Item
                                    label={
                                        <>
                                            Istio版本：
                                            {
                                                isStandaloneMesh(type) && (
                                                    <Popover content={istioTip}>
                                                        <QuestionCircleOutlined />
                                                    </Popover>
                                                )
                                            }
                                        </>
                                    }
                                    name="istioVersion"
                                    rules={istioVersionValidate}
                                    colon={false}
                                >
                                    <IstioVersion
                                        formDataInstante={formDataInstante}
                                        type={type}
                                        datasource={istioDatasource}
                                    />
                                </Form.Item>
                            );
                        }}
                    </Form.Item>
                    <Form.Item
                        shouldUpdate
                        noStyle
                    >
                        {({getFieldValue}) => {
                            const type: TServiceMeshType = getFieldValue('type');
                            if (isHostingMesh(type)) {
                                return (
                                    <>
                                        <Form.Item
                                            name="controlPlaneBlb"
                                            label="控制面BLB"
                                        >
                                            <Radio.Group optionType="button" options={blbTypeData} />
                                        </Form.Item>
                                        <Form.Item required label="网络类型">
                                            <NetworkType
                                                isUserChangeRegion={isUserChangeRegion}
                                                offUserChangeRegion={offUserChangeRegion}
                                            />
                                        </Form.Item>
                                        <Form.Item
                                            required
                                            label="安全组"
                                            name={['networkType', 'securityGroupId']}
                                        >
                                            <SecurityGroupCheckbox />
                                        </Form.Item>
                                        {/* TODO 这期暂由后端选取安全组 */}
                                        {/* <Form.Item
                                            required
                                            label="安全组"
                                            name={'securityGroupId'}
                                            rules={securityGroupIdValidate}
                                        >
                                            <SecurityGroupSelect vpcId={vpcId} />
                                        </Form.Item> */}

                                        {/* TODO 后端未ready */}
                                        {/* <Form.Item
                                            label={
                                                <>
                                                    公网访问：{
                                                        <Popover content={elasticPublicNetworkTip}>
                                                            <QuestionCircleOutlined />
                                                        </Popover>
                                                    }
                                                </>
                                            }
                                            colon={false}
                                        >
                                            <ElasticPublicNetwork />
                                        </Form.Item> */}
                                    </>
                                );
                            }
                            else {
                                return (
                                    <>
                                        <Form.Item
                                            label={
                                                <>
                                                    主集群：{
                                                        <Popover content={clusterTip}>
                                                            <QuestionCircleOutlined />
                                                        </Popover>
                                                    }
                                                </>
                                            }
                                            colon={false}
                                            help={clusterHelp}
                                            name="installationClusterId"
                                            rules={istioValidate}
                                        >
                                            {loading
                                                ? <Spin size="small" />
                                                : (
                                                    <SelectCluster
                                                        datasource={clusterDatasource}
                                                        clusterRbac={clusterRbac}
                                                    />
                                                )
                                            }
                                        </Form.Item>
                                        <Form.Item label="关联BLB" name="blbType" help={blbHelp}>
                                            <Radio.Group optionType="button" options={blbTypeData} disabled={loading} />
                                        </Form.Item>
                                    </>
                                );
                            }
                        }}
                    </Form.Item>

                    <DiscoverySelectorFormItem />

                    <BlsFormItem formDataInstante={formDataInstante} />

                    <Form.Item
                        shouldUpdate
                        noStyle
                    >
                        {({getFieldValue}) => {
                            const type = getFieldValue('type');
                            return isStandaloneMesh(type) && (
                                <MeshMonitorFormItem formDataInstante={formDataInstante} />
                            );
                        }}
                    </Form.Item>

                    <MultiProtocolFormItem />

                    <Form.Item
                        shouldUpdate
                        noStyle
                    >
                        {({getFieldValue}) => {
                            const type = getFieldValue('type');
                            const configCluster = getFieldValue('configCluster');

                            if (isHostingMesh(type)) {
                                return (
                                    <Form.Item
                                        name="configCluster"
                                        label="Istio资源配置"
                                        help={configClusterHelpMap.get(configCluster) || ''}
                                    >
                                        <Radio.Group optionType="button" options={configClusterData} />
                                    </Form.Item>
                                );
                            }
                        }}
                    </Form.Item>

                    <Form.Item
                        shouldUpdate
                        noStyle
                    >
                        {({getFieldValue}) => {
                            const type = getFieldValue('type');
                            const configCluster = getFieldValue('configCluster');

                            if (isHostingMesh(type) && configCluster === 'EXTERNAL') {
                                return (
                                    <Form.Item label="API Server访问" name="apiServerEip" valuePropName="checked">
                                        <Checkbox>开启公网访问，否则无法通过外网访问API Server。</Checkbox>
                                    </Form.Item>
                                );
                            }
                        }}
                    </Form.Item>

                    <TraceFormItem type="create" formDataInstante={formDataInstante} />

                    <Form.Item
                        shouldUpdate
                        noStyle
                    >
                        {({getFieldValue}) => {
                            const elasticPublicNetworkType = getFieldValue('elasticPublicNetwork.type');
                            return (
                                <FormPageFooter
                                    elasticPublicNetworkType={elasticPublicNetworkType}
                                    formDataInstante={formDataInstante}
                                    clusterData={clusterData}
                                    loading={loading}
                                />
                            );
                        }}
                    </Form.Item>
                </Form>
            </div>
        </div>
    );
}
