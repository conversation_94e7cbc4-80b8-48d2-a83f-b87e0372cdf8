import {useEffect, useState, useCallback} from 'react';
import {Switch, Route, useHistory} from 'react-router-dom';
import {Spin} from '@osui/ui';
import Home from '@/modules/Home';
import NotFound from '@/components/NotFound';
import ServiceInstance from '@/modules/ServiceMeshInstance';
import RegistrationInstance from '@/modules/RegistrationInstance';
import RegistrationNamespace from '@/modules/RegistrationNamespace';
import CreateRegistration from '@/modules/CreateRegistration';
import ServiceInstanceDetail from '@/modules/ServiceAdmin/serviceInstanceBaseInfo';
import {checkAuthorize} from '@/utils/authorize';
import urls from '@/urls';
import useRegionBlackList from '@/hooks/useRegionBlackList';
import {useFramework} from '@/hooks';
import useRegistrationWhiteList from '@/hooks/useRegistrationWhiteList';
import CreateServiceMesh from '../CreateServiceMesh';
import SubscribeService from '../SubscribeService';
import CrdView from '../CrdList/CrdView';
import CrdCreate from '../CrdList/CrdCreate';
import CrdUpdate from '../CrdList/CrdUpdate';
import AssociateCluster from '../AssociateCluster';
import GatewayCreate from '../GatewayCreate';
import BlbAdd from '../BlbAdd';

export default function App() {
    useRegionBlackList();
    const {account: {accountId}} = useFramework();
    const history = useHistory();
    const [loading, setLoading] = useState(true);
    // 看一下是否有注册中心白名单，如果不在白名单中，且url是注册中心，则跳转网格页
    // @todo为了处理用户直接访问详情url，这里会多请求一次白名单, 后续优化
    useRegistrationWhiteList();
    // @todo 默认用户是授过权的,这块要在优化一下
    const checkAuth = useCallback( // 检测是否开通过服务
        async () => {
            setLoading(true);
            await checkAuthorize(accountId, history);
            setLoading(false);
        },
        [history, accountId]
    );
    useEffect(
        () => {
            checkAuth();
        },
        [checkAuth]
    );

    if (loading) {
        return <Spin />;
    }
    return (
        <Switch>
            <Route path={urls.cluster.associate.path()} component={AssociateCluster} />
            <Route path={urls.subscribeService.path()} component={SubscribeService} />
            <Route path={urls.createServiceMesh.path()} component={CreateServiceMesh} />
            <Route path={urls.gateway.blb.add.path()} component={BlbAdd} />
            <Route path={urls.gateway.create.path()} component={GatewayCreate} />
            <Route path={urls.crd.update.path()} component={CrdUpdate} />
            <Route path={urls.crd.create.path()} component={CrdCreate} />
            <Route path={urls.crd.view.path()} component={CrdView} />
            <Route path={urls.serviceMeshInstanceId.path()} component={ServiceInstance} />
            <Route path={urls.registrationInstanceId.path()} component={RegistrationInstance} />
            <Route path={urls.registrationNamespaces.path()} component={RegistrationNamespace} />
            <Route path={urls.createRegistration.path()} component={CreateRegistration} />
            <Route path={urls.admin.serviceInstanceBaseInfo.path()} component={ServiceInstanceDetail} />
            <Route path={urls.home.path()} component={Home} />
            <Route path="*" component={NotFound} />
        </Switch>
    );
}
