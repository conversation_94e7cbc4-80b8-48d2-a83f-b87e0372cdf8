import React from 'react';
import {Link, Switch, useLocation, Route, Redirect} from 'react-router-dom';
import {Menu} from '@osui/ui';
import styled from 'styled-components';
import Overview from '@/modules/Overview';
import ServiceMeshList from '@/modules/ServiceMeshList';
import RegistrationList from '@/modules/RegistrationList';
import {ServiceAdmin} from '@/modules/ServiceAdmin';
import * as Layout from '@/components/Layout';
import {useNewUser} from '@/hooks/useNewUser';
// import useRegistrationWhiteList from '@/hooks/useRegistrationWhiteList';
import {
    OVERVIEW_GUIDE_LINK,
    OVERVIEW_DASHBOARD_LINK,
    OVERVIEW_LINK,
    SERVICE_MESH_LIST_LINK,
    REGISTRATION_LIST,
    SERVICE_MESH_ADMIN_PATH,
} from '@/links';

// const {SubMenu} = Menu;

const BlankStyled = styled.div`
    height: 4px;
`;

export default function Home() {
    const {pathname} = useLocation();
    const isNewUser = useNewUser();
    // const {inRegistrationWhite} = useRegistrationWhiteList();
    const overviewDefaultPath = isNewUser ? OVERVIEW_GUIDE_LINK : OVERVIEW_DASHBOARD_LINK;
    const newPathName = pathname === OVERVIEW_GUIDE_LINK
     || pathname === OVERVIEW_DASHBOARD_LINK ? OVERVIEW_LINK : pathname;
    return (
        <>
            <Layout.Sider title="服务网格CSM">
                <BlankStyled />
                <Menu selectedKeys={[newPathName]} defaultOpenKeys={['REGISTRATION_CENTER']} mode="inline">
                    <Menu.Item key={OVERVIEW_LINK}>
                        <Link to={overviewDefaultPath}>全局概览</Link>
                    </Menu.Item>
                    <Menu.Item key={SERVICE_MESH_LIST_LINK}>
                        <Link to={SERVICE_MESH_LIST_LINK}>网格列表</Link>
                    </Menu.Item>
                    {/* {inRegistrationWhite
                        && (
                            <SubMenu key={'REGISTRATION_CENTER'} title="注册配置中心">
                                <Menu.Item key={REGISTRATION_LIST}>
                                    <Link to={REGISTRATION_LIST}>实例列表</Link>
                                </Menu.Item>
                                <Menu.Item key={SERVICE_MESH_ADMIN_PATH.fill()}>
                                    <Link to={SERVICE_MESH_ADMIN_PATH.fill()}>服务管理</Link>
                                </Menu.Item>
                            </SubMenu>
                        )
                    } */}
                </Menu>
            </Layout.Sider>
            <Layout.Content>
                <Switch>
                    <Route path={OVERVIEW_LINK} component={Overview} />
                    <Route path={SERVICE_MESH_LIST_LINK} component={ServiceMeshList} />
                    <Route path={REGISTRATION_LIST} component={RegistrationList} />
                    <Route path={SERVICE_MESH_ADMIN_PATH.fill()} component={ServiceAdmin} />
                    <Redirect to={OVERVIEW_LINK} />
                </Switch>
            </Layout.Content>
        </>
    );
}
