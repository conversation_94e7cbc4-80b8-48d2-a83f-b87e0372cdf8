import {OutlinedQuestionCircle} from '@baidu/acud-icon';
import {IColumns, IFilters, ITools} from '@baidu/icloud-ui-pro-table';
import StateTag from '@baidu/icloud-ui-state-tag';
import {Tooltip} from '@osui/ui';
import Region from '@/components/Region';
import {StatusDict} from '@/dicts/sidecar';
import urls from '@/urls';
import ExternalLink from '@/components/ExternalLink';
import {CCE_PROJECT_NAME} from '@/dicts';
import SwitchBox from './SwitchBox';


export const columns: IColumns = [{
    title: '地域',
    dataIndex: 'region',
    render(value) {
        return <Region value={value} projectName={CCE_PROJECT_NAME} />;
    },
}, {
    title: '集群名称',
    dataIndex: 'clusterName',
}, {
    title: '命名空间',
    dataIndex: 'namespace',
    render(value, record) {
        const {clusterId: clusterUuid, namespace: namespaceName, clusterName} = record;
        return (
            <ExternalLink
                href={urls.external.namespaceDetail.fill({}, {clusterUuid, namespaceName, clusterName})}
                value={value}
            />
        );
    },
}, {
    title: '状态',
    dataIndex: 'status',
    filterType: 'single',
    filters: Object.entries(StatusDict).map(([key, value]) => ({value: key, text: value.text})),
    render(value) {
        const data = StatusDict[value];
        return data ? <StateTag type={data.type}>{data.text}</StateTag> : null;
    },
}, {
    title: '创建时间',
    dataIndex: 'createTime',
    renderType: 'dateTime',
    sorter: true,
}, {
    title: (
        <div className="flex">
            <span className="margin-right-8">自动注入</span>
            <Tooltip placement="top" title="运行中的工作负载，用户在重启后，将执行自动注入策略。">
                <OutlinedQuestionCircle />
            </Tooltip>
        </div>
    ),
    dataIndex: 'labels',
    render(value, record, _, {refresh}) {
        return <SwitchBox data={value} record={record} refresh={refresh} />;
    },
}];
export const tools: ITools = [
    {
        renderType: 'refresh',
    },
];
// 筛选区
export const filters: IFilters = [
    {
        name: ['keywordType', 'keyword'],
        renderType: 'groupSearch',
        onFilter: (value, record) => {
            const [field, inputValue] = value || [];
            return !inputValue || (field && record[field].includes(inputValue));
        },
        defaultValue: ['namespace', ''],
        props: {
            options: [
                {
                    label: '命名空间',
                    value: 'namespace',
                },
                {
                    label: '集群名称',
                    value: 'clusterName',
                },
            ],
        },
    },
];
