import {InputNumber, Modal, Tooltip} from '@osui/ui';
import Form, {FormREffects} from '@baidu/icloud-ui-form';
import {useCallback, useMemo, useState} from 'react';
import {OutlinedQuestionCircle} from '@baidu/acud-icon';
import styled from 'styled-components';
import {QuotaItem} from '@/api/sidecar';

const TitleWrapperStyled = styled.div`
    display: flex;
    align-items: center;
    line-height: 22px;
    margin-bottom: 20px;
`;
const TitleStyled = styled.span`
    font-weight: 500;
    font-size: 14px;
`;
const InputNumberStyled = styled(InputNumber)`
    width: 120px;
`;

export enum EditType {
    CPU,
    MEMORY,
}

const checkRequestAndLimit = (formInstance: {formValues: {request: number, limit: number}}, propName: string) => {
    const {formValues: {request = 0, limit = 0}} = formInstance;
    const requestMinVal = 1;
    const limitMinVal = 1;

    if (propName === 'request' && request < requestMinVal) {
        return `request值应>=${requestMinVal}`;
    }
    if (propName === 'limit' && limit < limitMinVal) {
        return `limit值应>=${requestMinVal}`;
    }
    if (request > limit) {
        return 'request值应<=limit值';
    }

    return null;
};
const effects: FormREffects = $ => {
    $.validateField('request', ['request'], formInstance => {
        return checkRequestAndLimit(formInstance, 'request');
    });
    $.validateField('limit', ['limit'], formInstance => {
        return checkRequestAndLimit(formInstance, 'limit');
    });

    $.validateField('request', ['request', 'limit'], formInstance => {
        return checkRequestAndLimit(formInstance, '');
    });
    $.validateField('limit', ['request', 'limit'], formInstance => {
        return checkRequestAndLimit(formInstance, '');
    });
};

interface Iprops {
    type: EditType;
    submitting: boolean;
    visible: boolean;
    quotaItem: QuotaItem;
    onCancel: () => void;
    onOk: (quotaItem: QuotaItem) => void;
}

export default function EditQuotaItem({type, submitting, visible, quotaItem, onCancel, onOk}: Iprops) {
    const {request = 0, limit = 0, unit = ''} = quotaItem;
    const [formData, setFormData] = useState({request, limit});

    const onSubmit = useCallback(
        () => {
            const {request, limit} = formData;
            onOk({request: parseInt(String(request), 10), limit: parseInt(String(limit), 10)});
        },
        [formData, onOk]
    );

    const title = useMemo(
        () => {
            const titleMap = new Map([
                [EditType.CPU, 'CPU配额'],
                [EditType.MEMORY, '内存配额'],
            ]);
            return titleMap.get(type) ?? '';
        },
        [type]
    );

    return (
        <Modal title="编辑" confirmLoading={submitting} visible={visible} onCancel={onCancel} onOk={onSubmit}>
            <TitleWrapperStyled>
                <TitleStyled className="margin-right-8">{title}</TitleStyled>
                <Tooltip
                    placement="top"
                    title="request用户资源预分配，若节点资源少于申请资源量，容器将创建失败。limit用于限制容器使用的资源上限，避免异常情况下消耗过多的节点资源"
                >
                    <OutlinedQuestionCircle />
                </Tooltip>
            </TitleWrapperStyled>

            <Form effects={effects} initialValues={formData} onChange={setFormData}>
                <Form.Field
                    label="request"
                    name="request"
                    required
                >
                    <InputNumberStyled
                        placeholder=" "
                        addonAfter={unit}
                        type="number"
                    />
                </Form.Field>

                <Form.Field
                    label="limit"
                    name="limit"
                    required
                >
                    <InputNumberStyled
                        placeholder=" "
                        addonAfter={unit}
                        type="number"
                    />
                </Form.Field>
            </Form>
        </Modal>
    );
}
