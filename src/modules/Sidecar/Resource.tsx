import {Spin, Row, Col, But<PERSON>, message, Alert} from '@osui/ui';
import {useCallback, useEffect, useState} from 'react';
import {useBoolean} from 'huse';
import styled from 'styled-components';
import Card from '@baidu/icloud-ui-card';
import {OutlinedEditingSquare} from '@baidu/acud-icon';
import {getSidecarQuotaApi, QuotaItem, SidecarQuotaResponse, updateSidecarQuotaApi} from '@/api/sidecar';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import EditQuotaItem, {EditType} from './EditQuotaItem';

const TitleStyled = styled.div`
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 18px;
    font-weight: 500;
`;

const AlertContainer = styled(Alert)`margin-bottom: 16px;`;

const OutlinedEditingSquareStyled = styled(OutlinedEditingSquare)`
    margin: 0 8px 0 16px;
`;

export default function Resource() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();

    const [loading, {on, off}] = useBoolean(true);
    const [sidecarQuota, setSidecarQuota] = useState<SidecarQuotaResponse>();

    // 编辑 CPU配额
    const [cpuQuotaSubmitting, {on: onCpuQuotaSubmitting, off: offCpuQuotaSubmitting}] = useBoolean(false);
    const [isEditCpuQuota, {on: onEditCpuQuota, off: offEditCpuQuota}] = useBoolean(false);
    const submitEditCpuQuota = useCallback(
        async (quotaItem: QuotaItem) => {
            const {request, limit} = quotaItem;
            try {
                onCpuQuotaSubmitting();
                const res = await updateSidecarQuotaApi({serviceMeshInstanceId, cpuQuota: {request, limit}});
                setSidecarQuota(res);
                message.success('CPU配额编辑成功。');
                offEditCpuQuota();
            } catch (error) {
                message.error('CPU配额编辑失败，请重新操作。');
            } finally {
                offCpuQuotaSubmitting();
            }
        },
        [offCpuQuotaSubmitting, offEditCpuQuota, onCpuQuotaSubmitting, serviceMeshInstanceId]
    );

    // 编辑 内存配额
    const [memoryQuotaSubmitting, {on: onMemoryQuotaSubmitting, off: offMemoryQuotaSubmitting}] = useBoolean(false);
    const [isEditMemoryQuota, {on: onEditMemoryQuota, off: offEditMemoryQuota}] = useBoolean(false);
    const submitEditMemoryQuota = useCallback(
        async (quotaItem: QuotaItem) => {
            const {request, limit} = quotaItem;
            try {
                onMemoryQuotaSubmitting();
                const res = await updateSidecarQuotaApi({serviceMeshInstanceId, memoryQuota: {request, limit}});
                setSidecarQuota(res);
                message.success('内存配额编辑成功。');
                offEditMemoryQuota();
            } catch (error) {
                message.error('内存配额编辑失败，请重新操作。');
            } finally {
                offMemoryQuotaSubmitting();
            }
        },
        [offEditMemoryQuota, offMemoryQuotaSubmitting, onMemoryQuotaSubmitting, serviceMeshInstanceId]
    );

    useEffect(
        () => {
            (async () => {
                try {
                    on();
                    const res = await getSidecarQuotaApi({serviceMeshInstanceId});
                    setSidecarQuota(res);
                } catch (error) {
                    // console.log(error);
                } finally {
                    off();
                }
            })();
        },
        [off, on, serviceMeshInstanceId]
    );

    if (loading) {
        return <Spin />;
    }

    const {cpuQuota, memoryQuota} = sidecarQuota || {};
    return (
        <>
            <AlertContainer
                showIcon
                message={'配额变更后，请进行重启，新配置才能生效。'}
                type="warning"
            />

            <Row gutter={16}>
                <Col span={8}>
                    <TitleStyled>CPU配额
                        <Button type="link" onClick={onEditCpuQuota}>
                            <OutlinedEditingSquareStyled />编辑
                        </Button>
                    </TitleStyled>
                    <Card.Field title="request">{cpuQuota?.request} {cpuQuota?.unit}</Card.Field>
                    <Card.Field title="limit">{cpuQuota?.limit} {cpuQuota?.unit}</Card.Field>
                </Col>
                <Col span={8}>
                    <TitleStyled>内存配额
                        <Button type="link" onClick={onEditMemoryQuota}>
                            <OutlinedEditingSquareStyled />编辑
                        </Button>
                    </TitleStyled>
                    <Card.Field title="request">{memoryQuota?.request} {memoryQuota?.unit}</Card.Field>
                    <Card.Field title="limit">{memoryQuota?.limit} {memoryQuota?.unit}</Card.Field>
                </Col>
            </Row>

            <EditQuotaItem
                type={EditType.CPU}
                submitting={cpuQuotaSubmitting}
                visible={isEditCpuQuota}
                quotaItem={sidecarQuota?.cpuQuota as QuotaItem}
                onCancel={offEditCpuQuota}
                onOk={submitEditCpuQuota}
            />

            <EditQuotaItem
                type={EditType.MEMORY}
                submitting={memoryQuotaSubmitting}
                visible={isEditMemoryQuota}
                quotaItem={sidecarQuota?.memoryQuota as QuotaItem}
                onCancel={offEditMemoryQuota}
                onOk={submitEditMemoryQuota}
            />
        </>
    );
}
