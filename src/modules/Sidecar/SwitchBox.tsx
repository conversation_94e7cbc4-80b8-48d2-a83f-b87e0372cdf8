import {message, Modal, Switch, Tooltip} from '@osui/ui';
import {useCallback, useState} from 'react';
import {updateInjectStatusApi} from '@/api/sidecar';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getModalContent, getModalTitle} from './util';

interface IProps {
    data: string;
    record: any;
    refresh: () => void;
}
function SwitchBox({data, record, refresh}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [checked, setChecked] = useState(data?.indexOf('istio-injection:enabled') > -1);
    const onChange = useCallback(
        async enabled => {
            const opText = enabled ? '开启' : '关闭';
            Modal.confirm({
                title: getModalTitle(enabled),
                content: getModalContent(enabled),
                async onOk() {
                    try {
                        const {namespace, clusterId: clusterUuid} = record;
                        const params = {
                            serviceMeshInstanceId,
                            namespace,
                            clusterUuid,
                            enabled: enabled,
                        };
                        await updateInjectStatusApi(params);
                        message.success(`${opText}Sidecar自动注入成功`);
                    } catch (error) {
                        setChecked(!enabled);
                        message.error(`${opText}Sidecar自动注入失败，请重新操作。`);
                    } finally {
                        refresh();
                    }
                },
                onCancel() {
                    setChecked(!enabled);
                },
                closable: true,
            });

        },
        [record, refresh, serviceMeshInstanceId]
    );
    return (
        <Tooltip
            placement="top"
            title={'未开启自动注入的命名空间，仅Pod的Annotation为 <sidecar.istio.io/inject="true"> 的工作负载会注入Sidecar。'}
        >
            <Switch
                onChange={onChange}
                checked={checked}
            />
        </Tooltip>
    );
}
export default SwitchBox;
