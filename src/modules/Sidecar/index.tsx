import React from 'react';
import Card from '@baidu/icloud-ui-card';
import styled from 'styled-components';
import Resource from './Resource';
import Inject from './Inject';

const LineStyled = styled.div`
    height: 1px;
    background-color: var(--color-gray-4);
    margin-top: 9px;
`;
const CardStyled = styled(Card)`
    margin-top: -16px;
`;
export default function Sidecar() {
    return (
        <div>
            <Card title="Sidecar 资源配置">
                <Resource />
                <LineStyled />
            </Card>
            <CardStyled title="自动注入配置">
                <Inject />
            </CardStyled>
        </div>
    );
}
