import ProTable, {IOperations} from '@baidu/icloud-ui-pro-table';
import {Alert, Button, message, Modal} from '@osui/ui';
import React, {useCallback} from 'react';
import styled from 'styled-components';
import {getSidecarNamespaceApi, updateInjectStatusApi} from '@/api/sidecar';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {useListSelectedRow, useListTotal} from '@/hooks/useList';
import {getModalTitle, getModalContent} from './util';
import {columns, filters, tools} from './columns';

const AlertContainer = styled(Alert)`margin-bottom: 16px;`;

function BulkOperation({selectedRowKeys, refresh, setSelectedRowKeys, isOpen}) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const text = isOpen ? '开启' : '关闭';
    const handleClick = useCallback(
        () => {
            const opText = isOpen ? '开启' : '关闭';
            Modal.confirm({
                title: getModalTitle(isOpen),
                content: getModalContent(isOpen),
                async onOk() {
                    try {
                        await Promise.all(
                            selectedRowKeys.map(recordString => {
                                const [namespace, clusterUuid] = recordString.split('#');

                                const params = {
                                    serviceMeshInstanceId,
                                    namespace,
                                    clusterUuid,
                                    enabled: isOpen,
                                };
                                return updateInjectStatusApi(params);
                            }));
                        message.success(`${opText}Sidecar自动注入成功`);
                    } catch (error) {
                        message.error(`${opText}Sidecar自动注入失败，请重新操作。`);
                    } finally {
                        setSelectedRowKeys([]);
                        refresh();
                    }
                },
                onCancel() {

                },
                closable: true,
            });
        },
        [isOpen, refresh, selectedRowKeys, serviceMeshInstanceId, setSelectedRowKeys]
    );
    return (
        <Button
            disabled={selectedRowKeys.length === 0}
            onClick={handleClick}
        >
            {text}
        </Button>
    );
}


export default function Inject() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {selectedRowKeys, rowSelection, setSelectedRowKeys} = useListSelectedRow();
    const {request, total} = useListTotal(getSidecarNamespaceApi);
    const getList = useCallback(
        params => {
            return request({
                serviceMeshInstanceId,
                ...params,
            });
        },
        [request, serviceMeshInstanceId]
    );
    const rowKey = useCallback(
        record => `${record.namespace}#${record.clusterId}#${record.labels?.join(';')}`,
        []
    );

    const operations: IOperations = [
        {
            render(props, {refresh}) {
                return (
                    <BulkOperation
                        isOpen
                        selectedRowKeys={selectedRowKeys}
                        setSelectedRowKeys={setSelectedRowKeys}
                        refresh={refresh}
                    />
                );
            },
        },
        {
            render(props, {refresh}) {
                return (
                    <BulkOperation
                        isOpen={false}
                        selectedRowKeys={selectedRowKeys}
                        setSelectedRowKeys={setSelectedRowKeys}
                        refresh={refresh}
                    />
                );
            },
        },
        {
            render() {
                return `已选中${selectedRowKeys.length}条，共${total}条`;
            },
        },
    ];
    return (
        <div>
            <AlertContainer
                showIcon
                message={
                    `开启自动注入，除Annotation为 <sidecar.istio.io/inject=“false”> 的Pod的工作负载，均会注入Sidecar；
                    命名空间将被配置标签 <istio-injection:enabled>，该标签将在关闭时被移除。`
                }
                type="warning"
            />
            <ProTable
                request={getList}
                rowKey={rowKey}
                columns={columns}
                rowSelection={rowSelection}
                operations={operations}
                filters={filters}
                tools={tools}
            />
        </div>
    );
}
