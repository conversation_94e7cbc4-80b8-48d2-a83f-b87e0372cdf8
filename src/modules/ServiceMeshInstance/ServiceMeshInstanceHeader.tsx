import {Spin} from '@osui/ui';
import {useHistory} from 'react-router-dom';
import {useCallback} from 'react';
import styled from 'styled-components';
import StateTag from '@baidu/icloud-ui-state-tag';
import * as Layout from '@/components/Layout';
import {useQuery} from '@/hooks/useUrl';
import {useCurrentServiceMeshInstance} from '@/modules/ServiceMeshInstance';
import {SERVICE_MESH_LIST_LINK} from '@/links';
import {ServiceMeshStatus, ServiceMeshStatusDict} from '@/dicts/serviceMesh';
import ProductDocLink from '@/components/ProductDocLink';

const TitleAreaStyled = styled.div`
    display: flex;
`;
const TitleStyled = styled.section`
    margin-right: 12px;
    font-size: var(--font-size-large);
`;

interface TitleAreaProps{
    title: string;
    status: ServiceMeshStatus;
}
function TitleArea({title, status}: TitleAreaProps) {
    const data = ServiceMeshStatusDict[status] ?? {};
    return (
        <TitleAreaStyled>
            <TitleStyled>{title}</TitleStyled>
            <StateTag type={data.value}>{data.text}</StateTag>
        </TitleAreaStyled>
    );
}

export default function ServiceMeshInstanceHeader() {
    let data = useCurrentServiceMeshInstance();
    const {
        status,
        serviceMeshInstanceId,
        instanceName,
    } = useQuery<
        {
            status: ServiceMeshStatus;
            serviceMeshInstanceId: string;
            instanceName: string;
        }
    >();
    if (!data) {
        data = {status, OverviewOfSidecar: {instanceId: serviceMeshInstanceId, instanceName}};
    }

    const history = useHistory();
    const onHeaderBack = useCallback(
        () => {
            history.push(SERVICE_MESH_LIST_LINK);
        },
        [history]
    );

    return (
        <Layout.Header
            backIcon
            onBack={onHeaderBack}
            title={
                data.OverviewOfSidecar.instanceName
                    ? (
                        <TitleArea
                            title={data.OverviewOfSidecar.instanceName}
                            status={data.status}
                        />
                    )
                    : <Spin size="small" />
            }
            extra={
                <ProductDocLink />
            }
        />
    );
}
