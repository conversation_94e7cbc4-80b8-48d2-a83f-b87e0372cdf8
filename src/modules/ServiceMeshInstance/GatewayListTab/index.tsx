import {Menu} from '@osui/ui';
import {Link} from 'react-router-dom';
import {useEffect, useMemo} from 'react';
import urls from '@/urls';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {isHostingMesh} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/util';
import {TServiceMeshType} from '@/modules/CreateServiceMesh/type';
import {useQuery} from '@/hooks/useUrl';
import {useCurrentServiceMeshInstance} from '..';
import {getServiceMeshInstanceTypeByLocalStorage, setServiceMeshInstanceTypeByLocalStorage} from './util';

interface IProps {
    key: string;
}
export default function GatewayListTab(props: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {type: typeFromQuery} = useQuery<{ type: TServiceMeshType }>();
    const data = useCurrentServiceMeshInstance();

    const url = useMemo(
        () => {
            return urls.gateway.list.fill({serviceMeshInstanceId});
        },
        [serviceMeshInstanceId]
    );

    const isHostingMeshType = useMemo(
        () => {
            return isHostingMesh(typeFromQuery)
                || isHostingMesh(getServiceMeshInstanceTypeByLocalStorage(serviceMeshInstanceId))
                || isHostingMesh(data?.type);
        },
        [data?.type, serviceMeshInstanceId, typeFromQuery]
    );

    useEffect(
        () => {
            if (data && data?.type) {
                setServiceMeshInstanceTypeByLocalStorage(serviceMeshInstanceId, data?.type);
            }
        },
        [data, serviceMeshInstanceId]
    );

    return (
        <>
            {
                isHostingMeshType
                && (
                    <Menu.Item {...props}>
                        <Link to={url}>网关管理</Link>
                    </Menu.Item>
                )
            }
        </>
    );
}
