import {TServiceMeshType} from '@/modules/CreateServiceMesh/type';

export const getServiceMeshInstanceTypeByLocalStorage = (serviceMeshInstanceId: string) => {
    const serviceMeshInstanceTypeObj = JSON.parse(localStorage.getItem('serviceMeshInstanceTypeObjStr') || '{}');
    return serviceMeshInstanceTypeObj[serviceMeshInstanceId];
};

export const setServiceMeshInstanceTypeByLocalStorage = (serviceMeshInstanceId: string, type: TServiceMeshType) => {
    const serviceMeshInstanceTypeObj = JSON.parse(localStorage.getItem('serviceMeshInstanceTypeObjStr') || '{}');
    serviceMeshInstanceTypeObj[serviceMeshInstanceId] = type;
    localStorage.setItem('serviceMeshInstanceTypeObjStr', JSON.stringify(serviceMeshInstanceTypeObj));
};
