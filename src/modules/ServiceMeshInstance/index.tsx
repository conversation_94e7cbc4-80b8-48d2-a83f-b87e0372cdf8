import {Link, useHistory, useLocation} from 'react-router-dom';
import React, {createContext, useCallback, useContext, useEffect, useMemo} from 'react';
import {useRequestCallback} from 'huse';
import {Menu} from '@osui/ui';
import styled from 'styled-components';
import {getServiceMeshInstanceApi} from '@/api/serviceMesh';
import {useCurrentServiceMeshIdSafe, useFramework} from '@/hooks';
import {R4} from '@/styles';
import urls from '@/urls';
import {ServiceMeshInstance} from '@/interface/serviceMesh';
import PageLoading from '@/components/PageLoading';
import {SERVICE_MESH_LIST_LINK} from '@/links';
import useVisibilityChange from '@/hooks/useVisibilityChange';
import NewFeatureTag from '@/components/NewFeatureTag';
import {isHostingMesh, isStandaloneMesh} from '../CreateServiceMesh/CreateServiceMeshForm/util';
import {TServiceMeshType} from '../CreateServiceMesh/type';
import {routes} from './routes';
import ServiceMeshInstanceHeader from './ServiceMeshInstanceHeader';
import {diagnosisQueryDft} from './constant';

const Main = styled.div`
    background-color: white;
    border-radius: ${R4};
    overflow: hidden;
    margin: 16px;
    min-height: calc(100vh - 50px - 48px - 32px);
`;

const MenuContainerStyled = styled.div`
    height: inherit;
    border-right: 1px solid #f2f2f4;
`;
// @todo 没写完
const defaultValue = {
    type: '',
    status: '',
    istioVersion: '',
    controlPanelAddress: '',
    billingModel: '',
    VpcInfo: {
        vpcId: '',
        vpcName: '',
        vpcCidr: '',
    },
    OverviewOfSidecar: {
        instanceId: '',
        instanceName: '',
        num: NaN,
    },
    ClusterInfo: {
        clusterId: '',
        clusterName: '',
    },
    BlbInfo: {
        blbId: '',
        blbName: '',
        blbStatus: '',
    },
};
const CurrentServiceMeshInstanceContext = createContext(defaultValue as ServiceMeshInstance);
const CurrentServiceMeshInstanceContextProvider = CurrentServiceMeshInstanceContext.Provider;
export const useCurrentServiceMeshInstance = () => {
    return useContext(CurrentServiceMeshInstanceContext);
};
export default function ServiceInstance() {
    const history = useHistory();
    const {pathname} = useLocation();
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [request, {data}] = useRequestCallback(getServiceMeshInstanceApi, {
        serviceMeshInstanceId,
    });
    const {region: {onRegionChange, unRegionChange}} = useFramework();
    const goToServiceMeshList = useCallback(
        () => {
            history.push(SERVICE_MESH_LIST_LINK);
        },
        [history]
    );
    useEffect(
        () => {
            onRegionChange(goToServiceMeshList);
            return () => unRegionChange(goToServiceMeshList);
        },
        [goToServiceMeshList, onRegionChange, unRegionChange]
    );

    useEffect(
        () => {
            request();
        },
        [request]
    );
    useVisibilityChange({onShow: request});
    // 暂时不定时刷新了，感觉会有bug
    // useInterval(request, 5000);
    const SERVICE_MESH_BASE_INFO_PATH = urls.serviceMeshBaseInfo.fill({serviceMeshInstanceId});
    const CLUSTER_LIST_PATH = urls.cluster.list.fill({serviceMeshInstanceId});
    const GATEWAY_LIST_PATH = urls.gateway.list.fill({serviceMeshInstanceId});
    const CRD_LIST_PATH = urls.crd.list.fill({serviceMeshInstanceId});
    const SERVICE_LIST_PATH = urls.serviceList.fill({serviceMeshInstanceId});
    const SIDECAR_PATH = urls.sidecar.fill({serviceMeshInstanceId});
    const SWIM_LANE_GROUP__PATH = urls.swimLaneGroup.fill({serviceMeshInstanceId});
    const BLS_LOG_PATH = urls.blsLog.fill({serviceMeshInstanceId});
    const COMP_LIST_PATH = urls.compList.fill({serviceMeshInstanceId});
    const DIAGNOSIS_PATH_KEY = urls.diagnosis.fill(
        {serviceMeshInstanceId}
    );
    const CPROM_PATH = urls.cprom.fill({serviceMeshInstanceId});
    const TRACE_PATH = urls.trace.fill({serviceMeshInstanceId});
    const DIAGNOSIS_PATH = urls.diagnosis.fill(
        {serviceMeshInstanceId},
        {...diagnosisQueryDft, namespace: data?.type === 'standalone' ? 'default' : undefined}
    );

    const defaultKeys = useMemo(
        () => (pathname.includes(SERVICE_LIST_PATH)
            ? SERVICE_LIST_PATH
            : pathname.includes(GATEWAY_LIST_PATH)
                ? GATEWAY_LIST_PATH
                : pathname),
        [GATEWAY_LIST_PATH, SERVICE_LIST_PATH, pathname]
    );

    return (
        <CurrentServiceMeshInstanceContextProvider value={data as ServiceMeshInstance}>
            <div>
                <ServiceMeshInstanceHeader />
                <Main className="flex">
                    <MenuContainerStyled className="flex-none">
                        <Menu
                            items={
                                [
                                    {
                                        label: <Link to={SERVICE_MESH_BASE_INFO_PATH}>基本信息</Link>,
                                        key: SERVICE_MESH_BASE_INFO_PATH,
                                    },
                                    {
                                        label: <Link to={CLUSTER_LIST_PATH}>集群管理</Link>,
                                        key: CLUSTER_LIST_PATH,
                                    },
                                    {
                                        label: <Link to={GATEWAY_LIST_PATH}>网关管理</Link>,
                                        key: GATEWAY_LIST_PATH,
                                    },
                                    {
                                        label: <Link to={CRD_LIST_PATH}>Istio资源管理</Link>,
                                        key: CRD_LIST_PATH,
                                    },
                                    {
                                        label: <Link to={SERVICE_LIST_PATH}>服务列表</Link>,
                                        key: SERVICE_LIST_PATH,
                                    },
                                    {
                                        label: <Link to={SIDECAR_PATH}>注入配置</Link>,
                                        key: SIDECAR_PATH,
                                    },
                                    {
                                        label: <Link to={DIAGNOSIS_PATH}>诊断工具</Link>,
                                        key: DIAGNOSIS_PATH_KEY,
                                    },
                                    {
                                        label: <Link to={COMP_LIST_PATH}>组件管理</Link>,
                                        key: COMP_LIST_PATH,
                                    },
                                    {
                                        label: <Link to={SWIM_LANE_GROUP__PATH}>流量泳道</Link>,
                                        key: SWIM_LANE_GROUP__PATH,
                                    },
                                    {
                                        label: '可观测管理',
                                        key: 'monitor',
                                        children: [
                                            {
                                                label: (
                                                    <Link to={BLS_LOG_PATH}>
                                                        日志中心
                                                    </Link>
                                                ),
                                                key: BLS_LOG_PATH,
                                            },
                                            {
                                                label: (
                                                    <Link to={CPROM_PATH}>
                                                        Prometheus 监控
                                                    </Link>
                                                ),
                                                key: CPROM_PATH,
                                            },
                                            {
                                                label: (
                                                    <Link to={TRACE_PATH}>
                                                        链路追踪
                                                        <NewFeatureTag />
                                                    </Link>
                                                ),
                                                key: TRACE_PATH,
                                            },
                                        ],
                                    },
                                ].filter(
                                    v => (v.key !== COMP_LIST_PATH || isStandaloneMesh(data?.type as TServiceMeshType))
                                        // eslint-disable-next-line max-len
                                        && (v.key !== GATEWAY_LIST_PATH || isHostingMesh(data?.type as TServiceMeshType))
                                )
                            }
                            selectedKeys={[defaultKeys]}
                            className="secondary-menu"
                            mode="inline"
                        />
                    </MenuContainerStyled>

                    <div className="flex-auto">
                        {data ? routes : <PageLoading />}
                    </div>
                </Main>
            </div>
        </CurrentServiceMeshInstanceContextProvider>
    );
}
