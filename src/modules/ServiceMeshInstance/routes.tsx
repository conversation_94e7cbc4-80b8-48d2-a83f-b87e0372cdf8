import {Switch, Route} from 'react-router-dom';
import ServiceMeshBaseInfo from '@/modules/ServiceMeshBaseInfo';
import ServiceDetail from '@/modules/ServiceDetail';
import ServiceList from '@/modules/ServiceList';
import ServiceMeshCluster from '@/modules/ServiceMeshCluster';
import CrdList from '@/modules/CrdList';
import Sidecar from '@/modules/Sidecar';
import urls from '@/urls';
import GatewayList from '@/modules/GatewayList';
import ServiceMeshCompList from '@/modules/ServiceMeshCompList';
import GatewayDetail from '../GatewayDetail';
import SwimLaneGroup from '../SwimLaneGroup';
import Diagnosis from '../Diagnosis';
import <PERSON><PERSON><PERSON> from '../Cprom';
import BlsLog from '../BlsLog';
import Trace from '../Trace';

export const routes = (
    <Switch>
        <Route path={urls.serviceMeshBaseInfo.path()} component={ServiceMeshBaseInfo} />
        <Route path={urls.serviceDetail.path()} component={ServiceDetail} />
        <Route path={urls.serviceList.path()} component={ServiceList} />
        <Route path={urls.cluster.list.path()} component={ServiceMeshCluster} />
        <Route path={urls.gateway.detail.path()} component={GatewayDetail} />
        <Route path={urls.gateway.list.path()} component={GatewayList} />
        <Route path={urls.crd.list.path()} component={CrdList} />
        <Route path={urls.sidecar.path()} component={Sidecar} />
        <Route path={urls.swimLaneGroup.path()} component={SwimLaneGroup} />
        <Route path={urls.blsLog.path()} component={BlsLog} />
        <Route path={urls.compList.path()} component={ServiceMeshCompList} />
        <Route path={urls.cprom.path()} component={Cprom} />
        <Route path={urls.trace.path()} component={Trace} />
        <Route path={urls.diagnosis.path()} component={Diagnosis} />
    </Switch>
);
