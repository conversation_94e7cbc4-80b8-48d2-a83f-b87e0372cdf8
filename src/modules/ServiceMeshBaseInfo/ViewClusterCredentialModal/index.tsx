import {Button, Modal, Radio, Tooltip} from '@osui/ui';
import {useRequestCallback} from 'huse';
import {useMemo, useCallback, useState, useEffect} from 'react';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import MonacoEditorComponent from '@/components/MonacoEditorComponent';
import {copyText, downloadText} from '@/utils';
import {getServiceMeshKubeconfig} from '@/api/serviceMesh';
import c from './index.less';


export default function ViewClusterCredentialModal(
    {visible, onCancel, isOpenServerEip}: {visible: boolean, onCancel: () => void, isOpenServerEip: boolean}) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const [kubeConfigType, setType] = useState<string>('vpc');
    const [request, {data: kubeData}] = useRequestCallback(getServiceMeshKubeconfig,
        {serviceMeshInstanceId, kubeConfigType});

    useEffect(
        () => {
            request();
        },
        [request, kubeConfigType]
    );
    const options = [
        {
            label: 'VPC访问凭证',
            value: 'vpc',
        },
        {
            label: '公网访问凭证',
            value: 'public',
            disabled: !isOpenServerEip,
        },
    ];
    const value = useMemo(
        () => {
            return kubeData?.kubeConfig || '';
        },
        [kubeData]
    );
    const onTypeChange = useCallback(
        (e: any) => {
            setType(e.target.value);
        },
        [setType]
    );
    return (
        <Modal
            className={c['view-cluster-credential-modal']}
            width={800}
            title="查看网格凭证"
            footer={null}
            visible={visible}
            onCancel={onCancel}
        >
            <div className={c['action-wrapper']}>
                <Radio.Group onChange={onTypeChange} optionType="button" options={options} value={kubeConfigType} />
                <Tooltip placement={'top'} title={'复制长期访问凭证'}>
                    <Button
                        className={c['download-btn']}
                        type="link"
                        // eslint-disable-next-line react/jsx-no-bind
                        onClick={() => copyText(value)}
                    >复制
                    </Button>
                </Tooltip>
                <Tooltip placement={'top'} title={'下载长期访问凭证'}>
                    <Button
                        className={c['download-btn']}
                        type="link"
                        // eslint-disable-next-line react/jsx-no-bind
                        onClick={() => downloadText(value, 'kubectl.conf')}
                    >下载
                    </Button>
                </Tooltip>
            </div>
            <MonacoEditorComponent
                language="yaml"
                width="760px"
                height="400px"
                theme="vs-dark"
                value={value}
                options={{
                    readOnly: true,
                    lineNumbers: 'off',
                    wordWrap: 'on',
                    wrappingIndent: 'indent',
                    scrollBeyondLastLine: false,
                    contextmenu: false,
                }}
            />
        </Modal>
    );
}
