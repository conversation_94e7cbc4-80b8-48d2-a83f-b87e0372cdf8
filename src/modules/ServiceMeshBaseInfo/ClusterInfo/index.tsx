import ExternalLink from '@/components/ExternalLink';
import {ServiceMeshInstance} from '@/interface/serviceMesh';
import urls from '@/urls';

interface IProps {
    serviceMeshInstance: ServiceMeshInstance;
}

export default function ClusterInfo({serviceMeshInstance}: IProps) {
    return (
        <ExternalLink
            href={urls.external.clusterDetail.fill({},
                {
                    clusterUuid: serviceMeshInstance.ClusterInfo.clusterId,
                    clusterName: serviceMeshInstance.ClusterInfo.clusterName,
                }
            )}
            value={
                [
                    serviceMeshInstance.ClusterInfo.clusterName,
                    serviceMeshInstance.ClusterInfo.clusterId,
                ].join('/')
            }
        />
    );
}
