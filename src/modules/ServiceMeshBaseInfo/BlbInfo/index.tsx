import {Tooltip} from '@osui/ui';
import {OutlinedBceWarningCircle} from '@baidu/acud-icon';
import {ServiceMeshInstance} from '@/interface/serviceMesh';
import {BlbStatusDict, BlbStatusValue} from '@/dicts/serviceMesh';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';

interface IProps {
    serviceMeshInstance: ServiceMeshInstance;
}

export default function BlbInfo({serviceMeshInstance}: IProps) {
    return (
        <>
            {
                serviceMeshInstance.BlbInfo
            && serviceMeshInstance.BlbInfo.blbStatus === BlbStatusValue.pending
                    ? (
                        <div>{BlbStatusDict[BlbStatusValue.pending]}</div>
                    )
                    : serviceMeshInstance.BlbInfo
                && serviceMeshInstance.BlbInfo.blbStatus === BlbStatusValue.running
                        ? (
                            <ExternalLink
                                href={
                                    urls.external.blbDetail.fill({},
                                        {appblbId: serviceMeshInstance.BlbInfo.blbId}
                                    )
                                }
                                value={serviceMeshInstance.BlbInfo.blbId}
                            />
                        )
                        : (
                            <div className="flex">
                                未关联BLB
                                <Tooltip title="未关联BLB，仅支持单集群服务治理">
                                    <OutlinedBceWarningCircle className="warning-icon" />
                                </Tooltip>
                            </div>
                        )
            }
        </>
    );
}
