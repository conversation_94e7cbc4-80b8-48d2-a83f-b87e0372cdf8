import _ from 'lodash';
import {RegionList} from '@/hooks';
import {IMonitorItem} from '@/api/createServiceMesh';

export const getMonitorInstanceLink = (monitor: IMonitorItem, regionList: RegionList) => {
    // 注：后端数据会有异常（如 monitor.instances为 [] ），则 name、id、region 需展示为 -- 。
    const {name = '--', id = '--', region = ''} = _.get(monitor, 'instances.[0]', {});
    const regionText = regionList[region] ?? '--';

    return [
        name,
        `${id}（${regionText}）`,
    ].join('/');
};
