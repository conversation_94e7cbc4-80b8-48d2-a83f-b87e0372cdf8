import React, {useEffect, useState, useMemo} from 'react';
import {useBoolean, useRequest} from 'huse';
import Card from '@baidu/icloud-ui-card';
import Text from '@baidu/icloud-ui-text';
import {Row, Col} from '@osui/ui';
import StateTag from '@baidu/icloud-ui-state-tag';
import urls from '@/urls';
import ExternalLink from '@/components/ExternalLink';
import {getServiceMeshInstanceApi} from '@/api/serviceMesh';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import PageLoading from '@/components/PageLoading';
import {
    ServiceMeshBillingModeDict,
    ServiceMeshStatusDict,
    SERVICE_MESH_TYPE_DICT,
    DEFAULT_VALUE,
    multiProtocolEnabledMap,
} from '@/dicts/serviceMesh';
import UpgradeIstioButton from '@/components/UpgradeIstioButton';
import EditDiscoverySelector from '@/components/EditDiscoverySelector';
import {isShowMultiProtocol} from '@/components/MultiProtocolFormItem/util';
import {isStandaloneMesh} from '../CreateServiceMesh/CreateServiceMeshForm/util';
import ViewClusterCredentialModal from './ViewClusterCredentialModal';
import BlbInfo from './BlbInfo';
import ClusterInfo from './ClusterInfo';
import Title from './Title';
import c from './index.less';

// TODO 拆组件
// eslint-disable-next-line complexity
export default function ServiceInstanceDetail() {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {data, pending} = useRequest(getServiceMeshInstanceApi, {
        serviceMeshInstanceId,
    });
    const [serviceMeshInstance, setServiceMeshInstance] = useState(data);
    useEffect(
        () => {
            setServiceMeshInstance(data);
        },
        [data]
    );

    const isHostingMesh = useMemo(
        () => {
            return serviceMeshInstance?.type === 'hosting';
        },
        [serviceMeshInstance?.type]
    );

    const configCluster = useMemo(
        () => serviceMeshInstance?.configCluster || 'EXTERNAL',
        [serviceMeshInstance?.configCluster]
    );

    // Cprom
    // const [editMonitorVisible, {on: editMonitorOnVisible, off: editMonitorOffVisible}] = useBoolean(false);
    // const [
    //     editMonitorConfirmLoading,
    //     {on: editMonitorOnConfirmLoading, off: editMonitorOffConfirmLoading},
    // ] = useBoolean(false);
    // const editMonitorOnOk = useCallback(
    //     async monitor => {
    //         const rdMonitor = _.cloneDeep(monitor);
    //         const [region = '', value = '{}'] = monitor?.instances ?? [];
    //         const {id = '', name = ''} = JSON.parse(value);
    //         const instances = [{region, id, name}];
    //         rdMonitor.instances = instances;

    //         try {
    //             editMonitorOnConfirmLoading();
    //             await updateMonitorApiWithCleanData({serviceMeshInstanceId, ...rdMonitor});
    //             message.success('监控指标采集编辑成功。');
    //             setServiceMeshInstance(
    //                 Object.assign({}, serviceMeshInstance, {
    //                     // 前端用的，instances必须为数组形式的值
    //                     monitor: {...monitor, instances: monitor.enabled ? instances : []},
    //                 })
    //             );
    //         } catch (error) {
    //             message.error('监控指标采集编辑失败，请重新操作。');
    //         } finally {
    //             editMonitorOffConfirmLoading();
    //             editMonitorOffVisible();
    //         }
    //     },
    //     [
    //         editMonitorOffConfirmLoading,
    //         editMonitorOffVisible,
    //         editMonitorOnConfirmLoading,
    //         serviceMeshInstance,
    //         serviceMeshInstanceId,
    //     ]
    // );

    const [viewClusterCredentialModalVisible, {
        on: viewClusterCredentialModalOn,
        off: viewClusterCredentialModalOff,
    }] = useBoolean(false);

    if (!serviceMeshInstance || pending) {
        return <PageLoading />;
    }
    const statusItem = ServiceMeshStatusDict[serviceMeshInstance.status];

    return (
        <Card
            title={
                <Title
                    viewClusterCredentialModalOn={viewClusterCredentialModalOn}
                    isHostingMesh={isHostingMesh}
                    serviceMeshInstance={serviceMeshInstance}
                />
            }
        >
            {isHostingMesh && <ViewClusterCredentialModal
                visible={viewClusterCredentialModalVisible}
                onCancel={viewClusterCredentialModalOff}
                isOpenServerEip={!!serviceMeshInstance.apiServerEip}
            />}
            <Row gutter={[48, 16]} className={c('base-info')}>
                <Col span={8}>
                    <Card.Field title="网格名称">
                        {serviceMeshInstance.OverviewOfSidecar.instanceName}
                    </Card.Field>
                </Col>
                <Col span={8}>
                    <Card.Field title="ID">
                        <Text copyable>
                            {serviceMeshInstance.OverviewOfSidecar.instanceId}
                        </Text>
                    </Card.Field>
                </Col>
                <Col span={8}>
                    <Card.Field title="实例类型">
                        {SERVICE_MESH_TYPE_DICT[serviceMeshInstance.type]}
                    </Card.Field>
                </Col>
                <Col span={8}>
                    <Card.Field title="运行状态">
                        {
                            statusItem ? <StateTag type={statusItem.value}>{statusItem.text}</StateTag> : null
                        }
                    </Card.Field>
                </Col>
                <Col span={8}>
                    <Card.Field title="支付方式">
                        {ServiceMeshBillingModeDict[serviceMeshInstance.billingModel]}
                    </Card.Field>
                </Col>
                <Col span={8}>
                    <Card.Field title="Sidecar数量">
                        {serviceMeshInstance.OverviewOfSidecar.num} 个
                    </Card.Field>
                </Col>
                <Col span={8}>
                    <Card.Field title="Istio版本">
                        {/* TODO 第一期不支持升级操作，后续支持 */}
                        <span className="margin-right-8">{serviceMeshInstance.istioVersion}</span>
                        <UpgradeIstioButton />
                    </Card.Field>
                </Col>

                <Col span={8}>
                    <Card.Field title="VPC">
                        <ExternalLink
                            href={urls.external.instanceDetail.fill({}, {vpcId: serviceMeshInstance.VpcInfo.vpcId})}
                            value={`${serviceMeshInstance.VpcInfo.vpcName}（${serviceMeshInstance.VpcInfo.vpcCidr}）`}
                        />
                    </Card.Field>
                </Col>
                {
                    isStandaloneMesh(serviceMeshInstance.type)
                    && (
                        <Col span={8}>
                            <Card.Field title="关联BLB">
                                <BlbInfo serviceMeshInstance={serviceMeshInstance} />
                            </Card.Field>
                        </Col>
                    )
                }
                {
                    isStandaloneMesh(serviceMeshInstance.type)
                    && (
                        <Col span={8}>
                            <Card.Field title="部署位置">
                                <ClusterInfo serviceMeshInstance={serviceMeshInstance} />
                            </Card.Field>
                        </Col>
                    )
                }
                {
                    isStandaloneMesh(serviceMeshInstance.type)
                    && (
                        <Col span={8}>
                            <Card.Field title="控制面地址">
                                {serviceMeshInstance.controlPanelAddress || DEFAULT_VALUE}
                            </Card.Field>
                        </Col>
                    )
                }
                <Col span={8}>
                    <EditDiscoverySelector
                        discoverySelector={serviceMeshInstance.discoverySelector}
                    />
                </Col>

                {/* {
                    isStandaloneMesh(serviceMeshInstance.type)
                    && (
                        <Col span={8}>
                            <Card.Field title="监控指标采集">
                                <Monitor
                                    serviceMeshInstance={serviceMeshInstance}
                                    editMonitorVisible={editMonitorVisible}
                                    editMonitorConfirmLoading={editMonitorConfirmLoading}
                                    editMonitorOnVisible={editMonitorOnVisible}
                                    editMonitorOffVisible={editMonitorOffVisible}
                                    editMonitorOnOk={editMonitorOnOk}
                                />
                            </Card.Field>
                        </Col>
                    )
                } */}

                {
                    isShowMultiProtocol(serviceMeshInstance.type, serviceMeshInstance.istioVersion)
                    && (
                        <Col span={8}>
                            <Card.Field title="支持兼容三方协议">
                                {multiProtocolEnabledMap.get(serviceMeshInstance.multiProtocol)}
                            </Card.Field>
                        </Col>
                    )
                }
                {
                    isHostingMesh
                    && (
                        <>
                            <Col span={8}>
                                <Card.Field title="Istio资源配置">
                                    {serviceMeshInstance.configCluster === 'EXTERNAL' ? '控制面托管集群' : '数据面集群'}
                                </Card.Field>
                            </Col>

                            {
                                configCluster === 'EXTERNAL'
                                && (
                                    <Col span={8}>
                                        <Card.Field title="API Server访问">
                                            {serviceMeshInstance.apiServerEip ? '开启' : '关闭'}
                                        </Card.Field>
                                    </Col>
                                )
                            }
                        </>
                    )
                }
            </Row>
        </Card>
    );
}
