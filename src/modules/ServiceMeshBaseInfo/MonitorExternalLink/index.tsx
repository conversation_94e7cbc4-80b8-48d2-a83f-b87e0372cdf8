import styled from 'styled-components';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {useFramework} from '@/hooks';
import {IInstanceItem, IMonitorItem} from '@/api/createServiceMesh';
import {getMonitorInstanceLink} from '../util';

const ExternalLinkStyled = styled(ExternalLink)`
    padding-left: 12px !important;
`;

interface IProps {
    monitor: IMonitorItem;
}
export default function MonitorExternalLink({monitor}: IProps) {
    const {region: {regionList}} = useFramework();

    if (!monitor.enabled) {
        return null;
    }

    return (
        <ExternalLinkStyled
            href={
                urls.external.cpromInstanceDetail.fill(
                    {},
                    {id: (monitor?.instances?.[0] as IInstanceItem)?.id ?? ''}
                )
            }
            value={
                getMonitorInstanceLink(monitor, regionList)
            }
        />
    );
}
