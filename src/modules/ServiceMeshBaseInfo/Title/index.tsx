import {Button} from '@osui/ui';
import {useMemo} from 'react';
import {ServiceMeshInstance} from '@/interface/serviceMesh';
import c from './index.less';

interface IProps {
    viewClusterCredentialModalOn: () => void;
    isHostingMesh: boolean;
    serviceMeshInstance: ServiceMeshInstance;
}

export default function Title({viewClusterCredentialModalOn, isHostingMesh, serviceMeshInstance}: IProps) {
    const configCluster = useMemo(
        () => serviceMeshInstance?.configCluster || 'EXTERNAL',
        [serviceMeshInstance?.configCluster]
    );

    return (
        <>
            基本信息
            {/* 托管网格支持查看网格凭证 */}
            {
                isHostingMesh
                && (
                    <Button
                        className={c['view-credential-btn']}
                        onClick={viewClusterCredentialModalOn}
                        disabled={configCluster === 'REMOTE'}
                        disabledReason={configCluster === 'REMOTE' ? '该托管网格Istio资源配置在纳管数据面集群中，网格凭证不可用' : ''}
                    >
                        查看网格凭证
                    </Button>
                )
            }
        </>
    );
}
