import {OutlinedEditingSquare} from '@baidu/acud-icon';
import styled from 'styled-components';
import {Button} from '@osui/ui';
import EditMonitor from '@/components/EditMonitor';
import {monitorEnabledMap} from '@/dicts/serviceMesh';
import {ServiceMeshInstance} from '@/interface/serviceMesh';
import MonitorExternalLink from '../MonitorExternalLink';

interface IProps {
    serviceMeshInstance: ServiceMeshInstance;
    editMonitorVisible: boolean;
    editMonitorConfirmLoading: boolean;
    editMonitorOnVisible: () => void;
    editMonitorOffVisible: () => void;
    editMonitorOnOk: (monitor: any) => Promise<void>;
}

const EditButtonStyled = styled(Button)`
    margin-left: 12px !important;
`;
export default function Monitor({
    serviceMeshInstance,
    editMonitorVisible,
    editMonitorConfirmLoading,
    editMonitorOnVisible,
    editMonitorOffVisible,
    editMonitorOnOk,
}: IProps) {
    return (
        <>
            {
                monitorEnabledMap.get(serviceMeshInstance.monitor.enabled)
            }
            <MonitorExternalLink monitor={serviceMeshInstance.monitor} />

            <EditButtonStyled type="link" onClick={editMonitorOnVisible}>
                <OutlinedEditingSquare />
            </EditButtonStyled>

            <EditMonitor
                visible={editMonitorVisible}
                monitor={serviceMeshInstance.monitor}
                confirmLoading={editMonitorConfirmLoading}
                onCancel={editMonitorOffVisible}
                onOk={editMonitorOnOk}
            />
        </>
    );
}
