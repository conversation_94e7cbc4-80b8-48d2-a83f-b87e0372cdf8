import React, {useEffect} from 'react';
import styled from 'styled-components';
import {Menu} from '@osui/ui';
import {Link, Route, Switch, useHistory, useLocation} from 'react-router-dom';
import ProductDocLink from '@/components/ProductDocLink';
import * as Layout from '@/components/Layout';
import urls from '@/urls';
import {useNewUser} from '@/hooks/useNewUser';
import {LayoutHeaderWrap} from '@/components/HeaderWrap';
import {OVERVIEW_GUIDE_LINK, OVERVIEW_DASHBOARD_LINK} from '@/links';
import Guide from '../Guide';
import Dashboard from '../Dashboard';
import CancelPublicBetaNotice from './CancelPublicBetaNotice';

const OverviewHeaderWrap = styled(LayoutHeaderWrap)`
    .ant-page-header.osui-page-header {
        padding-top: 12px;
        padding-bottom: 4px;
    }
`;

export default function Overview() {
    const {pathname} = useLocation();
    const history = useHistory();
    const isNewUser = useNewUser();
    useEffect(
        () => {
            if (pathname === urls.overview.base.fill()) {
                history.replace(isNewUser ? OVERVIEW_GUIDE_LINK : OVERVIEW_DASHBOARD_LINK);
            }
        },
        [history, isNewUser, pathname]
    );
    return (
        <div>
            <OverviewHeaderWrap>
                <Layout.Header
                    title="全局概览"
                    subTitle={<CancelPublicBetaNotice />}
                    extra={
                        <ProductDocLink />
                    }
                />
            </OverviewHeaderWrap>
            <Menu mode="horizontal" selectedKeys={[pathname]}>
                <Menu.Item key={OVERVIEW_GUIDE_LINK}>
                    <Link to={OVERVIEW_GUIDE_LINK}>指引</Link>
                </Menu.Item>
                <Menu.Item key={OVERVIEW_DASHBOARD_LINK}>
                    <Link to={OVERVIEW_DASHBOARD_LINK}>资源仪表盘</Link>
                </Menu.Item>
            </Menu>
            <Switch>
                <Route path={OVERVIEW_GUIDE_LINK} component={Guide} />
                <Route path={OVERVIEW_DASHBOARD_LINK} component={Dashboard} />
            </Switch>
        </div>
    );
}
