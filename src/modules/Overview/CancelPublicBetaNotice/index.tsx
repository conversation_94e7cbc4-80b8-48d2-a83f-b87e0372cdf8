import {OutlinedNotice, OutlinedClose} from '@baidu/acud-icon';
import {useCallback, useState} from 'react';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {getCancelPublicBetaNoticeStatus, setCancelPublicBetaNoticeStatus} from './util';
import c from './index.less';

export default function CancelPublicBetaNotice() {
    const [noticeStatus, setNnoticeStatus] = useState(getCancelPublicBetaNoticeStatus());

    const clickCloseIcon = useCallback(
        () => {
            setNnoticeStatus('fasle');
            setCancelPublicBetaNoticeStatus('false');
        },
        []
    );

    return (
        <>
            {
                noticeStatus === 'true'
                && (
                    <div className={c['cancel-public-beta-notice']}>
                        <OutlinedNotice fill="#2468f2" />
                        <ExternalLink
                            className={c['link']}
                            href={urls.external.csmDocCancelBeta.fill()}
                            value="CSM结束公测公告，点击了解"
                        />
                        <OutlinedClose className={c['close-icon']} fill="#2468f2" onClick={clickCloseIcon} />
                    </div>
                )
            }
        </>
    );
}
