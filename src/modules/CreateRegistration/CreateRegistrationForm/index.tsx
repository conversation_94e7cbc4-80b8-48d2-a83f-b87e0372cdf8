/* eslint-disable */
import {useCallback, useEffect, useState, useMemo} from 'react';
import {useHistory} from 'react-router-dom';
import {Form, Input, Popover, Switch, Select} from '@osui/ui';
import {QuestionCircleOutlined} from '@ant-design/icons';
import _ from 'lodash';
import styled from 'styled-components';
import {useBoolean, useRequestCallback} from 'huse';
import {useFramework} from '@/hooks';
import NetworkType from '@/modules/CreateServiceMesh/CreateServiceMeshForm/NetworkType';
import {
    getPrometheusList,
    getPrometheusTokenList,
    createRegistrationInstance,
    getSubNetIpNumber,
    getEsgInstanceListApi,
} from '@/api/registration';
import PageFooter from '@/components/PageFooter';
import {REGISTRATION_LIST} from '@/links';

import {useFormInitData} from '../hook/useFormInitData';
import {
    nameValidate,
    cpromInstanceValidate,
    cpromTokenValidate,
} from './formValidate';
import c from './index.less';

const SubnetSelectStyled = styled(Select)`
    width: 240px !important;
`;


export default function CreateRegistrationForm() {
    const history = useHistory();
    const {region: {currentRegion, regionList}} = useFramework();
    const [cpromList, setCpromList] = useState<any[]>([]);
    const [cpromInstanceId, setCpromInstanceId] = useState('');
    const [subNetId, setSubNetId] = useState('');
    const [ipNumber, setIpNumber] = useState(0);
    const [tokenList, setTokenList] = useState<any[]>([]);
    const [isUserChangeRegion, {off: offUserChangeRegion}] = useBoolean(true);

    const {
        formDataInstante,
    } = useFormInitData();

    const [requestCpromList, {
        data: _cpromList,
    }] = useRequestCallback(getPrometheusList, {
        pageNo: 1,
        pageSize: 1000,
    });

    const [requestToken, {
        data: _tokenList,
    }] = useRequestCallback(getPrometheusTokenList, {
        cpromInstanceId,
    });

    const [requestIp, {
        data: ipData,
    }] = useRequestCallback(getSubNetIpNumber, {
        subnetId: subNetId,
    });

    const [getEsgInstanceList,
        {pending: esgLoading, data: _esgInstanceList}] = useRequestCallback(getEsgInstanceListApi, {
        pageNo: 1,
        pageSize: 1000,
    });

    useEffect(
        () => {
            getEsgInstanceList();
        },
        [getEsgInstanceList, currentRegion]
    );

    useEffect(
        () => {
            if (cpromInstanceId) {
                requestToken();
            }
        },
        [requestToken, cpromInstanceId]
    );

    useEffect(
        () => {
            if (subNetId) {
                requestIp();
            }
        },
        [requestIp, subNetId]
    );

    useEffect(
        () => {
            const subNetTotalData: any = _.get(ipData, 'subnets[0]', {});
            if (!_.isEmpty(subNetTotalData)) {
                const num = subNetTotalData.totalIps - subNetTotalData.usedIps;
                setIpNumber(num);
            }
        },
        [ipData]
    );

    useEffect(
        () => {
            const list = (_tokenList || []).map((d: any) => {
                return {
                    label: d.token,
                    value: d.token,
                };
            });
            setTokenList(list);
        },
        [_tokenList]
    );

    useEffect(
        () => {
            const list = _.get(_cpromList, 'items', []).map((d: any) => {
                return {
                    label: d?.spec?.instanceName,
                    value: d.spec.instanceID,
                };
            });
            setCpromList(list);
        },
        [_cpromList]
    );


    const esgOptions = useMemo(
        () => {
            return _esgInstanceList?.result.map(({esgId, name}: {name: string, esgId: string}) => (
                {
                    label: `${name}(${esgId})`,
                    value: esgId,
                }
            )) ?? [];
        },
        [_esgInstanceList]
    );

    useEffect(
        () => {
            formDataInstante.setFieldsValue({
                monitorToken: '',
                monitorInstanceId: '',
                networkType: {vpcNetworkId: undefined, subnetId: undefined},
            });
            requestCpromList();
        },
        [currentRegion, requestCpromList, formDataInstante]
    );

    const onValuesChange = useCallback(
        // eslint-disable-next-line complexity
        changedValues => {
            const {monitorInstanceId, networkType} = changedValues;
            if (monitorInstanceId) {
                formDataInstante.setFieldsValue({
                    monitorToken: '',
                });
                setCpromInstanceId(monitorInstanceId);
            }
            if (networkType?.subnetId) {
                setSubNetId(networkType?.subnetId);
            }
        },
        [formDataInstante]
    );

    const onCancel = useCallback(
        () => {
            history.push(REGISTRATION_LIST);
        },
        [history]
    );

    const onSubmit = useCallback( // 表单提交事件
        async () => {
            try {
                // 验证表单 并拿到表单中的值
                const {
                    name,
                    networkType,
                    monitorEnable,
                    monitorInstanceId,
                    monitorToken,
                    esgId,
                } = await formDataInstante.validateFields(); // 验证表单 并拿到表单中的值
                const {subnetId, vpcNetworkId} = networkType;
                const param: any = {
                    name,
                    region: currentRegion,
                    networkConfig: {
                        vpcId: vpcNetworkId,
                        subnetId: subnetId,
                    },
                    monitorEnable: monitorEnable || false,
                };
                if (monitorEnable) {
                    param.monitorInstanceId = monitorInstanceId;
                    param.monitorToken = monitorToken;
                }
                if (esgId) {
                    param.enterpriseSecurityGroupId = esgId;
                }
                const res = await createRegistrationInstance(param);
                if (res) {
                    onCancel();
                }
            } catch (err) {
                console.error(err);
            }
        },
        [formDataInstante, currentRegion, onCancel]
    );

    Form.useLabelLayout('basic', 0);

    return (
        <div className={c.createMeshForm}>
            <div className={c.createMeshFormWrap}>
                <Form
                    name="basic"
                    labelAlign="left"
                    form={formDataInstante}
                    onValuesChange={onValuesChange}
                    autoComplete="off"
                >
                    <Form.Item label="支持协议" className="mesh-protocol">
                        <span>Nacos、</span>
                        <span>Eureka</span>
                    </Form.Item>
                    <Form.Item
                        label="实例名称"
                        help="最长60个字符，支持中英文大小写，-，_"
                        rules={nameValidate}
                        name="name"
                    >
                        <Input
                            className="mesh-name"
                            maxLength={60}
                            showCount
                            placeholder="请输入实例名称"
                        />
                    </Form.Item>
                    <Form.Item label="当前地域">
                        <div>{regionList[currentRegion]}</div>
                    </Form.Item>
                    <Form.Item required label="网络类型" help={ipNumber ? `当前子网可用IP共${ipNumber}个` : ''}>
                        <NetworkType
                            isUserChangeRegion={isUserChangeRegion}
                            offUserChangeRegion={offUserChangeRegion}
                            className={c.createRegistrationNet}
                        />
                    </Form.Item>
                    <Form.Item
                        name="esgId"
                        label={
                            <>
                                访问策略：{
                                    <Popover content={'选择需要绑定的企业安全组'}>
                                        <QuestionCircleOutlined />
                                    </Popover>
                                }
                            </>
                        }
                        colon={false}
                    >
                        <Select
                            disabled={esgLoading}
                            loading={esgLoading}
                            allowClear
                            className={c.selectEsg}
                            options={esgOptions}
                            placeholder="请选择"
                        />
                    </Form.Item>
                    <Form.Item
                        label="监控指标采集"
                        name="monitorEnable"
                        help={
                            <>开启Prometheus监控，<a href="https://cloud.baidu.com/doc/CProm/s/Gl0gcsh46" target="_blank" rel="noreferrer">查看费用详情 </a></>
                        }
                    >
                        <Switch />
                    </Form.Item>
                    <Form.Item
                        shouldUpdate
                        noStyle
                        className={c.cpromInstanceWrap}
                    >
                        {
                            ({getFieldValue}) => {
                                const monitorEnable = getFieldValue('monitorEnable');
                                if (monitorEnable) {
                                    return (
                                        <>
                                            <div className={c.cpromInstanceWrap}>
                                                <Form.Item
                                                    label="选择监控实例"
                                                    name="monitorInstanceId"
                                                    rules={cpromInstanceValidate}
                                                    help={
                                                        <>没有合适的监控实例，可以 <a href="https://console.bce.baidu.com/cprom/#/instance/list" target="_blank" rel="noreferrer">去创建实例</a></>
                                                    }
                                                >
                                                    <SubnetSelectStyled
                                                        placeholder="请选择监控实例"
                                                        options={cpromList}
                                                    />
                                                </Form.Item>
                                            </div>
                                            <div className={c.cpromInstanceWrap}>
                                                <Form.Item
                                                    label="监控实例Token"
                                                    name="monitorToken"
                                                    rules={cpromTokenValidate}
                                                    help={
                                                        <>若没有Token，点击 <a href="https://console.bce.baidu.com/cprom/#/instance/list" target="_blank" rel="noreferrer">已选监控实例 </a>，查看实例信息可生成Token</>
                                                    }
                                                >
                                                    <SubnetSelectStyled
                                                        placeholder="请选择监控实例Token"
                                                        options={tokenList}
                                                    />
                                                </Form.Item>
                                            </div>
                                        </>
                                    );
                                }
                            }
                        }
                    </Form.Item>
                    <Form.Item
                        shouldUpdate
                        noStyle
                    >
                        <PageFooter
                            onConfirm={onSubmit}
                            onCancel={onCancel}
                            confirmButton={{disabled: false}}
                        />
                    </Form.Item>
                </Form>
            </div>
        </div>
    );
}
