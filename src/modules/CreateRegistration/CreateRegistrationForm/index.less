.create-mesh-form {
    margin: 16px;
    background-color: #fff;
    border-radius: 6px;
}

.create-mesh-form-wrap {
    padding: 24px;

    :global {
        .mesh-name {
            width: 400px;
        }

        .mesh-protocol {
            span {
                margin-right: 8px;
            }
        }

        .select-cluster {
            width: 400px;
        }

        .ant-form-item {
            margin-bottom: 24px;
        }

        .ant-form-item-explain {
            margin-top: 4px;
            line-height: 20px;
        }
    }
}

.create-registration-net {
    display: flex;

    :global {
        .ant-form-item {
            margin-right: 16px;
            margin-bottom: 0;
        }
    }
}

.cprom-instance-wrap {
    margin-top: 49px;
}

.selectEsg {
    width: 240px !important;
}
