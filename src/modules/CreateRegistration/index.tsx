import React, {useCallback} from 'react';
import {useHistory} from 'react-router-dom';
import {Header} from '@/components/Layout';
import urls from '@/urls';
import CreateRegistrationForm from './CreateRegistrationForm';

function CreateRegistrationHeader() {
    const history = useHistory();
    const toBack = useCallback(
        () => {
            history.push(urls.registrationList.fill());
        },
        [history]
    );

    return (
        <Header
            backIcon
            onBack={toBack}
            title="创建注册中心实例"
        />
    );
}

export default function CreateRegistration() {
    return (
        <>
            <CreateRegistrationHeader />
            <CreateRegistrationForm />
        </>
    );
}
