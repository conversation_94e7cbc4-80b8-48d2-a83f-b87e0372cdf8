import React from 'react';
import styled from 'styled-components';
/**
 * @warning 千万别用framework.widgets.marketing.inject这个api,AI写的都比他好,真服了
 */
const Iframe = styled.iframe`
    width:100%;
    height:100vh;
    border:0;
    min-height:600px;
`;
export default function Guide() {
    const isOnline = location.host.includes('bce.baidu.com');
    const host = isOnline ? 'console.bce.baidu.com' : 'qasandbox.bcetest.baidu.com';
    const URL_PREFIX = `https://${host}/console/marketing.html`;
    const path = `${URL_PREFIX}?serviceType=csm&_=${new Date().getTime()}`;
    return (
        <Iframe src={path} />
    );
}
