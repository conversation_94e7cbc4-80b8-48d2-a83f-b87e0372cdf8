declare const $features: Record<string, any>;
declare const $build: { version: string, time: string, target: string };
interface FrameworkEvents {
    ACTION_NOT_FOUND: string;
    FORWARD_ACTION: string;
    ENTER_ACTION_COMPLETE: string;
    LEAVE_ACTION: string;

    SERVICES_CHANGED: string;
    REGION_CHANGED: string;
    AFTER_REGION_CHANGED: string;
    HIDE_REGION_SWITCHER: string;
    SET_REGION_RESOURCE_NUM: string;
}

type EventHandler = (event: string, callback: (...args: any[]) => void, self?: any) => void;
interface Window {
    FRAMEWORK_DATA: {
        currentRegion: string,
    }
    framework: {
        events: {
            on: EventHandler,
            un: EventHandler,
            fire: (event: string, { }: { path: string[] }) => void,
        }
        EVENTS: FrameworkEvents
        boot: () => Promise<FrameworkData>
        region: {
            setRegion: (region: string) => any
        }
    }
}