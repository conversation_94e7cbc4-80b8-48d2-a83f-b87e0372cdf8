import {CrdType} from '@/dicts/crd';

export interface Crd {
    name: string;
    namespace: string;
    kind: CrdType;
    updatedAt: number;
    content: string;
}

export interface QueryResponseBase<T>{
    result: T;
    totalCount: number;
    pageSize: number;
    pageNo: number;
}
export interface QueryRequestParamsBase{
    pageSize?: number;
    pageNo?: number;
    keyword?: string;
    keywordType?: string;
    order?: string;
    orderBy?: string;
}

// ISO时间类型 目前没办法区分出来字符串还是别的
// 格式为'2021-07-13T09:38:49+00:00'
export type IsoTime = string;

// 时间戳类型
export type Timestamp = number;

export type StageTagType = 'success' | 'pending' | 'error' | 'warning' | 'info';
