import {IDiscoverySelector, IInstanceItem, TConfigCluster} from '@/api/createServiceMesh';
import {Region} from '@/dicts';
import {BillingMode, BlbStatusType, ServiceMeshStatus, ServiceMeshType} from '@/dicts/serviceMesh';

export type IstioVersion = string;
export type ServiceMeshInstanceId = string;
export interface ServiceMesh {
    instanceId: ServiceMeshInstanceId;
    istioVersion: string;
    type: ServiceMeshType;
    instanceName: string;
    installationClusterId: number; // @todo 关联cluster id
    region: Region;
}
export interface ServiceMeshInstance {
    type: ServiceMeshType;
    status: ServiceMeshStatus;
    istioVersion: IstioVersion;
    controlPanelAddress: string;
    billingModel: BillingMode;
    // 托管网格才有 networkType
    networkType?: {
        vpcNetworkId: string;
        vpcNetworkName: string;
        vpcNetworkCidr: string;
        subnetId: string;
        subnetName: string;
        subnetCidr: string;
    };
    VpcInfo: {
        vpcId: string;
        vpcName: string;
        vpcCidr: string;
    };
    OverviewOfSidecar: {
        instanceId: ServiceMeshInstanceId;
        instanceName: string;
        num: number;
    };
    ClusterInfo: {
        clusterId: string;
        clusterName: string;
    };
    BlbInfo: {
        blbId: string;
        blbName: string;
        blbStatus: BlbStatusType;
    };
    discoverySelector: IDiscoverySelector;
    monitor: {
        enabled: boolean;
        instances: IInstanceItem[];
    };
    multiProtocol: boolean;
    configCluster?: TConfigCluster;
    apiServerEip?: boolean;
}
