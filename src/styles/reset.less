// 用途：组件样式的重置

.osui-tabs.ant-tabs-top > .ant-tabs-nav {
    &::before,
    .ant-tabs-nav-list {
        margin-left: 24px;
    }
}

// ant-menu
.ant-menu .ant-menu-item {
    margin: 8px 0;
}

.ant-menu-inline {
    border-right: none;
}

.ant-menu-inline .ant-menu-selected::after,
.ant-menu-inline .ant-menu-item-selected::after {
    transform: scaleY(0);
    opacity: 0;
}

// 开通页组件
.icloud-ui-activation-page-container {
    @media screen and (min-width: 1920px) {
        width: 1708px;
        margin-inline: auto;
    }
}

.icloud-ui-form-footer {
    padding-left: 16px !important;
}

// osui的分页器
.osui-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
}

.ant-page-header {
    padding: 8px 16px !important;
}

// form 组件
.ant-form {
    .ant-form-item-label-left {
        margin: 0;
    }

    .ant-form-item-control {
        margin-left: 0 !important;

        .ant-form-item-explain {
            margin-top: 4px;
            color: var(--GRAY_5);
        }
    }
}

.icloud-ui-card {
    .ant-row {
        margin-right: 0 !important;
    }
}

// 弹窗（Modal组件，一般是二次确认用）
.ant-modal-root {
    .ant-modal-body {
        .ant-modal-confirm-title {
            font-family: var(--font-family-PingFangScMedium);
            font-size: var(--font-size-large);
        }
    }
}

// 刷新icon的样式
.acuicon-outlined-refresh {
    :hover {
        cursor: pointer;
    }
}

// ? icon的样式
.anticon-question-circle {
    &:hover {
        color: var(--color-brand-6);
    }
}

.acuicon-outlined-question-circle {
    &:hover {
        color: var(--color-brand-6);
    }
}

.ant-popover-inner-content {
    max-width: 240px;
}

.ant-modal-content {
    .ant-modal-title {
        font-family: var(--font-family-PingFangScMedium);
        font-size: var(--font-size-large);
        color: var(--GRAY_2);
        line-height: 24px;
        font-weight: 500;
    }
}

.ant-table {
    // 指定列宽不生效。详见：https://github.com/ant-design/ant-design/issues/13825
    .ant-table-fixed {
        table-layout: fixed;
    }

    .ant-table-tbody > tr > td {
        word-wrap: break-word;
        word-break: break-all;
    }

    .ant-table-tbody > .ant-table-placeholder > td {
        border-bottom: none;
    }

    div.ant-typography {
        margin-bottom: 0;
    }
}

.ant-menu-sub.ant-menu-inline {
    background-color: unset;
}

.ant-menu-sub .ant-menu-item:hover {
    background-color: unset !important;
}

.ant-menu.ant-menu-inline {
    border-right: none;

    .ant-menu-item-only-child,
    .ant-menu-submenu-title {
        padding-left: 16px !important;
    }

    .ant-menu-item-only-child {
        .ant-menu-title-content {
            a {
                font-size: 14px;
                line-height: 22px;
            }
        }
    }

    .ant-menu-submenu {
        .ant-menu-item-only-child {
            padding-left: 28px !important;
        }

        .ant-menu-submenu-expand-icon {
            color: var(--GRAY_2);
        }

        :hover {
            .ant-menu-submenu-expand-icon {
                color: var(--primary-color);
            }
        }
    }

    .ant-menu-item-selected {
        position: relative;
        background-color: #e6f0ff !important;

        &::before {
            position: absolute;
            content: '';
            display: block;
            width: 2px;
            top: 0;
            bottom: 0;
            left: 0;
            background-color: var(--color-brand-6);
        }
    }

    .ant-menu-submenu-selected {
        .ant-menu-submenu-expand-icon {
            color: var(--primary-color);
        }
    }

    .ant-menu-submenu-open {
        .ant-menu-submenu-expand-icon {
            transform: rotate(-90deg);
        }
    }

    .ant-menu-submenu-expand-icon {
        width: 16px;
        transform: rotate(90deg);
        top: auto;
    }

    a,
    span {
        font-size: 14px;
    }
}

.ant-menu.ant-menu-vertical.secondary-menu {
    width: 160px !important;
    border-right: none;

    .ant-menu-item-selected {
        position: relative;

        &::before {
            display: none;
        }
    }

    a {
        font-size: 14px;
    }
}

.ant-page-header.osui-page-header {
    .ant-page-header-heading {
        display: flex;
        align-items: center;
    }

    .ant-page-header-heading > span,
    .ant-page-header-heading > div {
        margin: 0;
    }

    .ant-page-header-heading-extra {
        display: flex;
        align-items: center;
        height: 24px;
    }
}

.icloud-ui-pro-table {
    padding: initial;
    border-radius: initial;
    background-color: initial;

    .icloud-ui-pro-table-toolbar {
        margin-bottom: 16px;
    }
}
// 兼容表格的筛选在共有云sdk下的bug
input[type=radio] {
    position: absolute;
}
