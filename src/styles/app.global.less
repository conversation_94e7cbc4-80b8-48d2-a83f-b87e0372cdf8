@import '~@osui/icloud-theme/dist/theme/vars.css';
@import '~@osui/icloud-theme/dist/antd4-styles-patch.css';
@import './layout.less';
@import './icon.less';
@import './reset.less';
@import 'acud/dist/acud.min.css';

* {
    box-sizing: border-box;
}

:root {
    --font-size-small: 12px;
    --font-size-medium: 14px;
    --font-size-large: 16px;
    --font-size-extra-large: 18px;
    --font-family-PingFangScMedium: 'PingFangSC-Medium';
    --font-family-PingFangScRegular: 'PingFangSC-Regular';
    --GRAY_2: #151b26;
    --GRAY_4: #5c5f66;
    --GRAY_5: #84868c;
    --GRAY_6: #b8babf;
    --GRAY_7: #999;
    --GRAY_8: #e8e9eb;
    --GRAY_10: #f7f7f9;
    --GRAY_11: #fff;
    --RED: #f33d3d;
    --RED_1: #f33e3e;
}

html,
body {
    margin: 0;
    min-width: initial;
}

body #bce-content #main {
    background-color: var(--GRAY_10);
}
