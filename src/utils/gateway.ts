import _ from 'lodash';
import {IGatewayBasicConfig, IGatewayNetworkConfig} from '@/api/gateway';
import {ServiceMeshInstance} from '@/interface/serviceMesh';
import {getSubmittedMonitor} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/util';

export const getBasicConfigForRequest = (basicConfig: IGatewayBasicConfig): IGatewayBasicConfig => {
    const basicConfigForRequest = _.cloneDeep(basicConfig);
    basicConfigForRequest.resourceQuota = (basicConfigForRequest.resourceQuota as unknown as string[])[0];
    basicConfigForRequest.monitor = getSubmittedMonitor(
        basicConfig.monitor as unknown as { enabled: boolean, instances: string[] }
    );

    return basicConfigForRequest;
};

export const getNetworkConfigForRequest = (
    networkConfig: IGatewayNetworkConfig,
    serviceMeshInstance: ServiceMeshInstance | undefined
): IGatewayNetworkConfig => {
    const networkConfigForRequest = _.cloneDeep(networkConfig);
    delete networkConfigForRequest.elasticPublicNetwork.id;

    // 拼上 networkType 字段
    if (serviceMeshInstance && serviceMeshInstance.networkType) {
        const {networkType} = serviceMeshInstance;
        networkConfigForRequest.networkType = networkType;
    }

    return networkConfigForRequest;
};
