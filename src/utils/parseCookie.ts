/**
 * 解析Cookie
 *
 * @inner
 * @param {string} name cookie键名
 * @param {boolean} needDecode 是否自动解码
 * @return {?string} 获取的cookie值，获取不到时返回null
 */
export function parseCookie(name, needDecode) {
    if (typeof name === 'string' && name !== '') {
        let matches = new RegExp('(?:^|)' + name + '(?:(?:=([^;]*))|;|$)').exec(String(document.cookie));

        if (matches) {
            if ((matches = matches[1])) {
                return needDecode ? decodeURIComponent(matches) : matches;
            }

            return '';
        }
    }

    return null;
}
