/**
 * @param {*} num
 * @returns string
 * @description 将数字转换为千分位
 */
export function toThousands(num: number, fixed?: number, check = true) {
    let text: string|number = '';
    if (check) {
        text = num ? parseFloat(String(num).replace(/,/g, '')) : 0.00;
        if (isNaN(text)) {
            throw new Error(`数据异常:${num}`);
        }
        text = Number(fixed) > 0
            ? text.toFixed(Number(fixed))
            : (Number(fixed) === 0) ? Math.round(text) : text.toString();
    } else {
        text = String(num);
    }
    if (typeof text === 'string') {
        return text.replace(text.includes('.')
            ? /(\d)(?=(\d{3})+\.)/g : /(\d)(?=(\d{3})+$)/g, ($0, $1) => {
            return `${$1},`;
        });
    } else {
        return text;
    }

}
