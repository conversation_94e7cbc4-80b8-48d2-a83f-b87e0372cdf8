import {addClusterList} from '@/api/cluster';
import {deleteClusterListApi} from '@/api/clusterMesh';

// region 和 clusterId字段通过 # 字符进行连接
export const CLUSTER_UNIQUE_JOIN_CHAR = '#';
export const clusterRowKey = ({clusterId, region, clusterName}: any) =>
    `${region}${CLUSTER_UNIQUE_JOIN_CHAR}${clusterId}${CLUSTER_UNIQUE_JOIN_CHAR}${clusterName}`;

export const clusterWidthRegion =
    (selectedRowKeys: string[]): Array<{region: string, clusterId: string, clusterName: string}> => {
        return selectedRowKeys.map(v => {
            const [region = '', clusterId = '', clusterName = ''] = v.split(CLUSTER_UNIQUE_JOIN_CHAR);
            return {region, clusterId, clusterName};
        });
    };

// 集群管理（如批量添加、移除等）
type TManageCluster = 'add' | 'remove';
export const addOrRemoveClusterByBatch = async (
    serviceMeshInstanceId: string,
    selectedRowKeys: string[],
    type: TManageCluster
) => {
    const clusterList = clusterWidthRegion(selectedRowKeys);

    if (type === 'add') {
        await addClusterList({serviceMeshInstanceId, clusters: clusterList});
    }
    else if (type === 'remove') {
        await deleteClusterListApi({serviceMeshInstanceId, clusters: clusterList});
    }
};
