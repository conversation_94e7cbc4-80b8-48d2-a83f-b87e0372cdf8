
export const getRowSpan = <T>(datasource: T[], index: number, record: T, fieldName: keyof T) => {
    if (index > 0 && index < datasource.length) {
        if (record[fieldName] === datasource[index - 1][fieldName]) {
            return 0;
        }
    }

    return datasource.slice(index)
        .reduce((acc, cur) => {
            if (acc.flag && cur[fieldName] === record[fieldName]) {
                acc.count += 1;
            }
            return acc;
        }, {flag: true, count: 0})
        .count;
};
