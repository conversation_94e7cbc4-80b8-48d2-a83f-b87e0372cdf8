import _ from 'lodash';
import moment, {MomentInput} from 'moment';

export function dateFormat(data: MomentInput) {
    return moment(data).format('YYYY-MM-DD HH:mm:ss');
}

export function getTextFromValue(datasource: Array<{text: string, value: string}>, value: string) {
    return datasource?.filter(v => v.value.toUpperCase() === value.toUpperCase())[0]?.text || '';
}

export const formatMultiValueQuery = (query: object, multiValuePropList: string[]) => {
    const newQuery = _.cloneDeep(query);
    multiValuePropList.forEach(prop => {
        // TODO 有 warning
        if (query[prop] && Array.isArray(query[prop])) {
            newQuery[prop] = newQuery[prop].join(',');
        }
        else {
            delete newQuery[prop];
        }
    });
    return newQuery;
};

/**
 * 复制文本到剪贴板
 *
 * @param text 要复制的文本
 * @returns 无返回值
 */
export const copyText = (text: string) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text);
    } else {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
        document.body.removeChild(textArea);
    }
};

/**
 * 下载文本
 *
 * @param text 要下载的文本
 * @param name 下载的文本名称
 * @returns 无返回值
 */
export const downloadText = (text: string, name: string) => {
    try {
        const textFileAsBlob = new Blob([text], {type: 'text/plain'});
        const downloadLink = document.createElement('a');
        downloadLink.download = name;
        downloadLink.href = window.URL.createObjectURL(textFileAsBlob);
        downloadLink.click();
    } catch (error) {
        console.error(error);
    }
};
