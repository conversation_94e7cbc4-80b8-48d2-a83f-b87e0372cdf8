import {createFactory} from 'axios-interface';
import cookie from 'js-cookie';
import {message} from '@osui/ui';
import {REGION_GLOBAL} from '@/dicts';
import {parseCookie} from './parseCookie';

// 接口工厂
const {createInterface} = createFactory({
    onPending: (params, options) => {
        const csrftoken = parseCookie('bce-user-info', true);
        const _config = params?._config || options?.data?._config;
        const _configRegion = _config?.region || '';
        let XRegion = _config?.region || window.FRAMEWORK_DATA.currentRegion;

        // 边界：query里传的region为 global 时，XRegion需替换成 bj （因云桥不支持 global ）。
        if (_configRegion === REGION_GLOBAL) {
            XRegion = 'bj';
        }

        const config = {
            headers: {
                csrftoken: csrftoken ? csrftoken.replace(/"/g, '') : null,
                'X-Requested-By': 'ERApplication',
                'X-Region': XRegion,
            },
            ...options,
            params: {
                locale: cookie.get('bce-locale') || 'zh-cn',
                _: (new Date()).getTime(),
                ...options.params,
            },
        };

        // _configRegion有值时，需往 query 里传
        if (_configRegion) {
            config.params.region = _configRegion;
        }
        if (config.params?._config) {
            delete config.params._config;
        }
        if (config.data?._config) {
            delete config.data._config;
        }
        return config;
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onResolve: (result, params, options) => {
        // @todo 错误应该调用方处理
        if (result.data.success === false) {
            message.error(
                {
                    content: (
                        // eslint-disable-next-line react/no-danger
                        <span dangerouslySetInnerHTML={{
                            __html: result.data?.message?.global || '请求失败，请重试',
                        }}
                        >
                        </span>
                    ),
                },
                0
            );
            throw new Error(result.data?.message?.global || '请求失败，请重试');
        }
        return result.data.result;
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onReject: (result, params, options) => {
        // eslint-disable-next-line no-console
        console.log('reject', result);
        message.error(result?.message || '请求失败，请重试', 0);
        return result;
    },
    transformDeleteParamsIntoBody: true,
});

const createFactoryBlb = createFactory({
    onPending: (params, options) => {
        const csrftoken = parseCookie('bce-user-info', true);
        const _config = params?._config || options?.data?._config;
        const config = {
            headers: {
                csrftoken: csrftoken ? csrftoken.replace(/"/g, '') : null,
                'X-Requested-By': 'ERApplication',
                'X-Region': _config?.region || window.FRAMEWORK_DATA.currentRegion,
            },
            ...options,
            params: {
                locale: cookie.get('bce-locale') || 'zh-cn',
                _: (new Date()).getTime(),
                ...options.params,
            },
        };
        if (config.params?._config) {
            delete config.params._config;
        }
        if (config.data?._config) {
            delete config.data._config;
        }
        return config;
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onResolve: (result, params, options) => {
        const {success, page, message: msg} = result.data;
        if (!success) {
            message.error(msg || '请求失败，请重试', 0);
            throw new Error(msg || '请求失败，请重试');
        }
        return page;
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onReject: (result, params, options) => {
        // TODO
    },
    transformDeleteParamsIntoBody: true,
});

const createFactoryIam = createFactory({
    onPending: (params, options) => {
        const csrftoken = parseCookie('bce-user-info', true);
        const _config = params?._config || options?.data?._config;
        const config = {
            headers: {
                csrftoken: csrftoken ? csrftoken.replace(/"/g, '') : null,
                'X-Requested-By': 'ERApplication',
                'X-Region': _config?.region || window.FRAMEWORK_DATA.currentRegion,
            },
            ...options,
            params: {
                locale: cookie.get('bce-locale') || 'zh-cn',
                _: (new Date()).getTime(),
                ...options.params,
            },
        };
        if (config.params?._config) {
            delete config.params._config;
        }
        if (config.data?._config) {
            delete config.data._config;
        }
        return config;
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onResolve: (result, params, options) => {
        const {success, page, message: msg} = result.data;
        if (!success) {
            message.error(msg || '请求失败，请重试', 0);
            throw new Error(msg || '请求失败，请重试');
        }
        return page;
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onReject: (result, params, options) => {
        // TODO
    },
    transformDeleteParamsIntoBody: true,
});

export const createInterfaceBlb = createFactoryBlb.createInterface;
export const createInterfaceIam = createFactoryIam.createInterface;

export default createInterface;
