import {checkAuthorize<PERSON><PERSON>} from '@/api/authorize';
import {ROLE_NAME} from '@/dicts/authorize';
import urls from '@/urls';
import {IS_LOCALHOST} from '@/dicts';
let authorizeState = false;
export const checkAuthorize = async (accountId: string, history: any) => {
    // 本地关闭权限校验
    if (IS_LOCALHOST) {
        return true;
    }
    if (!authorizeState) {
        try {
            const res = await checkAuthorizeApi({
                roleName: ROLE_NAME,
                accountId,
            });
            // 如果没有用户信息 应该是个undefiend
            if (!res?.id) {
                history.push(urls.subscribeService.fill()); // 跳转到开通页
                return false;
            }
            authorizeState = true;
            return true;
        } catch (err) {
            history.push(urls.subscribeService.fill()); // 跳转到开通页
            return false;
        }
    }
    return true;
};

export const setAuthorizeState = (state: boolean) => {
    authorizeState = state;
};
