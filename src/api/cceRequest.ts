import createInterface from '@/utils/axios';
import {ClusterId, ClusterName} from '@/interface/cluster';
import {ERole} from '@/dicts/authorize';

export type TRole = ERole.CCE_ADMIN | ERole.CCE_DEVOPS | ERole.CCE_READONLY;

interface IGetClusterRbacParams {
    userID: string;
}
interface IGetClusterRbacResponse {
    data: IRoleClusterData[];
    requestID: string;
}
export interface IRoleClusterData {
    clusterID: ClusterId;
    clusterName: ClusterName;
    namespace: string;
    role: TRole; // all-管理员 devops-运维开发 readonly-只读
}
export const getClusterRbacApi = createInterface<IGetClusterRbacParams, IGetClusterRbacResponse>(
    'GET',
    '/api/cce/service/v2/rbac'
);
