import createInterface from '@/utils/axios';
import {ParamsServiceMeshInstanceId} from './serviceMesh';

export type TServicemeshTraceService = 'J<PERSON><PERSON>/Zipkin';
export interface IServicemeshTrace {
    traceEnabled: boolean;
    samplingRate?: number;
    service?: TServicemeshTraceService;
    address?: string;
}

interface IUpdateTraceParams extends ParamsServiceMeshInstanceId, IServicemeshTrace {
}

export const getTraceApi = createInterface<
    ParamsServiceMeshInstanceId,
    IServicemeshTrace
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/trace');

export const updateTraceApi = createInterface<
    IUpdateTraceParams,
    boolean
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/trace');
