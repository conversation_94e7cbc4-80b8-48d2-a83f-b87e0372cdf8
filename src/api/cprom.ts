import createInterface from '@/utils/axios';
import {ParamsServiceMeshInstanceId} from './serviceMesh';
import {IMonitorItem} from './createServiceMesh';

type TCpromPhase = 'Pending' | 'Creating' | 'Terminating' | 'Failed' | 'Running' | 'Upgrading' | 'Unknown';
export type TCpromInstanceTemplate = 'advance-v2';
export type TMonitorReason = 'CPromAgentException' | 'CSMEnvoyJobDeletedException' | 'CPromInstanceDeletedException';
export interface ICprom {
    status: {
        phase: TCpromPhase;
        message: string;
    };
    spec: {
        instanceID: string;
        instanceName: string;
        region: string;
        vmClusterConfig: {
            retentionPeriod: string;
        };
    };
    metadata: {
        creationTimestamp: string;
        name: string;
        labels: {
            'cprom-instance-type': string;
            'cprom-instance-template': TCpromInstanceTemplate;
        };
    };
    monitorGrafanaId: string;
    monitorGrafanaName: string;
    csmMonitor: {
        enabled: boolean;
        monitorDetail: boolean;
        monitorReason: TMonitorReason;
        description: string;
    };
}

interface IUpdateCpromParams extends ParamsServiceMeshInstanceId, IMonitorItem {}

export const updateCpromApi = createInterface<
    IUpdateCpromParams,
    IMonitorItem
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/monitor');

export const getCpromApi = createInterface<
    ParamsServiceMeshInstanceId,
    ICprom
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/monitor');
