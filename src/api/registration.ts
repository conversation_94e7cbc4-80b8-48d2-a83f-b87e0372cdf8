import createInterface, {createInterfaceBlb} from '@/utils/axios';
import {QueryRequestParamsBase} from '@/interface';

export interface ParamsRegistrationInstanceId {
    registrationhInstanceId: string;
}

export const getRegistrationListApi = createInterface<any, {result: any[]}>('GET', '/api/cse/v1/registries');
interface MonitorParamsBase{
    pageSize?: number;
    pageNo?: number;
}

export interface QueryRegistrationNamespace extends ParamsRegistrationInstanceId, QueryRequestParamsBase{
}

interface RegistrationNamespaceItem{
    name: string;
    comment?: string;
}

export interface ParamsRegistrationNamespace extends ParamsRegistrationInstanceId{
    namespaces: RegistrationNamespaceItem[];
}
export interface ParamsRegistrationNamespaceName extends ParamsRegistrationInstanceId{
    namespaces: string;
}

interface MonitorResponseBase{
    items?: any;
}

export const getRegistrationInstanceApi = createInterface<
    ParamsRegistrationInstanceId,
    any
>('GET', '/api/cse/v1/registry/{registrationhInstanceId}');


export const putRegistrationInstanceApi = createInterface<
    ParamsRegistrationInstanceId,
    any
>('PUT', '/api/cse/v1/registry/{registrationhInstanceId}');

export const getRegistrationMonitor = createInterface<
    MonitorParamsBase,
    MonitorResponseBase
>('GET', '/api/cprom/service/v1/instances');

export const getRegistrationParams = createInterface<
    ParamsRegistrationInstanceId,
    any
>('GET', '/api/cse/v1/registry/{registrationhInstanceId}/config');

export const getRegistrationNamespaceApi = createInterface<
    QueryRegistrationNamespace,
    any
>('GET', '/api/cse/v1/registry/{registrationhInstanceId}/namespaces');

export const createRegistrationNamespaceApi = createInterface<
    ParamsRegistrationNamespace,
    any
>('POST', '/api/cse/v1/registry/{registrationhInstanceId}/namespaces');

export const deleteRegistrationNamespaceApi = createInterface<
    ParamsRegistrationNamespaceName,
    any
>('DELETE', '/api/cse/v1/registry/{registrationhInstanceId}/namespaces?namespaces={namespaces}');

export const putRegistrationNamespaceApi = createInterface<
    ParamsRegistrationNamespace,
    any
>('PUT', '/api/cse/v1/registry/{registrationhInstanceId}/namespaces');

export const putRegistrationParams = createInterface<
    ParamsRegistrationInstanceId,
    any
>('PUT', '/api/cse/v1/registry/{registrationhInstanceId}/config');

export const deleteRegistrationInstanceApi = createInterface<
    ParamsRegistrationInstanceId,
    null
>('DELETE', '/api/cse/v1/registry/{registrationhInstanceId}');

export const getPrometheusList = createInterface<
    any,
    null
>('GET', '/api/cprom/service/v1/instances');

export const getPrometheusTokenList = createInterface<
    any,
    null
>('GET', '/api/cprom/service/v1/instance/{cpromInstanceId}/token');

export const createRegistrationInstance = createInterface<
    any,
    null
>('POST', '/api/cse/v1/registry');

export const getSubNetIpNumber = createInterface<
    any,
    null
>('GET', '/api/network/v1/subnet/{subnetId}');


export const getEsgInstanceListApi = createInterfaceBlb<
    MonitorParamsBase,
    any
>('POST', '/api/network/v1/enterprise/security/list');
