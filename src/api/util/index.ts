import {RegionList} from '@/hooks';
import {IDiscoverySelector, IInstanceItem} from '../createServiceMesh';

export interface Option {
    key: string;
    value: string;
    label: React.ReactNode;
    disabled?: boolean;
    children?: Option[];
}

// 放置一些后端数据转前端数据的函数
export const getDiscoverySelectorViewData = (discoverySelector: IDiscoverySelector) => {
    const matchLabels = JSON.stringify(discoverySelector.matchLabels) === '{}'
        ? {'': ''}
        : discoverySelector.matchLabels;

    return {...discoverySelector, matchLabels};
};

export const getMonitorInstanceOptions = (
    curSelectedRegion: string,
    monitorInstanceList: IInstanceItem[],
    regionList: RegionList,
    formated?: (instanceItem: IInstanceItem) => any
) => {
    if (!monitorInstanceList) {
        return [];
    }

    const selectableRegionList = [...new Set(monitorInstanceList.map(v => v.region))];
    return selectableRegionList.reduce((acc: Option[], region) => {
        const optionObj: Option = {
            key: region,
            value: region,
            label: regionList[region],
            disabled: region !== curSelectedRegion,
            children: monitorInstanceList.filter(v => v.region === region)
                .map(v => {
                    const {id = '', name = ''} = v;
                    const value = formated ? formated(v) : id;
                    return {key: id, value, label: name};
                }),
        };

        acc.push(optionObj);
        return acc;
    }, []) ?? [];
};
