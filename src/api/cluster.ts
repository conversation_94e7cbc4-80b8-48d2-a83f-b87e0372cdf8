import {QueryRequestParamsBase, QueryResponseBase} from '@/interface';
import {Cluster} from '@/interface/cluster';
import createInterface from '@/utils/axios';

export interface ParamsGetCandidateClusterList extends QueryRequestParamsBase {
    serviceMeshInstanceId: string;
}

export interface ParamsAddClusterList {
    serviceMeshInstanceId: string;
    clusters: Array<{region: string, clusterId: string, clusterName: string}>;
}

export const checkClusterList = createInterface<
    ParamsAddClusterList,
    QueryResponseBase<Cluster[]>
>('POST', '/api/csm/instance/{serviceMeshInstanceId}/clusterList/check');


export const getClusterListApi = createInterface<
    ParamsGetCandidateClusterList,
    QueryResponseBase<Cluster[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/clusterList');

export const getCandidateClusterList = createInterface<
    ParamsGetCandidateClusterList,
    QueryResponseBase<Cluster[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/clusterList/candidate');

export const addClusterList = createInterface<
    ParamsAddClusterList,
    null
>('POST', '/api/csm/instance/{serviceMeshInstanceId}/clusterList');
