import {QueryRequestParamsBase, QueryResponseBase} from '@/interface';
import {ServiceMeshInstanceId} from '@/interface/serviceMesh';
import createInterface from '@/utils/axios';

interface GetServiceListParams extends QueryRequestParamsBase {
    serviceMeshInstanceId: ServiceMeshInstanceId;
    clusterID?: string;
    clusterRegion?: string;
}

interface Service {
    host: string;
    namespace?: string;
    k8sServiceCount: number;
    serviceEntryCount: number;
}
type ServiceList = Service[];
type ServiceListResponse = QueryResponseBase<ServiceList>;

interface ParamsCreateServiceDetails {
    serviceMeshInstanceId: ServiceMeshInstanceId;
    host: string;
}

interface ServiceDetailsResponse {
    k8sServiceDetailInfoList: [
        {
            region: string;
            clusterId: string;
            clusterName: string;
            service: string;
            namespace: string;
        },
    ];
    serviceEntryDetailInfoList: [
        {
            name: string;
            namespace: string;
            exportTo: string;
        }
    ];
}

export const getServiceListApi = createInterface<
    GetServiceListParams,
    ServiceListResponse
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/serviceList');

export const getServiceDetailsApi = createInterface<
    ParamsCreateServiceDetails,
    ServiceDetailsResponse
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/{host}/serviceDetail');
