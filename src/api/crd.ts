import cookie from 'js-cookie';
import {Crd, QueryRequestParamsBase, QueryResponseBase} from '@/interface';
import {ServiceMeshInstanceId} from '@/interface/serviceMesh';
import createInterface from '@/utils/axios';
import {parseCookie} from '@/utils/parseCookie';

export interface ParamsGetCrdListByInstanceId extends QueryRequestParamsBase {
    serviceMeshInstanceId: ServiceMeshInstanceId;
}

export interface ParamsCreateCrd {
    serviceMeshInstanceId: ServiceMeshInstanceId;
    content: string;
}

export interface ParamsGetCrd {
    serviceMeshInstanceId: ServiceMeshInstanceId;
    namespace: string;
    kind: string;
    name: string;
}

export interface ParamsUpdateCrd {
    serviceMeshInstanceId: ServiceMeshInstanceId;
    namespace: string;
    kind: string;
    name: string;
    content: string;
}

export interface ParamsDeleteCrd{
    serviceMeshInstanceId: ServiceMeshInstanceId;
    crdList: Crd[];
}

// 导入Istio资源的请求参数
export interface ParamsImportCrd {
    serviceMeshInstanceId: ServiceMeshInstanceId;
    file: File;
    actionType: '1' | '2'; // 1跳过；2覆盖
}

// 导入结果中的资源项
export interface ImportResourceItem {
    kind: string;
    name: string;
    namespace: string;
    operation: 'create' | 'update' | 'skip';
    error?: string;
}

// 导入Istio资源的响应数据
export interface ImportCrdResponse {
    totalCount: number;
    successCount: number;
    updateCount: number;
    failureCount: number;
    skippedCount: number;
    successList: ImportResourceItem[];
    updateList: ImportResourceItem[];
    skippedList: ImportResourceItem[];
    failureList: ImportResourceItem[];
}

export const getCrdListByInstanceId = createInterface<
    ParamsGetCrdListByInstanceId,
    QueryResponseBase<Crd[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/crdList');

export const createCrd = createInterface<
    ParamsCreateCrd,
    Crd
>('POST', '/api/csm/instance/{serviceMeshInstanceId}/crd');

export const getCrd = createInterface<
    ParamsGetCrd,
    Crd
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/{namespace}/crd?kind={kind}&name={name}');

export const updateCrd = createInterface<
    ParamsUpdateCrd,
    Crd
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/{namespace}/crd?kind={kind}&name={name}');

export const deleteCrd = createInterface<
    ParamsDeleteCrd,
    null
>('DELETE', '/api/csm/instance/{serviceMeshInstanceId}/crd');

// 导入Istio资源接口 - 需要特殊处理文件上传
// eslint-disable-next-line complexity
export const importCrd = async (params: ParamsImportCrd): Promise<ImportCrdResponse> => {
    // 获取CSRF token，与项目中axios的处理方式保持一致
    const csrftoken = parseCookie('bce-user-info', true);
    const cleanCsrftoken = (typeof csrftoken === 'string' && csrftoken)
        ? csrftoken.replace(/"/g, '') : null;

    // 获取当前区域信息
    const currentRegion = window.FRAMEWORK_DATA?.currentRegion || 'bj';

    // 构建FormData
    const formData = new FormData();
    formData.append('file', params.file);
    formData.append('actionType', params.actionType);

    // 添加locale和时间戳参数（与项目axios保持一致）
    formData.append('locale', cookie.get('bce-locale') || 'zh-cn');
    formData.append('_', String(new Date().getTime()));

    try {
        const apiUrl = `/api/csm/migrate/${params.serviceMeshInstanceId}/resource`;
        const response = await fetch(apiUrl, {
            method: 'POST',
            body: formData,
            headers: {
                // 注意：不要设置Content-Type，让浏览器自动设置multipart/form-data的boundary
                'csrftoken': cleanCsrftoken || '',
                'X-Requested-By': 'ERApplication',
                'X-Region': currentRegion,
            },
        });

        if (!response.ok) {
            const errorData = await response.json();
            const errorMsg = errorData.message?.global || errorData.message
                || `HTTP ${response.status}: ${response.statusText}`;
            throw new Error(errorMsg);
        }

        const result = await response.json();

        // 检查业务逻辑是否成功
        if (result.success === false) {
            const businessErrorMsg = result.message?.global || result.message || '导入失败';
            throw new Error(businessErrorMsg);
        }

        return result.result;
    } catch (error) {
        // 提供更具体的错误信息
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('csrf')) {
            throw new Error('CSRF验证失败，请刷新页面后重试');
        }
        throw error;
    }
};
