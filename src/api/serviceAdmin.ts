import {QueryRequestParamsBase, QueryResponseBase} from '@/interface';
import createInterface from '@/utils/axios';

export interface ServiceAdminListItemType {
    name: string;
    namespace: string;
    department: string;
    business: string;
    createTime: string;
    updateTime: string;
    healthCount: number;
    totalCount: number;
}

export interface ServiceInstanceListItemType {
  serviceInstanceId: string;
  host: string;
  port: string;
  weight: string;
  healthStatus: boolean;
  isolateEnable: boolean;
  ttl: number;
  createTime: string;
  updateTime: string;
}

export interface ServiceInstanceItemType {
    id: string;
    host: string;
    isolateEnable: boolean;
    service: string;
}

export const getServiceAdminListApi = createInterface<
    QueryRequestParamsBase,
    QueryResponseBase<ServiceAdminListItemType[]>
>('GET', '/api/cse/v1/registry/{instanceId}/services');


export const getServiceInstanceListApi = createInterface<
    {serviceId: string, serviceName: string, instanceId: string, queryInstanceId: string},
    QueryResponseBase<ServiceInstanceListItemType[]>
    >('GET', '/api/cse/v1/registry/{instanceId}/service/{serviceId}/serviceInstances');

export const deleteServiceInstanceApi = createInterface<
    {instanceId: string, serviceId: string, serviceInstanceId: string},
    null
>('DELETE', '/api/cse/v1/registry/{instanceId}/service/{serviceId}/serviceInstance/{serviceInstanceId}');


export const batchUpdateServiceInstanceApi = createInterface<
    {instanceId: string, serviceId: string, serviceInstanceList: Array<ServiceInstanceItemType | null>, action: string},
    null
>('POST', '/api/cse/v1/registry/{instanceId}/service/{serviceId}/serviceInstances?Action={action}');


export const createServiceInstanceApi = createInterface<
    { instanceId: string, serviceId: string }
>('POST', '/api/cse/v1/registry/{instanceId}/service/{serviceId}/serviceInstance');

export const editServiceInstanceApi = createInterface<
    { instanceId: string, serviceId: string, serviceInstanceId: string}
>('PUT', '/api/cse/v1/registry/{instanceId}/service/{serviceId}/serviceInstance/{serviceInstanceId}');


