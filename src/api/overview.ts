import createInterface from '@/utils/axios';

export interface ServicesOverview {
    allOf: number;
    noneOf: number;
    anyOf: number;
}
export interface ClustersOverview {
    runningNum: number;
    total: number;
}
export interface GetServiceMeshOverviewListResponse {
    instanceId: string;
    instanceName: string;
    servicesOverview: ServicesOverview;
    sidecarNum: number;
    region: string;
    status: string;
    clustersOverview: ClustersOverview;
}
interface GetSidecarsOverviewParams {
    top?: number;
    region: string;
}
interface SidecarsOverviewItem {
    instanceId: string;
    instanceName: string;
    num: number;
}
export type GetSidecarsOverviewResponse = SidecarsOverviewItem[];
export interface GetInstancesOverviewResponse {
    groupByRegion: Record<string, number>;
    groupByStatus: {
        changing: number;
        exception: number;
        running: number;
    };
}

export interface getServiceMeshOverviewListParams {
    region: string;
}

export interface getInstancesOverviewParams {
    region: string;
}

// 查询网格实例概览信息
export const getServiceMeshOverviewListApi = createInterface<
    getServiceMeshOverviewListParams,
    GetServiceMeshOverviewListResponse[]
>('GET', '/api/csm/overview/instances/detail');

export const getInstancesOverviewApi = createInterface<
    getInstancesOverviewParams,
    GetInstancesOverviewResponse
>('GET', '/api/csm/overview/instances');

export const getSidecarsOverviewApi = createInterface<
    GetSidecarsOverviewParams,
    GetSidecarsOverviewResponse
>('GET', '/api/csm/overview/sidecars');
