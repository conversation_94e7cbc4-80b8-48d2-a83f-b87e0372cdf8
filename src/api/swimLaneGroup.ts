import createInterface from '@/utils/axios';
import {QueryResponseBase} from '@/interface';
import {ParamsServiceMeshInstanceId} from './serviceMesh';

// 泳道组
interface IPathSwimLaneGroup extends ParamsServiceMeshInstanceId {
    laneGroupID: string;
}

export interface ILaneServiceListItem {
    clusterRegion: string;
    clusterName: string;
    clusterID: string;
    namespace: string;
    isHost: boolean;
    serviceName: string;
}

export type TMatchingMode = 'exact' | 'prefix' | 'regex';
export interface IMatchRequestHeaderItem {
    headerId?: string;
    name: string;
    matchingMode: TMatchingMode;
    matchingContent: string;
}
interface IMatchRequest {
    routeName: string;
    headers: IMatchRequestHeaderItem[];
    uri: {
        enabled: boolean;
        matchingContent: string;
        matchingMode: TMatchingMode;
    };
}

interface IRouteDestinationItem {
    destination: {
        subset: string;
        host: string;
    };
}

export interface IRuleListItem {
    ruleId?: string;
    matchRequest: IMatchRequest;
    routeDestinations?: IRouteDestinationItem[];
}

export interface IRouteListItem extends ILaneServiceListItem {
    rules: IRuleListItem[];
}

export interface ISwimLane {
    laneID?: string;
    laneName: string;
    isBaseLane: boolean;
    labelSelectorKey: string;
    labelSelectorValue: string;
    serviceList: ILaneServiceListItem[];
    routeList?: IRouteListItem[];
}

export interface ICreateSwimLaneGroupParams extends ParamsServiceMeshInstanceId {
    groupID?: string;
    groupName: string;
    traceHeader: string;
    routeHeader: string;
    serviceList: ILaneServiceListItem[];
    baseLane: ISwimLane;
}

// 泳道
interface IPathSwimLane extends IPathSwimLaneGroup {
    laneID?: string;
}

export interface ICreateSwimLaneParams extends IPathSwimLane, ISwimLane {
}

interface IUpdateSwimLaneParams extends ICreateSwimLaneParams {
    laneID: string;
}

// 引流规则
interface IPathSwimLaneRoute extends IPathSwimLane {
    routeID?: string;
}

interface ICreateSwimLaneRouteParams extends IPathSwimLane {
    routeList: IRouteListItem[];
}

interface IUpdateSwimLaneRouteParams extends IPathSwimLaneRoute, IRouteListItem {
}

interface IGetLaneGroupLabelListParams extends ParamsServiceMeshInstanceId {
    serviceList: ILaneServiceListItem[];
}

export interface ILaneGroupLabelItem {
    key: string;
    valueList: string[];
}

interface IGetLaneServiceListParams extends ParamsServiceMeshInstanceId {
    clusterID?: string;
    clusterRegion?: string;
    namespace?: string;
}

interface IDeleteSwimLaneRouteParams extends IPathSwimLaneRoute {
    clusterRegion: string;
    clusterID: string;
    namespace: string;
    serviceName: string;
}

export const getLaneServiceListApi = createInterface<
    IGetLaneServiceListParams,
    QueryResponseBase<ILaneServiceListItem[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/lane/serviceList');

export const getLaneGroupLabelListApi = createInterface<
    IGetLaneGroupLabelListParams,
    QueryResponseBase<ILaneGroupLabelItem[]>
>('POST', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/label');

export const deleteSwimLaneRouteApi = createInterface<
    IDeleteSwimLaneRouteParams,
    boolean
>('DELETE', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/{laneGroupID}/lane/{laneID}/route');

export const getSwimLaneRouteApi = createInterface<
    IPathSwimLaneRoute,
    IRouteListItem
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/{laneGroupID}/lane/{laneID}/route:/{routeID}');

export const updateSwimLaneRouteApi = createInterface<
    IUpdateSwimLaneRouteParams,
    boolean
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/{laneGroupID}/lane/{laneID}/route');

export const createSwimLaneRouteApi = createInterface<
    ICreateSwimLaneRouteParams,
    boolean
>('POST', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/{laneGroupID}/lane/{laneID}/route');

export const getSwimLaneApi = createInterface<
    IPathSwimLane,
    ISwimLane
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/{laneGroupID}/lane/{laneID}');

export const getSwimLaneListApi = createInterface<
    IPathSwimLaneGroup,
    QueryResponseBase<ISwimLane[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/{laneGroupID}/lanes');

export const deleteSwimLaneApi = createInterface<
    IPathSwimLane,
    boolean
>('DELETE', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/{laneGroupID}/lane/{laneID}');

export const setBaseSwimLaneApi = createInterface<
    IPathSwimLane,
    QueryResponseBase<IRouteListItem[]>
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/{laneGroupID}/baseLane/{laneID}');

export const updateSwimLaneApi = createInterface<
    IUpdateSwimLaneParams,
    boolean
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/{laneGroupID}/lane/{laneID}');

export const createSwimLaneApi = createInterface<
    ICreateSwimLaneParams,
    boolean
>('POST', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/{laneGroupID}/lane');

export const getSwimLaneGroupListApi = createInterface<
    ParamsServiceMeshInstanceId,
    QueryResponseBase<ICreateSwimLaneGroupParams[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/laneGroups');

export const deleteSwimLaneGroupApi = createInterface<
    IPathSwimLaneGroup,
    boolean
>('DELETE', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup/{laneGroupID}');

export const createSwimLaneGroupApi = createInterface<
    ICreateSwimLaneGroupParams,
    boolean
>('POST', '/api/csm/instance/{serviceMeshInstanceId}/laneGroup');
