import _ from 'lodash';
import {Region} from '@/dicts';
import {BillingMode, ServiceMeshStatus, ServiceMeshType} from '@/dicts/serviceMesh';
import {QueryRequestParamsBase, QueryResponseBase} from '@/interface';
import {IstioVersion, ServiceMeshInstance, ServiceMeshInstanceId} from '@/interface/serviceMesh';
import createInterface from '@/utils/axios';
import {IDiscoverySelector, IInstanceItem} from './createServiceMesh';

export interface ParamsServiceMeshInstanceId {
    serviceMeshInstanceId: ServiceMeshInstanceId;
}

interface IDelServiceMeshInstanceParams {
    serviceMeshInstanceId: ServiceMeshInstanceId;
    isReleaseControlPlaneBlb: boolean;
    isReleaseEip: boolean;
}

export interface ServiceMeshListItem {
    billingModel: BillingMode;
    clusterCount: number;
    createTime: string;
    instanceId: ServiceMeshInstanceId;
    instanceName: string;
    instanceType: ServiceMeshType;
    istioVersion: IstioVersion;
    region: Region;
    runningClusterCount: number;
    sidecarCount: number;
    instanceStatus: ServiceMeshStatus;
    publicEnabled: boolean;
    blbInfo: {
        blbId: string;
        blbName: string;
    };
    eipInfo: {
        eipId: string;
        eipName: string;
    };
}

interface ParamsUpdateDiscoverySelector extends IDiscoverySelector {
    serviceMeshInstanceId: ServiceMeshInstanceId;
}

interface IParamsForUpdateMonitor {
    serviceMeshInstanceId: ServiceMeshInstanceId;
    enabled: boolean;
    // enabled 为 false时，不传 instances
    instances?: IInstanceItem[];
}

interface ICompItemMeta {
    type: string;
    name: string;
    installInfo: {
        allowInstall: boolean;
    };
    shortIntroduction: string;
    detailIntroduction: string;
    defaultParams: string;
    latestVersion: string;
}

type TCompItemStatusPhase = 'Running' | 'Installing' | 'Abnormal';
interface ICompItemInstance {
    name: string;
    status: {
        phase: TCompItemStatusPhase;
    };
    uninstallInfo: {
        allowUninstall: boolean;
    };
    updateInfo: {
        allowUpdate: boolean;
        message: string;
    };
    installedVersion: string;
    params: string;
    upgradeInfo: {
        allowUpgrade: boolean;
        nextVersion: string;
        message: string;
    };
}

interface ICompItem {
    meta: ICompItemMeta;
    instance: ICompItemInstance;
}

interface IParamsForInstallOrUnInstallServiceMeshComp extends ParamsServiceMeshInstanceId {
    name: string;
    params?: string;
}

interface KubeConfigType {
    kubeConfig: string;
    kubeConfigType: string;
}

export const installServiceMeshCompApi = createInterface<
    IParamsForInstallOrUnInstallServiceMeshComp,
    boolean
>('POST', '/api/csm/instance/{serviceMeshInstanceId}/addon');

export const uninstallServiceMeshCompApi = createInterface<
    IParamsForInstallOrUnInstallServiceMeshComp,
    QueryResponseBase<ICompItem[]>
>('DELETE', '/api/csm/instance/{serviceMeshInstanceId}/addon');

export const getServiceMeshCompListApi = createInterface<
    ParamsServiceMeshInstanceId,
    ICompItem[]
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/addon');

export const getServiceMeshListApi = createInterface<
    QueryRequestParamsBase,
    QueryResponseBase<ServiceMeshListItem[]>
>('GET', '/api/csm/instance/list');

export const getServiceMeshInstanceApi = createInterface<
    ParamsServiceMeshInstanceId,
    ServiceMeshInstance
>('GET', '/api/csm/instance/{serviceMeshInstanceId}');

// 最好不要暴露 updateMonitorApi
const updateMonitorApi = createInterface<
    IParamsForUpdateMonitor,
    IParamsForUpdateMonitor
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/monitor');
// 清洗数据，再传给后端
export const updateMonitorApiWithCleanData = (updateMonitor: IParamsForUpdateMonitor) => {
    const params = updateMonitor.enabled
        ? updateMonitor
        : _.omit(updateMonitor, ['instances']);
    return updateMonitorApi(params);
};

export const getDiscoverySelectorApi = createInterface<
    ParamsServiceMeshInstanceId,
    IDiscoverySelector
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/discoverySelector');

export const updateDiscoverySelectorApi = createInterface<
    ParamsUpdateDiscoverySelector,
    IDiscoverySelector
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/discoverySelector');

export const deleteServiceMeshInstanceApi = createInterface<
    IDelServiceMeshInstanceParams,
    null
>('DELETE', '/api/csm/instance/{serviceMeshInstanceId}');


export const getServiceMeshKubeconfig = createInterface<
    {
        serviceMeshInstanceId: ServiceMeshInstanceId;
        kubeConfigType: string;
    },
    KubeConfigType
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/kubeconfig');
