import createInterface from '@/utils/axios';

interface checkAuthorizeParams {
    roleName: string;
    accountId: string;
}
interface checkAuthorizeResponse {
    create_time: string;
    description: string;
    domain_id: string;
    grantType: string;
    id: string;
    name: string;
    type: string;
}

interface IUserInfoRes {
    domain_id: string;
    id: string;
}

// 查询用户信息
export const getUserInfoApi = createInterface<void, IUserInfoRes>(
    'POST',
    '/api/iam/user/detail'
);

// 验证用户是否开通过 服务
export const checkAuthorizeApi = createInterface<checkAuthorizeParams, checkAuthorizeResponse>(
    'POST',
    '/api/iam/sts/role/query'
);

export interface subScribeServiceParams {
    policyId: string;
    roleName: string;
    serviceId: string;
    accountId: string;
}
// 开通服务
export const subScribeServiceApi = createInterface<subScribeServiceParams, any>('POST', '/api/iam/sts/role/activate');
