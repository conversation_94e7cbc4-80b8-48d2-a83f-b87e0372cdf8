import {QueryRequestParamsBase, QueryResponseBase} from '@/interface';
import createInterface from '@/utils/axios';
import {ParamsServiceMeshInstanceId} from './serviceMesh';

interface INamespaceListItem {
    name: string;
}

interface IGetDiagnosisPodListParams extends ParamsServiceMeshInstanceId {
    namespace: string;
}

interface IPodListItem {
    name: string;
}

interface IGetDiagnosisExceptionListParams extends ParamsServiceMeshInstanceId {
    namespace: string;
}

interface IExceptionListItem {
    name: string;
    level: string;
    code: string;
    description: string;
}

interface IGetDiagnosisProxyStatusListParams extends ParamsServiceMeshInstanceId {
    namespace: string;
    podName: string;
}

interface IProxyStatusListItem {
    proxyName: string;
    clusterName: string;
    CDS: string;
    LDS: string;
    EDS: string;
    RDS: string;
    ECDS: string;
    pilotName: string;
    version: string;
}

interface IGetDiagnosisProxyConfigListParams extends QueryRequestParamsBase {
    serviceMeshInstanceId: string;
    proxyName: string;
    clusterName: string;
    type: string;
}

export const getDiagnosisProxyConfigList = createInterface<
    IGetDiagnosisProxyConfigListParams,
    QueryResponseBase<any[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/diagnosis/proxyConfigList');

export const getDiagnosisProxyStatusList = createInterface<
    IGetDiagnosisProxyStatusListParams,
    QueryResponseBase<IProxyStatusListItem[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/diagnosis/{namespace}/proxyStatusList');

export const getDiagnosisExceptionList = createInterface<
    IGetDiagnosisExceptionListParams,
    QueryResponseBase<IExceptionListItem[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/diagnosis/{namespace}/exceptionList');

export const getDiagnosisPodList = createInterface<
    IGetDiagnosisPodListParams,
    QueryResponseBase<IPodListItem[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/diagnosis/{namespace}/podList');

export const getDiagnosisNamespaceList = createInterface<
    ParamsServiceMeshInstanceId,
    QueryResponseBase<INamespaceListItem[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/diagnosis/namespaceList');
