import {QueryResponseBase} from '@/interface';
import {ServiceMeshInstanceId} from '@/interface/serviceMesh';
import createInterface from '@/utils/axios';

interface GetClusterListResponse {
    addedTime: string;
    clusterId: string;
    clusterName: string;
    status: string;
    connectionState: string;
    vpcId: string;
    vpcName: string;
    region: string;
    networkSegment: string;
    version: string;
}
interface DeleteClusterListParams {
    serviceMeshInstanceId: ServiceMeshInstanceId;
    clusters: Array<{
        region: string;
        clusterId: string;
    }>;
}

export const deleteClusterListApi = createInterface<
    DeleteClusterListParams,
    QueryResponseBase<GetClusterListResponse>
>('DELETE', '/api/csm/instance/{serviceMeshInstanceId}/clusterList');
