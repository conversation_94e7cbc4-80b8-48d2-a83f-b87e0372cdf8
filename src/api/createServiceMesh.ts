import {QueryRequestParamsBase} from '@/interface';
import {TServiceMeshType} from '@/modules/CreateServiceMesh/type';
import createInterface from '@/utils/axios';
import {IServicemeshTrace} from './trace';

export interface IClusterData {
    clusterId: string;
    clusterName: string;
    clusterVersion: string;
    admin: boolean;
    istioInstalledStatus: boolean;
}

export interface IDiscoverySelector {
    enabled: boolean;
    matchLabels: Record<string, string> | string[][];
}

// Cprom
export interface IInstanceItem {
    region: string;
    id: string;
    name?: string;
    isInstallCPromAgent: boolean;
}

export interface IInstanceList {
    instances: IInstanceItem[];
}

export interface IMonitorItem {
    enabled?: boolean;
    instances?: IInstanceItem[] | string[];
}

interface IBlsInstanceItem {
    name: string;
    region: string;
}
export interface IBlsItem {
    enabled: boolean;
    instances?: IBlsInstanceItem | string;
}

export type TConfigCluster = 'EXTERNAL' | 'REMOTE';
export interface ICreateServiceMeshParams {
    type: string;
    serviceMeshInstanceName: string;
    istioVersion: string;
    networkType: {
        vpcNetworkId: string;
        subnetId: string;
    };
    securityGroupId: string;
    elasticPublicNetwork: {
        enabled: boolean;
        type?: string;
        // 选中【绑定已有弹性公网 IP】时为必填项。
        id?: string;
    };
    installationClusterId: string;
    installationClusterName: string;
    discoverySelector: IDiscoverySelector;
    bls?: IBlsItem;
    monitor: IMonitorItem;
    multiProtocol: boolean;
    configCluster?: TConfigCluster;
    apiServerEip?: boolean;
    traceInfo: IServicemeshTrace;
}

export interface IVpcNetworkItem {
    vpcId: string;
    name: string;
    cidr: string;
}

export interface IGetSubnetListParams {
    vpcId: string;
}

export interface IGetSecurityGroupListParams {
    vpcId: string;
}

export interface ISubnetItem {
    subnetId: string;
    name: string;
    cidr: string;
}

export interface ISecurityGroupItem {
    id: string;
    name: string;
}

export interface IElasticPublicNetworkItem {
    id: string;
    name: string;
    ip: string;
    type: string;
    maxBandwidth: string;
}

export interface IGetIstioListParams {
    type: TServiceMeshType;
}

export interface IGetIstioSupportK8sVersionItem {
    istioVersion: string;
    supportedClusterVersionList: string[];
}

export const getIstioSupportK8sVersionListApi = createInterface<
    void,
    IGetIstioSupportK8sVersionItem[]
>('GET', '/api/csm/istioSupportK8sVersion');

export const getElasticPublicNetworkListApi = createInterface<
    QueryRequestParamsBase,
    ISecurityGroupItem[]
>('GET', '/api/csm/elasticPublicNetworkList');

export const getSecurityGroupListApi = createInterface<
    IGetSecurityGroupListParams,
    ISecurityGroupItem[]
>('GET', '/api/csm/vpc/{vpcId}/securityGroupList');

export const getSubnetListApi = createInterface<
    IGetSubnetListParams,
    ISubnetItem[]
>('GET', '/api/csm/vpc/{vpcId}/subnetList');

export const getVpcNetworkListApi = createInterface<
    void,
    IVpcNetworkItem[]
>('GET', '/api/csm/vpc/vpcList');

export const getMonitorInstanceListApi = createInterface<
    {clusterId?: string},
    IInstanceList
>('GET', '/api/csm/monitor/instanceList');

// 创建网格 请求集群列表
export const getClusterListApi = createInterface<
    void,
    IClusterData[]
>('GET', '/api/csm/cluster/list');
// 创建网格
export const createServiceMeshApi = createInterface<
    ICreateServiceMeshParams,
    boolean
>(
    'POST',
    '/api/csm/instance'
);
// 获取 Istio 的版本列表
export const getIstioListApi = createInterface<
    IGetIstioListParams,
    string[]
>('GET', '/api/csm/instance/{type}/version');
