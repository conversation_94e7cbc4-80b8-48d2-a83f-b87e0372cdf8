import createInterface from '@/utils/axios';
import {QueryResponseBase} from '@/interface';
import {ParamsServiceMeshInstanceId} from './serviceMesh';

interface IGetBlsAgentCheckByClusterIdParams {
    clusterId: string;
}

interface IBlsLogStoreListItem {
    name: string;
    region: string;
    retention: number;
}

interface ICreateBlsLogTaskParams extends ParamsServiceMeshInstanceId {
    name: string;
}

export type TBlsLogTaskStatus = 'Running' | 'Abnormal' | 'Deleted';
export interface IBlsLogTaskListItem {
    name: string;
    createTime: string;
    status: TBlsLogTaskStatus;
}

export type TBlsStatus = 'Open' | 'Close' | 'Abnormal';

export interface IBlsLogListItem {
    name: string;
    retention: number;
    createTime: string;
    updateTime: string;
}

interface IGetBlsLogListRsp extends QueryResponseBase<IBlsLogListItem[]> {
    status: TBlsStatus;
}

export const getBlsLogListApi = createInterface<
    ParamsServiceMeshInstanceId,
    IGetBlsLogListRsp
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/bls/blsList');

export const deleteBlsLogApi = createInterface<
    ParamsServiceMeshInstanceId,
    boolean
>('DELETE', '/api/csm/instance/{serviceMeshInstanceId}/bls/log');

export const getBlsLogTaskListApi = createInterface<
    ParamsServiceMeshInstanceId,
    QueryResponseBase<IBlsLogTaskListItem[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/bls/logTaskList');

export const createBlsLogTaskApi = createInterface<
    ICreateBlsLogTaskParams,
    boolean
>('POST', '/api/csm/instance/{serviceMeshInstanceId}/bls/logTask');

export const getBlsLogStoreListApi = createInterface<
    void,
    QueryResponseBase<IBlsLogStoreListItem[]>
>('GET', '/api/csm/bls/logStoreList');

// 创建网格页，是否安装 BLS agent
export const getBlsAgentCheckByClusterIdApi = createInterface<
    IGetBlsAgentCheckByClusterIdParams,
    {isExist: boolean}
>('GET', '/api/csm/cluster/{clusterId}/bls/agentCheck');

// 网格实例页，是否安装 BLS agent
export const getBlsAgentCheckApi = createInterface<
    ParamsServiceMeshInstanceId,
    {isExist: boolean, clusterId: string}
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/bls/agentCheck');
