import {QueryRequestParamsBase, QueryResponseBase} from '@/interface';
import createInterface, {createInterfaceBlb, createInterfaceIam} from '@/utils/axios';
import {IMonitorItem} from './createServiceMesh';

export type TGatewayStatus = 'deploying' | 'running' | 'abnormal' | 'deleting';
export type TGatewayBillingModel = 'postpaid' | 'free';
export type TGatewayDeployMode = 'hosting' | 'standalone';
export type TGatewayType = 'ingress' | 'egress';
export type TGatewayResourceQuota = '1C2G' | '2C4G' | '4C8G';
export type TGatewayLogType = 'BLS' | 'BOS';
export type TBlbStatus = 'creating' | 'available' | 'updating' | 'paused' | 'unavailable';
export type TBlblistenerItemType = 'TCP' | 'UDP' | 'HTTP' | 'HTTPS' | 'SSL';
export type TBlblistenerItemStatus = 'available';

// 域名管理
export type TDomainProtocol = 'HTTP' | 'HTTPS';

export interface IPathWithGatewayId {
    serviceMeshInstanceId: string;
    gatewayId: string;
}

export interface IGatewayHpa {
    enabled: boolean;
    minReplicas: number;
    maxReplicas: number;
}
export interface IGatewayLog {
    enabled: boolean;
    type: TGatewayLogType;
    logFile: string;
}
export interface IGatewayTlsAcc {
    enabled: boolean;
}
export interface IGatewayBasicConfig {
    gatewayId?: string;
    status?: TGatewayStatus;
    billingModel?: TGatewayBillingModel;
    gatewayName: string;
    deployMode: TGatewayDeployMode;
    gatewayType: TGatewayType;
    resourceQuota: TGatewayResourceQuota;
    replicas: number;
    hpa: IGatewayHpa;
    log: IGatewayLog;
    monitor: IMonitorItem;
    tlsAcc: IGatewayTlsAcc;
}
export interface IGatewayNetworkConfig {
    blbId: string;
    blbName: string;
    networkType: {
        vpcNetworkId: string;
        vpcNetworkName: string;
        vpcNetworkCidr: string;
        subnetId: string;
        subnetName: string;
        subnetCidr: string;
    };
    securityGroupId: string;
    elasticPublicNetwork: {
        enabled: boolean;
        type: string;
        id?: string;
        name: string;
        ip: string;
        eipType: string;
    };
}

export interface ICreateGatewayParams {
    serviceMeshInstanceId: string;
    basicConfig: IGatewayBasicConfig;
    networkConfig: IGatewayNetworkConfig;
}

export interface IGetGatewayListParams extends QueryRequestParamsBase {
    serviceMeshInstanceId: string;
    status?: TGatewayStatus;
}

export interface IGatewayItem {
    gatewayName: string;
    gatewayId: string;
    status: TGatewayStatus;
    billingModel: TGatewayBillingModel;
    deployMode: TGatewayDeployMode;
    resourceQuota: TGatewayResourceQuota;
    replicas: number;
    createTime: string;
}

export type TEipPurchaseType = '' | 'BGP' | 'BGP_S';
export interface IGetAvailableBlbListParams {
    serviceMeshInstanceId: string;
}
export interface IAvailableBlbItem {
    shortId: string;
    name: string;
    publicIp: string;
    eipType: string;
}

export interface IGatewayBlbItem {
    shortId: string;
    name: string;
    eipId: string;
    publicIp: string;
    internalIp: string;
    listenerList: IBlblistenerItem[];
    status: TBlbStatus;
    createTime: string;
}
export interface IBlblistenerItem {
    port: number;
    type: TBlblistenerItemType;
    status: TBlblistenerItemStatus;
}

export interface IUnusedEipItem {
    shortId: string;
    name: string;
    eip: string;
    eipPurchaseType: TEipPurchaseType;
    bandWidth: number;
}

export interface IGetGatewayDetail {
    basicConfig: IGatewayBasicConfig;
    networkConfig: IGatewayNetworkConfig;
}

export interface IAddGatewayBlbParams extends IPathWithGatewayId {
    networkConfig: IGatewayNetworkConfig;
}

export interface IDeleteGatewayBlbParams {
    instanceId: string;
    gatewayId: string;
    isReleaseBlb: boolean;
    isReleaseEip: boolean;
    blbId: string;
    eipId: string;
}

export interface IGetDomainListParams extends QueryRequestParamsBase, IPathWithGatewayId {
}

export interface IDomainListItem {
    port: {
        name: string;
        number: number;
        // 编辑时，为必传字段
        preNumber?: number;
        protocol: TDomainProtocol;
    };
    domains: string[] | string;
    cert?: {
        id: string;
        name: string;
    };
    isForceHttps: boolean;
    updateTime?: string;
}

export interface ICertificateListItem {
    certId: string;
    certName: string;
}

export interface ICreateOrUpdateDomainParams extends IDomainListItem, IPathWithGatewayId {
}

export interface IDeleteDomainParams extends IPathWithGatewayId {
    domains: string[];
    cert?: {
        id: string;
        name: string;
    };
}

export interface ILogstoreListItem {
    logStoreName: string;
}

export interface IUpdateGatewayLogParams extends IPathWithGatewayId, IGatewayLog {
}

export interface IUpdateGatewayHpaParams extends IPathWithGatewayId, IGatewayHpa {
}

export interface IUpdateGatewayMonitorParams extends IPathWithGatewayId, IMonitorItem {
}

export interface IUpdateGatewayTlsAccParams extends IPathWithGatewayId, IGatewayTlsAcc {
}

export interface IUpdateGatewayResourceQuotaParams extends IPathWithGatewayId {
    resourceQuota: TGatewayResourceQuota;
}

export type TGatewayIngressStatus = 'updating' | 'done' | 'exception';
export interface IGatewayIngressItem {
    enabled: boolean;
    region: string;
    clusterId: string;
    clusterName?: string;
    status?: TGatewayIngressStatus;
}

export interface IGetGatewayIngressStatusRsp {
    clusterList: IGatewayIngressItem[];
}

export interface IUpdateGatewayIngressStatusParams extends IPathWithGatewayId, IGetGatewayIngressStatusRsp {
}

export const getGatewayIngressStatusApi = createInterface<
    IPathWithGatewayId,
    IGetGatewayIngressStatusRsp
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/ingress/sync');

export const updateGatewayIngressStatusApi = createInterface<
    IUpdateGatewayIngressStatusParams,
    boolean
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/ingress/sync');

export const updateGatewayResourceQuotaApi = createInterface<
    IUpdateGatewayResourceQuotaParams,
    boolean
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/resourceQuota');

export const updateGatewayTlsAccApi = createInterface<
    IUpdateGatewayTlsAccParams,
    boolean
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/tlsAcc');

export const updateGatewayMonitorApi = createInterface<
    IUpdateGatewayMonitorParams,
    void
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/monitor');

export const updateGatewayHpaApi = createInterface<
    IUpdateGatewayHpaParams,
    boolean
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/hpa');

export const updateGatewayLogApi = createInterface<
    IUpdateGatewayLogParams,
    boolean
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/log');

export const getLogstoreListApi = createInterface<
    void,
    QueryResponseBase<ILogstoreListItem[]>
>('POST', '/api/bls/v2/log/logstore/list');

export const updateDomainApi = createInterface<
    ICreateOrUpdateDomainParams,
    boolean
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/domain');

export const deleteDomainApi = createInterface<
    IDeleteDomainParams,
    boolean
>('DELETE', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/domain');

export const createDomainApi = createInterface<
    ICreateOrUpdateDomainParams,
    boolean
>('POST', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/domain');

export const getCertificateListApi = createInterfaceIam<
    QueryRequestParamsBase,
    QueryResponseBase<ICertificateListItem[]>
>('POST', '/api/iam/certificate/list');

export const getDomainListApi = createInterface<
    IGetDomainListParams,
    QueryResponseBase<IDomainListItem[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/domainList');

export const deleteGatewayBlbApi = createInterface<
    IDeleteGatewayBlbParams,
    boolean
>('DELETE', '/api/csm/gateway/appblb');

export const addGatewayBlbApi = createInterface<
    IAddGatewayBlbParams,
    boolean
>('POST', '/api/csm/gateway/appblb');

export const createGatewayApi = createInterface<
    ICreateGatewayParams,
    boolean
>('POST', '/api/csm/instance/{serviceMeshInstanceId}/gateway');

export const getGatewayApi = createInterface<
    IPathWithGatewayId,
    IGetGatewayDetail
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}');

export const deleteGatewayApi = createInterface<
    IPathWithGatewayId,
    boolean
>('DELETE', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}');

export const getUnusedEipListApi = createInterfaceBlb<
    QueryRequestParamsBase,
    QueryResponseBase<IUnusedEipItem[]>
>('POST', '/api/blb/eip/unused_eips');

// 注：因blb接口问题，获取blb列表（调用blb后端）换成了2个CSM的后端接口（获取当前网格实例下，可使用的BLB列表 + 获取当前网格所绑定的BLB列表）。
export const getGatewayBlbList = createInterface<
    IPathWithGatewayId,
    QueryResponseBase<IGatewayBlbItem[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/gateway/{gatewayId}/blbList');

export const getAvailableBlbList = createInterface<
    IGetAvailableBlbListParams,
    QueryResponseBase<IAvailableBlbItem[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/availableBlbList');

export const getGatewayListApi = createInterface<
    IGetGatewayListParams,
    QueryResponseBase<IGatewayItem[]>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/gatewayList');
