import {Region} from '@/dicts';
import {SidercarStatus} from '@/dicts/sidecar';
import {IsoTime, QueryResponseBase} from '@/interface';
import {ServiceMeshInstanceId} from '@/interface/serviceMesh';
import createInterface from '@/utils/axios';

interface ParamsServiceMeshInstanceId {
    serviceMeshInstanceId: ServiceMeshInstanceId;
}
export interface QuotaItem {
    limit: number; // 应该是数字
    request: number;
    unit?: string;
}

interface UpdateQuotaItemParams {
    serviceMeshInstanceId: ServiceMeshInstanceId;
    cpuQuota?: QuotaItem;
    memoryQuota?: QuotaItem;
}

export interface SidecarQuotaResponse {
    cpuQuota: QuotaItem;
    memoryQuota: QuotaItem;
}

interface ClusterNamespaceResponse {
    clusterId: string;
    clusterName: string;
    createTime: IsoTime;
    labels: string[];
    namespace: string;
    region: Region;
    status: SidercarStatus;
}

interface UpdateInjectStatusParams {
    serviceMeshInstanceId: string;
    namespace: string;
    clusterUuid: string;
    enabled: boolean;
}

export const updateSidecarQuotaApi = createInterface<
    UpdateQuotaItemParams,
    SidecarQuotaResponse
>('PUT', '/api/csm/instance/{serviceMeshInstanceId}/sidecarQuota');

export const getSidecarQuotaApi = createInterface<
    ParamsServiceMeshInstanceId,
    SidecarQuotaResponse
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/sidecarQuota');

// 获取集群的命名空间
export const getSidecarNamespaceApi = createInterface<
    ParamsServiceMeshInstanceId,
    QueryResponseBase<ClusterNamespaceResponse>
>('GET', '/api/csm/instance/{serviceMeshInstanceId}/namespace');

export const updateInjectStatusApi = createInterface<
    UpdateInjectStatusParams,
    null
>(
    'PUT',
    '/api/csm/instance/{serviceMeshInstanceId}/namespace/{namespace}/sidecarInjection'
    + '?clusterUuid={clusterUuid}&enabled={enabled}'
);
