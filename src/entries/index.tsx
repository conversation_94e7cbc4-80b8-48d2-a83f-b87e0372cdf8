import {StrictMode} from 'react';
import {render} from 'react-dom';
import {<PERSON><PERSON><PERSON><PERSON>outer} from 'react-router-dom';
import ProTable, {ProTableProps} from '@baidu/icloud-ui-pro-table';
import {BrandProvider, ConfigProvider} from '@osui/ui';
import zhCN from 'antd/lib/locale/zh_CN';
import App from '@/modules/App';
import '@/styles';
import FrameworkProvider from '@/components/FrameworkProvider';
import {PROJECT_NAME, CSM_HAS_SLASH} from '@/dicts';
import {getUserInfoApi} from '@/api/authorize';
import ErrorBoundary from '@/components/ErrorBoundary';

if (!CSM_HAS_SLASH) { // 判断 pathname 中 /csm 后面是否有 /
    window.stop(); // 终止正在加载的页面，让用户察觉不到又刷新了一次页面
    location.href = '/csm/';
}

// ProTable全局配置，用于配置字段对应关系
const proTableGlobalConfig: Partial<ProTableProps> = {
    requestConfig: {
        pageSizeKey: 'pageSize',
        currentKey: 'pageNo',
        totalKey: 'totalCount',
        dataKey: 'result',
        formatParams: params => {
            const {order} = params;
            if (order) {
                if (order.toLowerCase() === 'ascend') {
                    params.order = 'asc';
                } else if (order.toLowerCase() === 'descend') {
                    params.order = 'desc';
                }
            }
            return params;
        },
    },
};

const main = (value: any) => render(
    <StrictMode>
        <ConfigProvider locale={zhCN}>
            <BrowserRouter basename={PROJECT_NAME}>
                <FrameworkProvider value={value}>
                    <ProTable.Provider value={proTableGlobalConfig}>
                        <ErrorBoundary>
                            <BrandProvider brand="icloud">
                                <App />
                            </BrandProvider>
                        </ErrorBoundary>
                    </ProTable.Provider>
                </FrameworkProvider>
            </BrowserRouter>
        </ConfigProvider>
    </StrictMode>,
    document.querySelector('#main')
);

window.framework.boot().then(async initData => {
    // @todo 目前用的不多,可以这么写,如果交互比较多直接给initData,然后hooks里面处理,相当于直接接管framework
    const data = {
        currentRegion: initData.region.id,
    };
    window.FRAMEWORK_DATA = data;
    // eslint-disable-next-line no-console
    console.log('initData', initData);
    const res = await getUserInfoApi();
    // TODO 因为开发与沙盒环境不一致，故暂先使用 faas 账号 下的 userId、accountId 作为默认值
    const {
        id: userId = 'c7ac82ae14ef42d1a4ffa3b2ececa17f',
        domain_id: accountId = 'c7ac82ae14ef42d1a4ffa3b2ececa17f',
    } = res ?? {};
    initData.userId = userId;
    initData.accountId = accountId;
    main(initData);
    window.$context = {
        getUserId: () => userId,
    };

    // regionSwitchHandler 是 APP 处理 Region 切换之后逻辑的函数
    window.framework.events.on(window.framework.EVENTS.AFTER_REGION_CHANGED, e => {
        data.currentRegion = e.data;
        initData.region.id = e.data;
        window.FRAMEWORK_DATA = data;
        main({...initData});
    }, this);
});


