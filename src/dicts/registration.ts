import {StageTagType} from '@/interface';

type RegistractionStatusDictInstance = Map<number, {
    text: string;
    value: StageTagType;
}>;

export const RegistractionStatusDict: RegistractionStatusDictInstance = new Map([
    [0, {text: '初始化', value: 'pending'}],
    [1, {text: '创建中', value: 'pending'}],
    [2, {text: '运行中', value: 'success'}],
    [3, {text: '调整中', value: 'pending'}],
    [4, {text: '释放中', value: 'pending'}],
    [5, {text: '运行异常', value: 'warning'}],
    [6, {text: '创建失败', value: 'error'}],
]);

export type RegistractionStatus = keyof typeof RegistractionStatusDict;
