import {isStrExistHost, isStrExisPathname} from '@/utils/environment';
export const PROJECT_NAME = 'csm';
export const CCE_PROJECT_NAME = 'cce';
// 只做类型标注，不要直接当做常量使用
export type Region ='bj'|'su';
export const REGION_GLOBAL = 'global';

export const IS_QASANDBOX = isStrExistHost('qasandbox'); // 是否是沙盒环境
export const IS_LOCALHOST = isStrExistHost('localhost'); // 是否是本地环境
export const CSM_HAS_SLASH = isStrExisPathname(/^(\/csm\/)/); // 是否是 /csm/开头
