// 线上开通服务的参数
export const ROLE_NAME = 'BceServiceRole_csm'; // roleName
export const POLICY_ID = '0b8c85a68082438e8f778765fffbf9c7'; // policyId
export const SERVICE_ID = '2bf77fcc60c0446b8d45abc3bd789ce0'; // serviceId
// 沙盒开通服务的参数
export const QASANDBOX_POLICY_ID = '********************************'; // policyId
export const QASANDBOX_SERVICE_ID = '********************************'; // serviceId

// BLB：线上开通服务的参数
export const ROLE_NAME_BLB = 'BceServiceRole_BLB'; // roleName
export const POLICY_ID_BLB = 'fa53c3d5549d43e69cf6ee754932d191'; // policyId
export const SERVICE_ID_BLB = '9159cff30acf4b18b6b9d33c86c29c09'; // serviceId
// BLB：沙盒开通服务的参数
export const QASANDBOX_POLICY_ID_BLB = '********************************'; // policyId
export const QASANDBOX_SERVICE_ID_BLB = '********************************'; // serviceId

// cce 集群的权限类型
export enum ERole {
    CCE_ADMIN = 'admin',
    CCE_DEVOPS = 'devops',
    CCE_READONLY = 'readonly'
}
