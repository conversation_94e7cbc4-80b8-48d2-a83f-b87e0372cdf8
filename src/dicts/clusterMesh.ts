import {IColumnFilters} from '@baidu/icloud-ui-pro-table';
import {StageTagType} from '@/interface';

interface ClusterStatusDictInstance {
    [key: string]: {
        text: string;
        value: StageTagType;
    };
}
export const ClusterStatusDict: ClusterStatusDictInstance = {
    'provisioning': {text: '创建中', value: 'pending'},
    'pending': {text: '部署中', value: 'pending'},
    'create_failed': {text: '部署失败', value: 'error'},
    'provisioned': {text: '已就绪', value: 'success'},
    'running': {text: '运行中', value: 'success'},
    'deleting': {text: '删除中', value: 'pending'},
    'delete_failed': {text: '删除失败', value: 'error'},
    'deleted': {text: '已删除', value: 'info'},
    'unknown': {text: '未知', value: 'error'},
};

export type ClusterStatus = keyof typeof ClusterStatusDict;

export const MASTER_MODE_FILTERS: IColumnFilters = [
    {text: '托管', value: 'managed'},
    {text: '托管', value: 'managedPro'},
    {text: '独立部署', value: 'custom'},
    {text: '独立部署', value: 'containerizedCustom'},
    {text: 'serverless', value: 'serverless'},
    {text: '未知', value: 'unknown'},
];

export const CONNECTION_FILTERS: IColumnFilters = [
    // {text: '全部', value: 'ALL'},
    {text: '已连通', value: 'connected', state: 'success'},
    {text: '未连通', value: 'not_connected', state: 'pending'},
    {text: '未知', value: 'unknown', state: 'error'},
];
