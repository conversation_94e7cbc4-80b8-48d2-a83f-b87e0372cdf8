import {StageTagType} from '@/interface';
import {TServiceMeshType} from '@/modules/CreateServiceMesh/type';

// Cprom
export const monitorEnabledMap = new Map([
    [false, '关闭'],
    [true, '开启'],
]);

// 服务发现范围配置
export const discoverySelectorEnabledMap = new Map([
    [false, '关闭'],
    [true, '开启'],
]);

// 支持兼容三方协议
export const multiProtocolEnabledMap = new Map([
    [false, '关闭'],
    [true, '开启'],
]);

// TODO BlbStatus相关的ts写法优化
export enum BlbStatusValue {
    notExist = 'notExist',
    pending = 'pending',
    running = 'running',
}
export const BlbStatusDict = {
    notExist: '不存在',
    pending: '创建中',
    running: '运行中',
};
export type BlbStatusType =keyof typeof BlbStatusDict;

export const DEFAULT_VALUE = '--'; // 基本信息默认值

export const SERVICE_MESH_TYPE_DICT: Record<TServiceMeshType, string> = {
    hosting: '托管',
    standalone: '独立',
};
export type ServiceMeshType =keyof typeof SERVICE_MESH_TYPE_DICT;
interface ServiceMeshStatusDictInstance{
    [key: string]: {
        text: string;
        value: StageTagType;
    };
}
export const ServiceMeshStatusDict: ServiceMeshStatusDictInstance = {
    // Running: {text: '运行中', value: 'success'},
    // Deploying: {text: '部署中', value: 'pending'},
    // Deleting: {text: '删除中', value: 'pending'},
    // DeploymentFailed: {text: '部署失败', value: 'error'},
    // RunningAbnormal: {text: '运行异常', value: 'warning'},
    // Deleted: {text: '已删除', value: 'info'},
    // 先增加小写字母的状态 等后端接口都改过来了在删除上方的驼峰字段
    running: {text: '运行中', value: 'success'},
    deploying: {text: '部署中', value: 'pending'},
    abnormal: {text: '运行异常', value: 'warning'},
};

export type ServiceMeshStatus = keyof typeof ServiceMeshStatusDict;

export const ServiceMeshBillingModeDict = {
    free: '-',
    postpaid: '后付费',
    prepaid: '预付费',
};

export type BillingMode = keyof typeof ServiceMeshBillingModeDict;
