import {
    TGatewayBillingModel,
    TGatewayStatus,
    TGatewayDeployMode,
    TGatewayResourceQuota,
    TEipPurchaseType,
    TGatewayType,
    TBlbStatus,
    TBlblistenerItemStatus,
    TDomainProtocol,
    TGatewayLogType,
} from '@/api/gateway';
import {StageTagType} from '@/interface';

export const gatewayLogTypeDict: Record<TGatewayLogType, string> = {
    BLS: '百度日志服务BLS',
    BOS: '对象存储BOS',
};

export const domainProtocolDict: Record<TDomainProtocol, string> = {
    HTTP: 'HTTP',
    HTTPS: 'HTTPS',
};

export const blblistenerItemStatusDict: Record<TBlblistenerItemStatus, { text: string, type: StageTagType }> = {
    available: {
        text: '',
        type: 'success',
    },
};

export const blbStatusDict: Record<TBlbStatus, {text: string, type: StageTagType}> = {
    creating: {
        text: '创建中',
        type: 'info',
    },
    available: {
        text: '运行中',
        type: 'success',
    },
    updating: {
        text: '更新中',
        type: 'info',
    },
    paused: {
        text: '已欠费',
        type: 'error',
    },
    unavailable: {
        text: '暂不可用',
        type: 'error',
    },
};

export const eipPurchaseTypeDict: Record<TEipPurchaseType, string> = {
    '': '全部',
    BGP: '标准型BGP',
    BGP_S: '增强型BGP',
};

export const gatewayGatewayTypeDict: Record<TGatewayType, string> = {
    ingress: '入口',
    egress: '出口',
};

export const gatewayDeployModeDict: Record<TGatewayDeployMode, string> = {
    standalone: '独立型',
    hosting: '托管型',
};

export const gatewayResourceQuotaDict: Record<TGatewayResourceQuota, string> = {
    '1C2G': '1核2G',
    '2C4G': '2核4G',
    '4C8G': '4核8G',
};

export const gatewayBillingModelDict: Record<TGatewayBillingModel, string> = {
    postpaid: '后付费',
    free: '-',
};

export const gatewayStatusDict: Record<TGatewayStatus, {text: string, type: StageTagType}> = {
    deploying: {
        text: '部署中',
        type: 'pending',
    },
    running: {
        text: '运行中',
        type: 'success',
    },
    abnormal: {
        text: '异常',
        type: 'warning',
    },
    deleting: {
        text: '删除中',
        type: 'pending',
    },
};
