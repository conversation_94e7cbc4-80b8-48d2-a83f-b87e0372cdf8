<?xml version="1.0" encoding="UTF-8"?>
<svg width="100px" height="100px" viewBox="0 0 100 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>暂无数据/结果</title>
    <defs>
        <linearGradient x1="16.4979754%" y1="38.5280437%" x2="56.2620383%" y2="63.1848064%" id="linearGradient-1">
            <stop stop-color="#DCE0EF" offset="0%"></stop>
            <stop stop-color="#DCDFEA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="28.8931882%" y1="55.7070718%" x2="93.8984841%" y2="48.875884%" id="linearGradient-2">
            <stop stop-color="#F1F3FA" offset="0%"></stop>
            <stop stop-color="#E8EAF0" offset="100%"></stop>
        </linearGradient>
        <ellipse id="path-3" cx="37" cy="19" rx="37" ry="19"></ellipse>
        <linearGradient x1="21.9251612%" y1="36.7755102%" x2="113.366222%" y2="55.8353584%" id="linearGradient-5">
            <stop stop-color="#FFFFFF" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-41" transform="translate(-30.000000, -504.000000)">
            <g id="暂无数据/结果" transform="translate(30.000000, 504.000000)">
                <rect id="矩形" x="0" y="0" width="100" height="100"></rect>
                <g id="编组-23" transform="translate(13.000000, 21.000000)">
                    <g id="编组-40备份" transform="translate(14.000000, 0.000000)"></g>
                    <g id="编组-15备份" transform="translate(0.000000, 17.000000)">
                        <ellipse id="椭圆形" fill="url(#linearGradient-1)" fill-rule="nonzero" cx="37" cy="21" rx="37" ry="19"></ellipse>
                        <mask id="mask-4" fill="white">
                            <use xlink:href="#path-3"></use>
                        </mask>
                        <use id="蒙版" fill="url(#linearGradient-2)" fill-rule="nonzero" xlink:href="#path-3"></use>
                        <ellipse id="椭圆形" stroke="url(#linearGradient-5)" stroke-width="0.5" fill-rule="nonzero" cx="36.5" cy="18" rx="17.5" ry="9"></ellipse>
                    </g>
                    <path d="M53.119421,22.3479393 C58.8728208,25.6695749 61.5187146,31.7109356 61.5001406,38.3166741 C61.4905577,41.6195434 60.3173522,43.9371817 58.4308377,45.0256273 C56.5349376,46.0953065 53.9257286,45.9264098 51.0537215,44.2749752 L24.380579,29.7230963 C18.6271792,26.4014607 13.9812854,18.3507169 13.9997767,11.7449784 C14.018828,5.13923984 18.7022643,2.48380802 24.4556641,5.80544358 C25.19713,6.22768539 25.910439,6.72499241 26.6049766,7.29736464 C27.2432004,0.550878851 32.4803898,-1.982572 38.8344708,1.67685701 C45.1791662,5.33628602 50.3881986,13.9031032 50.9794942,21.3627085 C51.6646462,21.5879041 52.3873408,21.9256975 53.119421,22.3479393 Z" id="路径" fill="#E0E3EF" fill-rule="nonzero"></path>
                    <path d="M31.1,12.3224613 C31.1,11.8751107 31.4626494,11.5124613 31.91,11.5124613 C32.0712677,11.5124613 32.2288647,11.5606 32.3626072,11.6507123 L36.1426072,14.1975792 C36.3660417,14.3481237 36.5,14.5999092 36.5,14.8693282 L36.5,21.6 L35.567,21.0094613 L36.5,21.6383813 L36.5,27.328706 C36.5,27.7760567 36.1373506,28.138706 35.69,28.138706 C35.5366236,28.138706 35.3863993,28.0951595 35.2568008,28.0131317 L31.4768008,25.6206251 C31.2422016,25.4721383 31.1,25.2138415 31.1,24.9361995 L31.1,12.3224613 Z" id="形状结合" fill="#FFFFFF"></path>
                    <path d="M40.4626072,24.2507123 L44.2426072,26.7975792 C44.4660417,26.9481237 44.6,27.1999092 44.6,27.4693282 L44.6,32.728706 C44.6,33.1760567 44.2373506,33.538706 43.79,33.538706 C43.6366236,33.538706 43.4863993,33.4951595 43.3568008,33.4131317 L39.5768008,31.0206251 C39.3422016,30.8721383 39.2,30.6138415 39.2,30.3361995 L39.2,24.9224613 C39.2,24.4751107 39.5626494,24.1124613 40.01,24.1124613 C40.1712677,24.1124613 40.3288647,24.1606 40.4626072,24.2507123 Z" id="矩形" fill="#F7F8FB"></path>
                </g>
            </g>
        </g>
    </g>
</svg>