import urls from '@/urls';

// 产品文档、首页的外链接
export const BLB_DOC_LINK = urls.external.blbDoc.path(); // blb 文档
export const CCE_DOC_LINK = urls.external.cceDoc.path(); // cce 文档
export const BLB_HOME_LINK = urls.external.blbHome.path(); // blb 首页
export const CCE_HOME_LINK = urls.external.cceHome.path(); // cce 首页
export const CPROM_HOME_LINK = urls.external.cPromHome.path(); // CProm首页
export const CSM_DOC_QUICK_START = urls.external.csmDocQuickStart.path(); // csm快速入门文档
export const CSM_DOC_AGREEMENT = urls.external.csmDocAgreement.path(); // csm免责声明文档
export const CSM_DOC_CREATE_MESH_INSTANCE = urls.external.csmDocCreateMeshInstance.path(); // csm创建网格实例文档
export const CSM_DOC_CLUSTER_MANAGE = urls.external.csmDocClusterManage.path(); // csm集群纳管文档
export const CSM_DOC_SIDECAR_INJECTION = urls.external.csmDocSidecarInjection.path(); // csm Sidecar注入文档
export const CSM_DOC_ISTIO_MANAGE = urls.external.csmDocIstioManage.path(); // csm istio资源管理文档
export const VPC_HOME = urls.external.vpcHome.path(); // vpc 首页
export const CSN_HOME = urls.external.csnHome.path(); // csn 首页
export const SERVICE_EXAMPLE_LINK = urls.external.serviceExample.path(); // 参考服务示例
// cce 外链接
export const CCE_CLUSTER_LIST_LINK = urls.external.cceClusterList.path(); // cce 集群列表
export const CCE_CLUSTER_RBAC_LINK = urls.external.cceClusterRbac.path(); // cce 集群权限管理

export const OVERVIEW_GUIDE_LINK = urls.overview.guide.fill(); // 引导页
export const OVERVIEW_DASHBOARD_LINK = urls.overview.dashboard.fill(); // 数据大盘页
export const CREATE_SERVICE_MESH_LINK = urls.createServiceMesh.fill(); // 创建网格页
export const SUBSCRIBE_SERVICE_LINK = urls.subscribeService.fill(); // 网格服务开通页
// @todo 实例详情的二级页有可能也不显示地域。如果要做支持传一个回调函数，startswith来匹配。
export const REGION_SWITCHER_BLACK_LIST = [
    OVERVIEW_GUIDE_LINK,
    OVERVIEW_DASHBOARD_LINK,
    CREATE_SERVICE_MESH_LINK,
    SUBSCRIBE_SERVICE_LINK,
];
export const SERVICE_MESH_INSTANCE_INFO_PATH = urls.serviceMeshBaseInfo; // 网格 实例详情页

export const SERVICE_MESH_ADMIN_INSTANCE_PATH = urls.admin.serviceInstanceList; // 网格 服务管理服务实例列表
export const SERVICE_MESH_ADMIN_INSTANCE_BASE_PATH = urls.admin.serviceInstanceBaseInfo; // 网格 服务管理服务实例详情
export const SERVICE_MESH_ADMIN_PATH = urls.admin.serviceList; // 网格 服务管理列表页
export const SERVICE_MESH_SERVICE_LIST_PATH = urls.serviceList; // 网格 服务列表页
export const SERVICE_MESH_CLUSTER_LIST_PATH = urls.cluster.list; // 网格 集群纳管页
export const SERVICE_MESH_LIST_LINK = urls.serviceMeshList.fill(); // 网格 列表页
export const OVERVIEW_LINK = urls.overview.base.fill(); // 概览页
export const REGISTRATION_LIST = urls.registrationList.fill();
export const REGISTRATION_CREATE = urls.createRegistration.fill();
