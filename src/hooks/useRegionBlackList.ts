import {useLocation} from 'react-router-dom';
import {REGION_SWITCHER_BLACK_LIST} from '@/links';

// 添加需要隐藏顶部导航地域
export const setRegionBlackList = (pathname: string) => REGION_SWITCHER_BLACK_LIST.push(pathname);

const useRegionBlackList = () => {
    const {pathname} = useLocation();
    const hideRegionRouteRegList = [
        /^\/service-mesh-instance\/[^/]+\/cluster/,
        /^\/service-mesh-instance\/[^/]+\/associate-cluster/,
    ];
    // 如果当前路径能匹配上数组中的任何一个规则，则隐藏当前页面的导航地域
    hideRegionRouteRegList.some(routeReg => routeReg.test(pathname)) && setRegionBlackList(pathname);
    // @todo 优化
    const switcher = document.querySelector('.header-select.region') as HTMLElement;
    if (switcher) {
        switcher.style.display = REGION_SWITCHER_BLACK_LIST.includes(pathname) ? 'none' : 'flex';
    }
};
export default useRegionBlackList;
