import {TableRowSelection} from 'antd/lib/table/interface';
import {useCallback, useMemo, useState} from 'react';

export function useListTotal(api: (params: any) => Promise<any>) {
    const [total, setTotal] = useState(0);
    const [data, setData] = useState([]);
    const request = useCallback(
        async params => {
            const res = await api(params);
            setTotal(res?.totalCount);
            setData(res?.result);
            return res;
        },
        [api]
    );
    return {request, total, data};
}
export function useListSelectedRow(props: TableRowSelection<Record<string, any>>) {
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const rowSelection: TableRowSelection<Record<string, any>> = useMemo(
        () => ({
            preserveSelectedRowKeys: true,
            selectedRowKeys,
            onChange: selectedRowKeys => setSelectedRowKeys(selectedRowKeys),
            ...props,
        }),
        [props, selectedRowKeys]
    );

    return {selectedRowKeys, rowSelection, setSelectedRowKeys};
}
