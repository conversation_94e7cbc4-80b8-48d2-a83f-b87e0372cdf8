import {useEffect, useMemo, useState} from 'react';
import {useRequestCallback} from 'huse';
import {getClusterRbacApi, TRole, IRoleClusterData} from '@/api/cceRequest';
import {useFramework} from './useFramework';
export interface IClusterRbac {
    [key: string]: boolean;
}
interface ICCEClusterData {
    data: IClusterRbac;
    pending: boolean;
    getClusterRbac(): any;
}

/**
 * 获取 cce 有相应权限的集群
 * @param role 权限 admin-管理员 | devops-运维开发 | readonly-只读
 * @returns 【全地域】有权限的集群列表
 */
export const useCCEClusterListWithPermissionInAllRegion = (role: TRole): string[] => {
    const {account: {userId = ''}, region: {regionList = {}}} = useFramework();
    const [clusterList, setClusterList] = useState<string[]>([]);

    useEffect(
        () => {
            (async () => {
                const promiseList = [];
                // TODO 可考虑使用 Object.keys(regionList).map(...)，但需回归测试。
                for (const key in regionList) {
                    if (regionList.hasOwnProperty(key)) {
                        promiseList.push(
                            getClusterRbacApi({userID: userId, _config: {region: key}})
                        );
                    }
                }

                // 发送全部的请求
                const resList = await Promise.all(promiseList);
                const tempClusterList: string[] = [];
                resList.forEach(res => {
                    if (res?.data?.length) {
                        const filterData: IRoleClusterData[] = res?.data?.filter(
                            v => v.role === `cce:${role}`
                        );
                        tempClusterList.push(...filterData?.map(v => v.clusterID));
                    }
                });
                setClusterList(tempClusterList);
            })();
        },
        [regionList, role, userId]
    );

    return clusterList;
};

/**
 * 获取 cce 有相应权限的集群
 * @param role 权限 admin-管理员 | devops-运维开发 | readonly-只读
 * @returns 有权限的集群对象集合 {clusterID: boolean}
 */
export const useCCEClusterRole = (role: TRole): ICCEClusterData => {
    const {account: {userId}} = useFramework();
    const [getClusterRbac, {data: res, pending}] = useRequestCallback(getClusterRbacApi, {userID: userId});
    const clusterList = useMemo<IClusterRbac>(
        () => {
            if (!pending && res?.data?.length) {
                const filterData: IRoleClusterData[] = res?.data?.filter(
                    v => v.role === `cce:${role}`
                );
                return Object.fromEntries(filterData?.map(v => [v.clusterID, true]) || []);
            }
            return {};
        },
        [res, role, pending]
    );
    return {data: clusterList, pending, getClusterRbac}; // 将请求函数暴露出去，由外部决定什么时候去请求数据
};
