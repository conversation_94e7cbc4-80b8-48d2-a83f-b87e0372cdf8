import {useCallback, useEffect} from 'react';

const visibilityChangeEvent = 'visibilitychange';

interface Props {
  onHide?: () => void;
  onShow?: () => void;
}

const useVisibilityChange = ({
    onHide = () => {},
    onShow = () => {},
}: Props = {}) => {
    const handleVisibilityChange = useCallback(
        () => {
            const isHidden = document.visibilityState === 'hidden';
            if (isHidden) {
                onHide();
            } else {
                onShow();
            }
        },
        [onHide, onShow]
    );


    useEffect(
        () => {
            document.addEventListener(visibilityChangeEvent, handleVisibilityChange);
            return () =>
                document.removeEventListener(
                    visibilityChangeEvent,
                    handleVisibilityChange
                );
        },
        [handleVisibilityChange]
    );


};

export default useVisibilityChange;
