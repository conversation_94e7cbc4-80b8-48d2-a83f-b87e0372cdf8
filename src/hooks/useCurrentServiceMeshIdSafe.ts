import {isNil} from 'lodash';
import {useParams} from 'react-router-dom';

export const useCurrentServiceMeshIdSafe = () => {
    const {serviceMeshInstanceId} = useParams<{serviceMeshInstanceId: string}>();
    if (!isNil(serviceMeshInstanceId)) {
        return serviceMeshInstanceId;
    }
    throw new Error('当前不能获取到 service mesh id！');
};

export const useCurRegistrationId = () => {
    const {registrationId} = useParams<{registrationId: string}>();
    if (!isNil(registrationId)) {
        return registrationId;
    }
    throw new Error('当前不能获取到 registration instance id！');
};
