import {useContext} from 'react';
import {FrameworkContext} from '@/components/FrameworkProvider/index';
import {PROJECT_NAME} from '@/dicts';
export interface RegionList { // 导航返回的地区数据 类型
    [key: string]: string;
}
export interface LabelValue { // datasource数据 类型
    label: string;
    value: string;
}
export interface Region { // Region 返回的 类型
    currentRegion: string;
    regionList: RegionList;
    datasource: LabelValue[];
    getRegionList: (projectName: string) => RegionList;
    setRegion(regionId: string): any;
    setRequestHeaderRegion(regionId: string): any;
    onRegionChange(callback: (e: any) => void): any;
    unRegionChange(callback: (e: any) => void): any;
}
export const region = (data: any): Region => { // 获取导航的 地区列表和当前地区 以及处理好的 datasource
    const currentRegion = data?.region?.id || 'global';
    let regionList: RegionList = {};
    if (data?.constants?.SERVICE_TYPE) {
        regionList = data?.constants?.SERVICE_TYPE[PROJECT_NAME.toUpperCase()]?.region || {};
    }
    const datasource = Object.entries(regionList)
        .map<LabelValue>(([k, v]: [string, any]) => ({label: v, value: k}));
    const setRegion = (regionId: string) => window?.framework?.region?.setRegion(regionId); // 设置导航地域
    const setRequestHeaderRegion = (regionId: string) => (window.FRAMEWORK_DATA.currentRegion = regionId);
    const getRegionList = (projectName = PROJECT_NAME) => {
        return data?.constants?.SERVICE_TYPE[projectName.toUpperCase()]?.region;
    };
    const handleRegionChangeAfter = (callback: (arg: any) => void) => {
        window.framework.events.on(window.framework.EVENTS.AFTER_REGION_CHANGED, callback, this);
    };
    const unHandleRegionChangeAfter = (callback: (...args: any[]) => void) => {
        window.framework.events.un(window.framework.EVENTS.AFTER_REGION_CHANGED, callback, this);
    };
    return {
        currentRegion, // 当前 地域
        regionList, // 地域列表
        datasource, // 地域 datasource 数据
        getRegionList,
        setRegion, // 设置导航地域
        setRequestHeaderRegion, // 设置 请求都中的 header
        onRegionChange: handleRegionChangeAfter,
        unRegionChange: unHandleRegionChangeAfter,
    };
};
export const account = (data: any) => {
    const {userId = '', accountId = ''} = data;
    const verifyStatus = data?.constants?.verifyUser?.verifyStatus === 'PASS';

    return {
        accountId,
        userId,
        verifyStatus, // 是否个人认证
    };
};
export const useFramework = () => {
    const context = useContext(FrameworkContext);

    return {
        region: region(context),
        account: account(context),
    };
};
