import {useLocation} from 'react-router-dom';
import qs from 'query-string';

export function useQuery<T>(): T {
    const {search} = useLocation();
    return qs.parse(search) as T;
}

export function getUrlQuery<T>(url: string, key: string): T {
    return new URLSearchParams(url).get(key) as T;
}

export function getUrlSearchByUpdateQuery(key: string, value: string, {urlSearch = location.search} = {}): string {
    const searchParams = new URLSearchParams(urlSearch);
    searchParams.set(key, value);
    return `?${searchParams.toString()}`;
}
