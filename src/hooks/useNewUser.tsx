import {useCallback, useEffect, useState} from 'react';
import {isNil} from 'lodash';
import {getServiceMeshListApi} from '@/api/serviceMesh';
import {useFramework} from './useFramework';

export function useNewUser() {
    const {account: {userId}} = useFramework();
    const USER_ID_KEY = `${userId}:_is_csm_new_user`;
    const storageStatus = localStorage.getItem(USER_ID_KEY) === null
        || localStorage.getItem(USER_ID_KEY) === 'true';
    const [isNewUser, setIsNewUser] = useState(storageStatus);

    const fetchData = useCallback(
        async () => {
            const data = await getServiceMeshListApi({});
            if (isNil(data?.result) || data?.result.length === 0) {
                localStorage.setItem(USER_ID_KEY, 'true');
                setIsNewUser(true);
            } else {
                setIsNewUser(false);
                localStorage.setItem(USER_ID_KEY, 'false');
            }
        },
        [USER_ID_KEY]
    );
    useEffect(
        () => {
            if (storageStatus) {
                fetchData();
            } else {
                setIsNewUser(false);
            }
        },
        [fetchData, storageStatus]
    );

    return isNewUser;
}
