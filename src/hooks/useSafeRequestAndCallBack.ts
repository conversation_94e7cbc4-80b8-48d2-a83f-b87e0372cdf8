import {useRequestCallback, Request} from 'huse';
import {useCallback, useEffect} from 'react';

// 针对请求参数尚未ready的场景
export function useSafeRequestAndCallBack<Params, Response>(
    api: Request<Params, Response>,
    params: Partial<Params>,
    shouldRequest: boolean
) {
    const [request, data] = useRequestCallback<Params, Response>(api, params as Params);
    useEffect(
        () => {
            if (shouldRequest) {
                request();
            }
        },
        [shouldRequest, request]
    );

    const safeRequest = useCallback(
        () => {
            if (shouldRequest) {
                request();
            }
        },
        [request, shouldRequest]
    );

    return {request: safeRequest, data};
}
