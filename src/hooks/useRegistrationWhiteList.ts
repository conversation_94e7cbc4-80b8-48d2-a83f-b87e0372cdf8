import {useEffect} from 'react';
import {useLocation, useHistory} from 'react-router-dom';
import {useRequestCallback} from 'huse';
import {isUserInCseWhiteListApi} from '@/api/whiteList';
import {useFramework} from '@/hooks';
import urls from '@/urls';

const useRegistrationWhiteList = () => {
    const {pathname} = useLocation();
    const history = useHistory();
    const {region: {currentRegion}} = useFramework();
    const [request, {pending, data}] = useRequestCallback(isUserInCseWhiteListApi, {featureType: 'EnableCSE'});
    useEffect(
        () => {
            request();
        },
        [request, currentRegion]
    );
    // 注册中心的url需要校验白名单
    const hideRegionRouteRegList = [
        /^\/registration/,
        /^\/service-admin-list/,
        /^\/create-registration/,
    ];
    // 如果当前路径能匹配上数组中的任何一个规则，则跳转首页url
    const isWhiteUrl = hideRegionRouteRegList.some(routeReg => routeReg.test(pathname));
    useEffect(
        () => {
            if (!pending && isWhiteUrl && data && !data.isExist) {
                history.push(urls.serviceMeshList.fill());
            }
        },
        [pending, isWhiteUrl, data, history]
    );
    return {inRegistrationWhite: data?.isExist};
};
export default useRegistrationWhiteList;
