import {generatePath} from 'react-router-dom';
import {StringifyOptions, stringify} from 'query-string';

export class URLTemplate {
    routerPath = '';

    constructor(routerPath: string) {
        this.routerPath = routerPath;
    }

    fill(params: Record<string, any> = {}, query?: Record<string, any>, config: StringifyOptions = {}) {
        const path = generatePath(this.routerPath, params);
        return query ? path + `?${stringify(query, {encode: false, ...config})}` : path;
    }

    path() {
        return this.routerPath;
    }
}

export const withPrefix = (prefix: string = '') => {
    return (routerPath: string) => new URLTemplate(prefix + routerPath);
};
