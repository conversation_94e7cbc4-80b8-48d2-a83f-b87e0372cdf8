import {withPrefix} from './routers';

const istioUrlPrefix = withPrefix('https://istio.io/latest/docs');
const istioUrlByVersionPrefix = withPrefix('https://istio.io/:version/docs');
const blsUrlPrefix = withPrefix('https://console.bce.baidu.com/bls/#/bls');
const iamUrlPrefix = withPrefix('https://console.bce.baidu.com/iam/#/iam');
const cceUrlPrefix = withPrefix('https://console.bce.baidu.com/cce/#/cce');
const vpcUrlPrefix = withPrefix('https://console.bce.baidu.com/network/#/vpc');
const blbUrlPrefix = withPrefix('https://console.bce.baidu.com/blb/#');
const eipUrlPrefix = withPrefix('https://console.bce.baidu.com/eip/#');
const csm = withPrefix('');
const serviceMeshInstanceId = withPrefix('/service-mesh-instance/:serviceMeshInstanceId');
const registrationInstanceId = withPrefix('/registration-instance/:registrationId');
const cloudUrlPrefix = withPrefix('https://cloud.baidu.com'); // cloud 前缀
const cloudUrlBlsPrefix = withPrefix('https://cloud.baidu.com/doc/BLS/s'); // cloud 前缀
const csmUrlPrefix = withPrefix('https://cloud.baidu.com/doc/CSM/s'); // csm文档 前缀
const cpromUrlPrefix = withPrefix('https://console.bce.baidu.com/cprom/#'); // 监控前缀
const csnUrlPrefix = withPrefix('https://console.bce.baidu.com/csn/#/csn'); // csn 前缀
const productPricePrefix = withPrefix('https://cloud.baidu.com/product-price/'); // 各个产品定价文档地址

export default {
    subscribeService: csm('/subscribe-service'),
    createServiceMesh: csm('/create-service-mesh'),
    serviceMeshInstanceId: serviceMeshInstanceId('/'),
    home: csm('/'),
    registrationInstanceId: registrationInstanceId('/'),

    crd: {
        list: serviceMeshInstanceId('/crd'),
        create: serviceMeshInstanceId('/crd/create'),
        view: serviceMeshInstanceId('/namespace/:namespace/crd/view'),
        update: serviceMeshInstanceId('/namespace/:namespace/crd/update'),
    },
    gateway: {
        list: serviceMeshInstanceId('/gateway'),
        create: serviceMeshInstanceId('/gateway/create'),
        detail: serviceMeshInstanceId('/gateway/:gatewayId/:tabId'),
        blb: {
            add: serviceMeshInstanceId('/gateway/:gatewayId/blb/add'),
        },
    },
    cluster: {
        associate: serviceMeshInstanceId('/associate-cluster'),
        list: serviceMeshInstanceId('/cluster'),
    },
    overview: {
        base: csm('/overview'),
        guide: csm('/overview/guide'),
        dashboard: csm('/overview/dashboard'),
    },
    cprom: serviceMeshInstanceId('/cprom'),
    trace: serviceMeshInstanceId('/trace'),
    diagnosis: serviceMeshInstanceId('/diagnosis'),
    serviceMeshList: csm('/service-mesh-list'),

    registrationList: csm('/registration-list'),
    createRegistration: csm('/create-registration'),
    registrationBaseInfo: registrationInstanceId('/base-info'),
    registrationNamespaces: registrationInstanceId('/namespaces'),
    registrationMonitor: registrationInstanceId('/monitor'),
    registrationParams: registrationInstanceId('/params'),

    admin: {
        serviceList: csm('/service-admin-list'),
        serviceInstanceList: csm('/service-admin-list/instance'),
        serviceInstanceBaseInfo: csm('/service-admin-list/instance/base-info'),
    },

    serviceMeshBaseInfo: serviceMeshInstanceId('/base-info'),
    serviceDetail: serviceMeshInstanceId('/service/:host'),
    serviceList: serviceMeshInstanceId('/service'),
    sidecar: serviceMeshInstanceId('/sidecar'),
    swimLaneGroup: serviceMeshInstanceId('/swimLaneGroup'),
    blsLog: serviceMeshInstanceId('/blsLog'),
    compList: serviceMeshInstanceId('/comp-list'),

    // 外链
    external: {
        doc: {
            bls: {
                help: cloudUrlBlsPrefix('/ak0dp97w3'),
                price: cloudUrlBlsPrefix('/akz0xh9c6'),
            },
        },
        istio: {
            extraordinary: istioUrlPrefix('/reference/config/analysis/'),
            extraordinaryCode: istioUrlByVersionPrefix('/reference/config/analysis/:code'),
            prerequisites: istioUrlPrefix('/setup/platform-setup/prerequisites/'),
            supportStatusOfIstioReleases:
                istioUrlPrefix('/releases/supported-releases/#support-status-of-istio-releases'),
        },
        bls: {
            logSearch: blsUrlPrefix('/search/log'),
            logDetail: blsUrlPrefix('/log/detail'),
            logList: blsUrlPrefix('/log/list'),
        },
        iam: {
            certList: iamUrlPrefix('/cert/list'),
        },
        price: {
            blb: productPricePrefix('blb.html'),
            eip: productPricePrefix('eip.html'),
        },
        cloudAgreement: cloudUrlPrefix('/doc/Agreements/s/Ojwvy1v7a'),
        addToWhiteList: cloudUrlPrefix('/survey/csmapply.html'),
        cce: {
            deploymentList: cceUrlPrefix('/application/deployment/list'),
            installComponent: cceUrlPrefix('/ai/helm/release'),
        },
        cprom: {
            grafanaList: cpromUrlPrefix('/grafana/list'),
            grafanaDetail: cpromUrlPrefix('/grafana/detail'),
            clusterCollect: cpromUrlPrefix('/instance/cluster-collect'),
            cpromInstanceCluster: cpromUrlPrefix('/instance/cluster'),
            cpromInstanceList: cpromUrlPrefix('/instance/list'),
        },
        clusterDetail: cceUrlPrefix('/cluster/detail'),
        instanceDetail: vpcUrlPrefix('/instance/detail'),
        subnetDetail: vpcUrlPrefix('/subnet/detail'),
        enterpriseSecurityList: vpcUrlPrefix('/enterpriseSecurity/list'),
        blbDetail: blbUrlPrefix('/appblb/detail'),
        blbCreate: blbUrlPrefix('/blb/create'),
        serviceDetail: cceUrlPrefix('/flow/service/detail'),
        namespaceDetail: cceUrlPrefix('/application/namespace/detail'),
        serviceExample: csmUrlPrefix('/dlgw09203#服务示例'),
        sidecarPodFailed: csmUrlPrefix('/Eloguezvd#注入-sidecar-pod-启动失败'),
        // cce 链接
        // https://console.bce.baidu.com/cce/#/cce/cluster/master/upgrade?clusterUuid=cce-hs78sa89
        cceClusterUpgrade: cceUrlPrefix('/cluster/master/upgrade'),
        cceClusterList: cceUrlPrefix('/cluster/list'), // cce集群列表
        cceClusterRbac: cceUrlPrefix('/cluster/rbac'), // cce权限管理
        // 文档和首页外链
        cpromCostDetail: cloudUrlPrefix('/doc/CProm/s/Gl0gcsh46'),
        blbDoc: cloudUrlPrefix('/doc/BLB/index.html'), // blb文档
        cceDoc: cloudUrlPrefix('/doc/CCE/index.html'), // cce文档
        vpcDoc: cloudUrlPrefix('/doc/VPC/index.html'), // vpc文档
        blbHome: cloudUrlPrefix('/product/blb.html'), // blb首页
        cceHome: cloudUrlPrefix('/product/cce.html'), // cce首页
        cPromHome: cpromUrlPrefix('/'), // 监控首页
        instanceAlarm: cpromUrlPrefix('/instance/alarm'),
        cpromInstanceList: cpromUrlPrefix('/instance/list'), // CProm控制台
        cpromCreate: cpromUrlPrefix('/create'),
        cpromInstanceDetail: cpromUrlPrefix('/instance/detail'),
        csmObservability: cloudUrlPrefix('/doc/CSM/index.html'), // 可观测配置
        csmDocProduct: csmUrlPrefix('/olgw091au'), // csm 产品文档
        csmDocQuickStart: csmUrlPrefix('/Jlgw09182'), // csm 文档 快速入门
        csmDocAgreement: csmUrlPrefix('/Qlgw091ql'), // csm 文档 免责声明协议
        csmDocCancelBeta: csmUrlPrefix('/vlgw091ns'), // csm 文档 取消公测
        csmDocCreateMeshInstance: csmUrlPrefix('/jlgw092hr'), // csm 文档 创建网格实例
        csmDocClusterManage: csmUrlPrefix('/6lgw09231'), // csm 文档 集群管理
        csmDocSidecarInjection: csmUrlPrefix('/Plgw092ez'), // csm 文档 Sidecar注入
        csmDocIstioManage: csmUrlPrefix('/nlgw0928y'), // csm 文档 istio资源管理
        csmDocCprom: csmUrlPrefix('/Slgw092c0'), // csm 文档 运维监控网格
        swimLaneGroup: csmUrlPrefix('/zltpp2r1v'), // csm 流量泳道、全链路灰度
        // TODO 补充链接
        csmDosTrace: csmUrlPrefix('/'), // csm 链路追踪
        vpcHome: vpcUrlPrefix('/peerconn/list'), // vpc 对等连接
        csnHome: csnUrlPrefix('/auth'), // csn 首页
        eniList: vpcUrlPrefix('/eni/list'), // 弹性网卡列表
        eipDetail: eipUrlPrefix('/eip/instance/detail'),
    },
};
