import {ReactNode, useCallback} from 'react';
import {getUrlQuery} from '@/hooks/useUrl';
import {useFramework} from '@/hooks';

interface Iprops extends React.HTMLAttributes<HTMLElement> {
    href?: string;
    value: string | number | ReactNode;
}

// 跳转外部链接
export default function ExternalLink({href = '', value, ...props}: Iprops) {
    const {region: {currentRegion, setRegion}} = useFramework();

    // 边界：href的query中可以传入 region 信息（没传就使用当前页面下的region，即 currentRegion ），
    // 每次跳转其他产品链接之前需要切换至目标 region ，再跳转链接。
    const clickExternalLink = useCallback(
        () => {
            const regionQuery = getUrlQuery<string>(href, 'region');
            if (regionQuery && regionQuery !== currentRegion) {
                setRegion(regionQuery || currentRegion).then(() => {
                    window.open(href, '_blank');
                });
                // 边界：得重新切回原先的地域，不然当前资源与地域对不上（注：需延迟几秒后再切回）。
                setTimeout(() => {
                    setRegion(currentRegion);
                }, 3 * 1000);
            }
            else {
                window.open(href, '_blank');
            }
        },
        [currentRegion, href, setRegion]
    );

    return (
        <a
            {...props}
            rel="noreferrer"
            onClick={clickExternalLink}
        >
            {value}
        </a>
    );
}
