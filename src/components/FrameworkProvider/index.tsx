import React, {createContext} from 'react';

export interface IFrameworkContext {
    currentRegion: string;
    regionList: object;
    accountId: string;
    userId: string;
    verifyStatus: string;
    constants: object;
}
const initialState: IFrameworkContext = {
    currentRegion: 'bj',
    regionList: {},
    accountId: '',
    userId: '',
    verifyStatus: '',
    constants: {},
};

export const FrameworkContext = createContext(initialState);

interface Props {
    value: any;
    children?: React.ReactNode;
}
export default function FrameworkProvider({value, children}: Props) {
    return (
        <FrameworkContext.Provider value={value}>
            {children}
        </FrameworkContext.Provider>
    );
}
