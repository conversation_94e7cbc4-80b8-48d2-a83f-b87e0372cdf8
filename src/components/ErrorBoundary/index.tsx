import {Result} from '@osui/ui';
import {useCallback} from 'react';
import * as <PERSON><PERSON> from '@baidu/weirwood-sdk';
import {Boundary} from 'react-suspense-boundary';
interface IProps {
    children: React.ReactNode;
}

function renderError(error: Error) {
    return (
        <Result
            status="error"
            title={error.message}
        />
    );
}
const options = {
    common: {
        // 只支持如下字符：英文字母(大小写)，数字(0-9)，下划线(_)，中划线(-)，点(.)，@符号
        buildid: '<buildid>',
        token: '89c26ad912a04c2c980ac912379684b2',
        ignoreUrls: [
            // 本地开发屏蔽错误发送
            'localhost',
            '127.0.0.1',
        ],
    },
    error: {
        collectWindowErrors: true,
        collectUnhandledRejections: true,
        // 静态资源加载异常
        collectResourceLoadErrors: true,
    },
    perf: {
        // 性能数据PV日志会比较大，可以输入 sampleRate 进行采样，控制在 50 W左右
        sampleRate: 1,
        spa: true,
        history: true,
    },
};

const weirwood = Weirwood.init(options);

export default function ErrorBoundary({children}: IProps) {
    const errorCaught = useCallback(
        (error: Error) => {
            weirwood.error.captureException(error);
        },
        []
    );
    return (
        <Boundary renderError={renderError} onErrorCaught={errorCaught}>
            {children}
        </Boundary>
    );
}
