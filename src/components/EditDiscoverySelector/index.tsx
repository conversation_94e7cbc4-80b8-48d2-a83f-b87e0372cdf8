import React, {useCallback, useMemo, useState} from 'react';
import Card from '@baidu/icloud-ui-card';
import styled from 'styled-components';
import {Button, message} from '@osui/ui';
import {OutlinedEditingSquare} from '@baidu/acud-icon';
import {useBoolean} from 'huse';
import {discoverySelectorEnabledMap} from '@/dicts/serviceMesh';
import {IDiscoverySelector} from '@/api/createServiceMesh';
import {updateDiscoverySelectorApi} from '@/api/serviceMesh';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {getDiscoverySelectorViewData} from '@/api/util';
import EditModal from './EditModal';

const MatchLabelsStyled = styled.span`
    margin-left: 12px;
`;
const EditButtonStyled = styled(Button)`
    margin-left: 12px !important;
`;

interface IProps {
    discoverySelector: IDiscoverySelector;
}

export default function EditDiscoverySelector({discoverySelector: discoverySelectorFromProps}: IProps) {
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const discoverySelectorViewData = useMemo(
        () => getDiscoverySelectorViewData(discoverySelectorFromProps),
        [discoverySelectorFromProps]
    );
    const [discoverySelector, setDiscoverySelector] = useState(discoverySelectorViewData);

    const [visible, {on: onVisible, off: offVisible}] = useBoolean(false);
    const [confirmLoading, {on: onConfirmLoading, off: offConfirmLoading}] = useBoolean(false);
    const onOk = useCallback(
        async (discoverySelector: IDiscoverySelector) => {
            try {
                let {matchLabels = []} = discoverySelector ?? {};
                matchLabels = (matchLabels as string[][]).reduce(
                    (acc: Record<string, string>, cur: string[]) => {
                        acc[cur[0]] = cur[1];
                        return acc;
                    }, {});
                const newDiscoverySelector = {...discoverySelector, matchLabels};
                onConfirmLoading();
                await updateDiscoverySelectorApi({serviceMeshInstanceId, ...newDiscoverySelector});
                message.success('服务发现范围配置编辑成功。');
                setDiscoverySelector(newDiscoverySelector);
            }
            catch (error) {
                message.error('服务发现范围配置编辑失败，请重新操作。');
            } finally {
                offConfirmLoading();
                offVisible();
            }
        },
        [offConfirmLoading, offVisible, onConfirmLoading, serviceMeshInstanceId]
    );
    const editDiscoverySelector = useCallback(
        () => {
            onVisible();
        },
        [onVisible]
    );

    return (
        <>
            <Card.Field title="服务发现范围配置">
                {discoverySelectorEnabledMap.get(discoverySelector.enabled)}
                {
                    discoverySelector.enabled
                    && (
                        <MatchLabelsStyled>
                            命名空间标签：
                            {
                                discoverySelector.enabled
                            && Object.entries(discoverySelector.matchLabels).map(([key, val]) => {
                                return (
                                    <span key={key}>
                                        {key} : {val}
                                    </span>
                                );
                            })
                            }
                        </MatchLabelsStyled>
                    )
                }
                <EditButtonStyled type="link" onClick={editDiscoverySelector}>
                    <OutlinedEditingSquare />
                </EditButtonStyled>
            </Card.Field>

            <EditModal
                visible={visible}
                discoverySelector={discoverySelector}
                confirmLoading={confirmLoading}
                onCancel={offVisible}
                onOk={onOk}
            />
        </>
    );
}

