import {useCallback, useMemo, useState} from 'react';
import {Alert, Form, Input, Modal, Popover, Space, Switch} from '@osui/ui';
import {QuestionCircleOutlined} from '@ant-design/icons';
import styled from 'styled-components';
import _ from 'lodash';
import {IDiscoverySelector} from '@/api/createServiceMesh';
import {discoverySelectorTip, namespaceLabelTip} from '@/modules/CreateServiceMesh/constants';
import {labelsKeyValidate, labelsValueValidate} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/formValidate';
import c from './editModal.less';

const AlertStyled = styled(Alert)`
    margin-bottom: 16px;
`;
const SpaceStyled = styled(Space)`
    display: flex;
`;
const LabelInputStyled = styled(Input)`
    width: 160px;
`;
const LabelSplitStyled = styled.div`
    margin: 0 8px;
`;
const ModalStyled = styled(Modal)`
    width: 720px !important;
`;

interface IProps {
    visible: boolean;
    discoverySelector: IDiscoverySelector;
    confirmLoading: boolean;
    onCancel: () => void;
    onOk: (discoverySelector: IDiscoverySelector) => void;
}

export default function EditModal({
    visible,
    discoverySelector: discoverySelectorFromProps,
    confirmLoading,
    onCancel,
    onOk,
}: IProps) {
    const discoverySelector = useMemo(
        () => {
            let {matchLabels = []} = discoverySelectorFromProps;
            if (!Array.isArray(matchLabels)) {
                matchLabels = Object.entries(matchLabels);
            }
            return {...discoverySelectorFromProps, matchLabels};
        },
        [discoverySelectorFromProps]
    );

    const [formDataInstante] = Form.useForm();
    const [formData, setFormData] = useState({discoverySelector});
    const changeValues = useCallback(
        value => {
            setFormData(
                formData => _.mergeWith({}, formData, value)
            );
        },
        []
    );

    const onClickOK = useCallback(
        async (e: React.MouseEvent<HTMLElement>) => {
            e.preventDefault();
            try {
                const {
                    discoverySelector,
                } = await formDataInstante.validateFields();
                onOk(discoverySelector);
            } catch (error) {
                // console.log(error);
            }
        },
        [formDataInstante, onOk]
    );

    Form.useLabelLayout('basic', 0);

    return (
        <ModalStyled
            className={c['edit-discovery-selector']}
            title="编辑"
            visible={visible}
            confirmLoading={confirmLoading}
            onCancel={onCancel}
            onOk={onClickOK}
        >
            <Form
                name="basic"
                labelAlign="left"
                initialValues={formData}
                form={formDataInstante}
                autoComplete="off"
                colon={false}
                onValuesChange={changeValues}
            >
                <AlertStyled
                    showIcon
                    message={'开启/关闭服务发现范围配置会重启服务网格控制面，大约1分钟，在此期间内的服务网格配置变更将在重启完成后下发。'}
                    type="warning"
                />

                <Form.Item
                    label={
                        <>
                            服务发现范围配置：{
                                <Popover content={discoverySelectorTip}>
                                    <QuestionCircleOutlined />
                                </Popover>
                            }
                        </>
                    }
                    colon={false}
                >
                    <Form.Item
                        name={['discoverySelector', 'enabled']}
                        valuePropName="checked"
                    >
                        <Switch />
                    </Form.Item>
                    {formData.discoverySelector.enabled
                    && (
                        <Form.Item
                            className={c['namespace-label']}
                            required
                            label={
                                <>
                                    命名空间标签：{
                                        <Popover
                                            content={namespaceLabelTip}
                                        >
                                            <QuestionCircleOutlined />
                                        </Popover>
                                    }
                                </>
                            }
                            colon={false}
                        >
                            {
                                Object.entries(formData.discoverySelector.matchLabels)
                                    .map((_item, index) => {
                                        return (
                                            <SpaceStyled
                                                // eslint-disable-next-line react/no-array-index-key
                                                key={index}
                                                align="baseline"
                                            >
                                                <Form.Item
                                                    rules={labelsKeyValidate}
                                                    name={['discoverySelector', 'matchLabels', index, 0]}
                                                >
                                                    <LabelInputStyled
                                                        showCount
                                                        placeholder="请输入标签名称"
                                                    />
                                                </Form.Item>
                                                <LabelSplitStyled>:</LabelSplitStyled>
                                                <Form.Item
                                                    rules={labelsValueValidate}
                                                    name={['discoverySelector', 'matchLabels', index, 1]}
                                                >
                                                    <LabelInputStyled
                                                        maxLength={63}
                                                        showCount
                                                        placeholder="请输入标签值"
                                                    />
                                                </Form.Item>
                                            </SpaceStyled>
                                        );
                                    })
                            }
                        </Form.Item>
                    )
                    }
                </Form.Item>
            </Form>
        </ModalStyled>
    );
}
