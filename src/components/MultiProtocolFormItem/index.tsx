import {Form, Popover, Switch} from '@osui/ui';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {multiProtocolTip} from './constant';
import {isShowMultiProtocol} from './util';

export default function MultiProtocolFormItem() {
    return (
        <Form.Item
            shouldUpdate
            noStyle
        >
            {({getFieldValue}) => (
                isShowMultiProtocol(getFieldValue('type'), getFieldValue('istioVersion'))
            )
                && (
                    <>
                        <Form.Item
                            label={
                                <>
                                    支持兼容三方协议：{
                                        <Popover content={multiProtocolTip}>
                                            <QuestionCircleOutlined />
                                        </Popover>
                                    }
                                </>
                            }
                            colon={false}
                        >

                            <Form.Item
                                name={['multiProtocol']}
                                valuePropName="checked"
                            >
                                <Switch />
                            </Form.Item>
                        </Form.Item>
                    </>
                )
            }
        </Form.Item>
    );
}
