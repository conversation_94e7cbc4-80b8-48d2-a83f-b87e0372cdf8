import {Form} from '@baidu/icloud-ui-form';
import React, {useCallback, useState} from 'react';
import {Button, message, Space} from '@osui/ui';
import {useHistory, useParams} from 'react-router-dom';
import {CopyToClipboard} from 'react-copy-to-clipboard';
import {useBoolean} from 'huse';
import styled from 'styled-components';
import * as Layout from '@/components/Layout';
import {useCurrentServiceMeshIdSafe} from '@/hooks';
import {createCrd, getCrd, updateCrd} from '@/api/crd';
import {useQuery} from '@/hooks/useUrl';
import {CrdManageType} from '@/dicts/crd';
import urls from '@/urls';
import PageFooter from '../PageFooter';
import Editor from './Editor';
import c from './index.less';

const {Header} = Layout;
const Container = styled.div`
    padding: 16px;
`;

const titleMap = new Map([
    [CrdManageType.CREATE, '创建'],
    [CrdManageType.UPDATE, '编辑'],
    [CrdManageType.VIEW, '查看'],
]);

interface Iprops {
    type: CrdManageType;
}

export default function CrdManage({type}: Iprops) {
    const history = useHistory();
    const serviceMeshInstanceId = useCurrentServiceMeshIdSafe();
    const {namespace} = useParams<{ namespace: string, kind: string, name: string }>();
    const {kind, name} = useQuery<{ kind: string, name: string }>();
    const [content, setContent] = useState('');

    const handleInitialValues = useCallback(
        async () => {
            if ([CrdManageType.VIEW, CrdManageType.UPDATE].includes(type)) {
                try {
                    const crd = await getCrd({
                        serviceMeshInstanceId,
                        namespace,
                        kind,
                        name,
                    });
                    return Promise.resolve({content: crd?.content ?? ''});
                }
                catch (err) {
                    // console.log(err)
                }
            }
            return Promise.resolve({content: ''});
        },
        [kind, name, namespace, serviceMeshInstanceId, type]
    );
    // 复制 yaml文本
    const onCopy = useCallback(
        () => {
            message.success('复制成功');
        },
        []
    );
    const getTitle = useCallback(
        () => `${titleMap.get(type)}Istio资源`,
        [type]
    );

    const [loading, {on, off}] = useBoolean(false);
    const handleSubmit = useCallback(
        async ({content}) => {
            try {
                on();
                if (type === CrdManageType.CREATE) {
                    await createCrd({serviceMeshInstanceId, content});
                    message.success(`${getTitle()}成功`);
                }
                else if (type === CrdManageType.UPDATE) {
                    await updateCrd({
                        serviceMeshInstanceId,
                        namespace,
                        kind,
                        name,
                        content,
                    });
                    message.success(`${getTitle()}成功`);
                }
                history.push(urls.crd.list.fill({serviceMeshInstanceId}));
            } catch (err) {
                // console.log(err)
            } finally {
                off();
            }
        },
        [getTitle, history, kind, name, namespace, off, on, serviceMeshInstanceId, type]
    );
    const handleCancel = useCallback(
        () => {
            history.push(urls.crd.list.fill({serviceMeshInstanceId}));
        },
        [history, serviceMeshInstanceId]
    );
    const onBack = useCallback(
        () => history.push(urls.crd.list.fill({serviceMeshInstanceId})),
        [history, serviceMeshInstanceId]
    );

    return (
        <div className={c['crd-manage']}>
            <Header backIcon onBack={onBack} title={getTitle()} />
            <Container>
                <Form
                    asyncInitialValues={handleInitialValues}
                    onSubmit={handleSubmit}
                >
                    <Form.FieldSet>
                        <Form.Field
                            label="Yaml文件"
                            name="content"
                            errorPosition="bottom"
                            required
                            rules={{
                                required: true,
                                message: 'Yaml内容不能为空',
                            }}
                        >
                            <Editor
                                type={type}
                                value={content}
                                onChange={setContent}
                            />
                        </Form.Field>

                        <div className={c['copy-btn-wrapper']}>
                            <Form.Data>
                                {data => (
                                    <CopyToClipboard
                                        text={data.content}
                                        onCopy={onCopy}
                                    >
                                        <Button>复制</Button>
                                    </CopyToClipboard>
                                )}
                            </Form.Data>
                        </div>

                        <Form.Data>
                            {data => (
                                <PageFooter showConfirmButton={false} showCancelButton={false}>
                                    <Space>
                                        <Form.Submit
                                            size="large"
                                            loading={loading}
                                            disabled={!data?.content?.length}
                                        >确认
                                        </Form.Submit>
                                        <Button size="large" onClick={handleCancel}>取消</Button>
                                    </Space>
                                </PageFooter>
                            )}
                        </Form.Data>
                    </Form.FieldSet>
                </Form>
            </Container>
        </div>
    );
}
