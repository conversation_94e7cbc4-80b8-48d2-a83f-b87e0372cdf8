import React from 'react';
import {CrdManageType} from '@/dicts/crd';
import MonacoEditorComponent from '../MonacoEditorComponent';

interface IProps {
    type: CrdManageType;
    value: string;
    onChange: (v: string) => void;
}

export default function Editor({type, value, onChange}: IProps) {
    return (
        <MonacoEditorComponent
            language="yaml"
            width="1000px"
            height="440px"
            value={value}
            options={type === CrdManageType.VIEW ? {readOnly: true} : {readOnly: false}}
            onChange={onChange}
        />
    );
}

