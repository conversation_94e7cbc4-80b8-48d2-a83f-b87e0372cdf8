import {Tooltip} from '@osui/ui';
import {OutlinedBceWarningCircle} from '@baidu/acud-icon';
import {recommendVersion} from '@/modules/AssociateCluster/constants';
import {compareVersion} from '@/utils/version';
import urls from '@/urls';
import ExternalLink from '../ExternalLink';

interface IProps {
    record: {
        version: string;
        clusterId: string;
    };
}

export function K8sVersion({record}: IProps) {
    const {version = '', clusterId = ''} = record;
    return (
        <div>
            {version}
            {compareVersion(version, recommendVersion) !== 1
            && (
                <Tooltip
                    title={
                        <div>
                            当前版本为{version}，推荐先将K8S版本为升级至{recommendVersion}以上版本再进行纳管。
                            <ExternalLink
                                href={urls.external.cceClusterUpgrade.fill({},
                                    {clusterUuid: clusterId}
                                )}
                                value="立即升级"
                            />
                        </div>
                    }
                >
                    <OutlinedBceWarningCircle className="warning-icon" />
                </Tooltip>
            )}
        </div>
    );
}
