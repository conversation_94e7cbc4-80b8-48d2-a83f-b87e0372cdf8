import {useCallback, useMemo, useState} from 'react';
import {Typography} from '@osui/ui';
import c from './index.less';
const {Paragraph} = Typography;

interface IProps {
    copyValue: string;
    type?: string;
    showTag?: () => void;
    hideTag?: () => void;
}
export default function CopyDataWhenHover({copyValue, type, showTag, hideTag}: IProps) {
    const [isCopyable, setCopyable] = useState(false);
    const mouseOverHandler = useCallback(
        () => {
            setCopyable(true);
            hideTag && hideTag();
        },
        [hideTag]
    );
    const mouseLeaveHandler = useCallback(
        () => {
            setCopyable(false);
            showTag && showTag();
        },
        [showTag]
    );
    const typeStyle = useMemo(
        () => {
            if (type === 'link') {
                return c.linkColor;
            }
        },
        [type]
    );

    return (
        <div onMouseEnter={mouseOverHandler} onMouseLeave={mouseLeaveHandler}>
            <Paragraph copyable={isCopyable} className={`${typeStyle} ${c['paragraph']}`}>
                {copyValue}
            </Paragraph>
        </div>
    );
}
