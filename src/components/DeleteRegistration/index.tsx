import {ReactNode, useCallback, useMemo, useRef} from 'react';
import {Modal, message} from '@osui/ui';
import {deleteRegistrationInstanceApi} from '@/api/registration';
interface DeleteSingleRegistrationProps {
    record: any;
    callback: () => void;
    children: ReactNode;
}

export default function DeleteRegistration(props: DeleteSingleRegistrationProps) {
    const {record, callback, children} = props;
    const checkData = useRef<any[]>([true, true, false]);
    const [modal, contextHolder] = Modal.useModal();

    const config = useMemo(
        () => {
            return {
                title: '确定删除注册中心？',
                content: (
                    <div>
                        删除后，注册中心服务不可用且数据删除，该操作不可逆，请谨慎操作。
                    </div>
                ),
                onOk: async () => {
                    try {
                        await deleteRegistrationInstanceApi({
                            registrationhInstanceId: record.id,
                        });
                        message.success(`注册中心实例 ${record.name} 删除成功。`);
                    } catch (error) {
                        message.error(`注册中心实例 ${record.name} 删除失败，请重新操作。`);
                    } finally {
                        callback();
                    }
                },
                onCancel: () => {
                    checkData.current = [true, true, false];
                },
                closable: true,
            };
        },
        [record.id, record.name, callback]
    );

    const handleButtonClick = useCallback(
        () => {
            modal.confirm(config);
        },
        [config, modal]
    );

    return (
        <>
            <div onClick={handleButtonClick}>
                {children}
            </div>
            {contextHolder}
        </>
    );
}
