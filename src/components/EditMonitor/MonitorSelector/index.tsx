import {OutlinedRefresh} from '@baidu/acud-icon';
import {Cascader, Form} from '@osui/ui';
import styled from 'styled-components';
import {instancesValidate} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/formValidate';
import {monitorHelp} from '@/modules/CreateServiceMesh/constants';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {Option} from '@/api/util';
import c from './index.less';

const InstancesStyled = styled.div`
    position: relative;
`;
const CascaderStyled = styled(Cascader)`
    width: 255px !important;
`;
const RefreshPriceStyled = styled.span`
    position: absolute;
    top: 5px;
    left: 396px;
`;
const OutlinedRefreshStyled = styled(OutlinedRefresh)`
    margin: 0 12px;
`;

interface IProps {
    monitorInstanceListLoading: boolean;
    monitorInstanceOptions: Option[];
    getMonitorInstanceList: () => Promise<void>;
}

export default function MonitorSelector(props: IProps) {
    const {
        monitorInstanceListLoading,
        monitorInstanceOptions,
        getMonitorInstanceList,
    } = props;

    return (
        <Form.Item
            shouldUpdate
            noStyle
        >
            {({getFieldValue}) => (getFieldValue(['monitor', 'enabled'])
                && (
                    <InstancesStyled>
                        <Form.Item
                            className={c['instances']}
                            required
                            name={['monitor', 'instances']}
                            rules={instancesValidate}
                            help={monitorHelp}
                            label="选择实例："
                            colon={false}
                        >
                            <CascaderStyled
                                disabled={monitorInstanceListLoading}
                                loading={monitorInstanceListLoading}
                                options={monitorInstanceOptions}
                                placeholder="请选择"
                            />
                        </Form.Item>

                        <RefreshPriceStyled>
                            <OutlinedRefreshStyled onClick={getMonitorInstanceList} />
                            <ExternalLink
                                href={urls.external.cpromCostDetail.fill()}
                                value="CProm费用详情"
                            />
                        </RefreshPriceStyled>
                    </InstancesStyled>
                ))
            }
        </Form.Item>
    );
}
