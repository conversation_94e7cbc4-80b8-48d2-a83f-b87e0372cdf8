import {Form, Modal} from '@osui/ui';
import {useCallback, useMemo} from 'react';
import {IMonitorItem} from '@/api/createServiceMesh';
import MonitorFormItem from '../MonitorFormItem';
import ConfirmContent from './ConfirmContent';

interface IMonitorItemView {
    enabled: boolean;
    instances: string[];
}

interface IProps {
    visible: boolean;
    monitor: IMonitorItem;
    confirmLoading: boolean;
    onCancel: () => void;
    onOk: (monitor: IMonitorItem) => void;
}

export default function EditMonitor({visible, monitor, confirmLoading, onCancel, onOk}: IProps) {
    const [formDataInstante] = Form.useForm();

    const forDataInitialValues = useMemo(
        () => {
            // 边界：进行Cprom的关闭操作后，instances为 undefined
            const {region = '', id = '', name = ''} = monitor?.instances?.[0] ?? {};
            const value = JSON.stringify({id, name});
            const monitorItemView: IMonitorItemView = {
                enabled: monitor?.enabled,
                instances: region ? [region, value] : [],
            };

            return {monitor: monitorItemView};
        },
        [monitor]
    );

    const onClickOK = useCallback(
        async (e: React.MouseEvent<HTMLElement>) => {
            e.preventDefault();
            try {
                const {monitor} = await formDataInstante.validateFields();
                const {enabled = ''} = monitor ?? {};

                if (enabled) {
                    onOk(monitor);
                }
                else {
                    onCancel();
                    // 关闭，出现二次确认弹窗
                    Modal.confirm({
                        title: '确定关闭监控指标采集吗？',
                        content: <ConfirmContent />,
                        onOk() {
                            onOk(monitor);
                        },
                        closable: true,
                    });
                }
            } catch (error) {
                // console.log(error);
            }
        },
        [formDataInstante, onCancel, onOk]
    );

    Form.useLabelLayout('basic', 0);

    return (
        <Modal
            width={728}
            title="编辑"
            visible={visible}
            confirmLoading={confirmLoading}
            onCancel={onCancel}
            onOk={onClickOK}
        >
            <Form
                name="basic"
                labelAlign="left"
                initialValues={forDataInitialValues}
                form={formDataInstante}
                autoComplete="off"
                colon={false}
            >
                <MonitorFormItem type="gateway" meshType="standalone" />
            </Form>
        </Modal>
    );
}
