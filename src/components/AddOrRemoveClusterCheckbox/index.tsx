// 添加、移除集群的勾选框

import {TooltipPlacement} from 'antd/lib/tooltip';
import {cloneElement, ReactElement, ReactNode} from 'react';
import {Tooltip} from '@osui/ui';
import {IS_QASANDBOX} from '@/dicts';
import {Cluster} from '@/interface/cluster';

type TOperation = 'add' | 'remove';

interface IProps {
    type: TOperation;
    clusterList: string[];
    record: Cluster;
    originNode: ReactNode;
    placement?: TooltipPlacement;
    curSelectedCount?: number;
    limitSelectedCount?: number;
}
export default function AddOrRemoveClusterCheckbox({
    type,
    clusterList,
    record,
    originNode,
    placement = 'right',
    curSelectedCount = 0,
    limitSelectedCount = 0,
}: IProps) {
    // eslint-disable-next-line complexity
    const tip = (() => {
        // 注：用户在cce处删除了集群、状态变为 deleted ，还得能在 CSM 进行最后的1次删除。
        // 根因：目前的cce集群变更，无法同步至CSM后端。
        if (record.status === 'deleted') {
            return '';
        }

        if (!IS_QASANDBOX && !clusterList.includes(record.clusterId)) {
            return '无权限，请到CCE上申请相关的权限';
        }

        if (type === 'add') {
            if (curSelectedCount >= limitSelectedCount) {
                return '当前勾选数已到达限制数';
            }
            else if (!record.available) {
                return '当前集群不可用';
            }
        }

        if (type === 'remove') {
            if (record.isConfig) {
                return 'Istio资源配置在该集群中，无法被移出';
            }
            if (record.isPrimary) {
                return '主集群不可被移除';
            }
            if (record.status === 'Removing') {
                return '当前集群移除中';
            }
        }

        return '';
    })();

    return (
        <Tooltip placement={placement} title={tip}>
            {
                cloneElement(originNode as ReactElement, {
                    disabled: Boolean(tip),
                })
            }
        </Tooltip>
    );
}
