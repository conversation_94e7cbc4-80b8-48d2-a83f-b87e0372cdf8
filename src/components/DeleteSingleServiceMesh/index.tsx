import {ReactNode, useCallback, useMemo, useRef} from 'react';
import {Modal, message, Checkbox} from '@osui/ui';
import styled from 'styled-components';
import {deleteServiceMeshInstanceApi, ServiceMeshListItem} from '@/api/serviceMesh';
import {isStandaloneMesh} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/util';
import generateCheckOptions from './generateCheckOptions';
interface DeleteSingleServiceMeshProps {
    record: ServiceMeshListItem;
    callback: () => void;
    children: ReactNode;
}

const StyledCheckboxGroup = styled(Checkbox.Group)`
    display: flex;
    flex-direction: column;
`;

export default function DeleteSingleServiceMesh(props: DeleteSingleServiceMeshProps) {
    const {record, callback, children} = props;
    const checkData = useRef<any[]>([true, true, false]);
    const [modal, contextHolder] = Modal.useModal();

    const checkOptions = useMemo(
        () => {
            return generateCheckOptions(record);
        },
        [record]
    );

    const handleChange = useCallback(
        (v: any) => {
            const currentStatus = checkOptions.map((item: any) => {
                return v.includes(item.value);
            });
            if (isStandaloneMesh(record.instanceType)) {
                checkData.current = [true, ...currentStatus, false];
            } else {
                checkData.current = currentStatus;
            }
        },
        [checkOptions, record.instanceType]
    );

    const config = useMemo(
        () => {
            return {
                title: '删除网格',
                content: (
                    <div>
                        {`网格 <${record.instanceName}(${record.instanceId})> 已关联以下信息，你确定要删除吗？`}
                        <StyledCheckboxGroup
                            options={checkOptions}
                            defaultValue={isStandaloneMesh(record.instanceType) ? ['blb'] : ['eni', 'blb']}
                            onChange={handleChange}
                        />
                    </div>
                ),
                onOk: async () => {
                    try {
                        await deleteServiceMeshInstanceApi({
                            serviceMeshInstanceId: record.instanceId,
                            isReleaseControlPlaneBlb: checkData.current[1],
                            isReleaseEip: checkData.current[2],
                        });
                        message.success(`网格 <${record.instanceName}(${record.instanceId})> 删除成功。`);
                    } catch (error) {
                        message.error(`网格 <${record.instanceName}(${record.instanceId})> 删除失败，请重新操作。`);
                    } finally {
                        callback();
                    }
                },
                onCancel: () => {
                    checkData.current = [true, true, false];
                },
                closable: true,
            };
        },
        [handleChange, record.instanceName, record.instanceId, checkOptions, record.instanceType, callback]
    );

    const handleButtonClick = useCallback(
        () => {
            modal.confirm(config);
        },
        [config, modal]
    );

    return (
        <>
            <div onClick={handleButtonClick}>
                {children}
            </div>
            {contextHolder}
        </>
    );
}
