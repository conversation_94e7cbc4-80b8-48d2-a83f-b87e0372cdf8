import {ServiceMeshListItem} from '@/api/serviceMesh';
import {isStandaloneMesh} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/util';
import urls from '@/urls';
import ExternalLink from '../ExternalLink';
import c from './generateCheckOptions.less';

export default function generateCheckOptions(record: ServiceMeshListItem) {
    // TODO BLB、EIP的勾选功能暂时隐掉。
    if (isStandaloneMesh(record.instanceType)) {
        return [
            // {
            //     label: (
            //         <>
            //             释放控制面BLB
            //             <ExternalLink
            //                 className={c['link']}
            //                 href={
            //                     urls.external.blbDetail.fill({},
            //                         {appblbId: record.blbInfo.blbId}
            //                     )
            //                 }
            //                 value={record.blbInfo.blbId}
            //             />
            //         </>
            //     ),
            //     value: 'blb',
            // },
        ];
    } else {
        return [
            {
                label: (
                    <>
                        释放CSM实例控制面创建的弹性网卡
                        <ExternalLink
                            className={c['link']}
                            href={
                                urls.external.eniList.fill()
                            }
                            value="弹性网卡列表"
                        />
                    </>
                ),
                value: 'eni',
                disabled: true,
            },
            // {
            //     label: (
            //         <>
            //             释放控制面BLB
            //             <ExternalLink
            //                 className={c['link']}
            //                 href={
            //                     urls.external.blbDetail.fill({},
            //                         {appblbId: record.blbInfo.blbId}
            //                     )
            //                 }
            //                 value={record.blbInfo.blbId}
            //             />
            //         </>
            //     ),
            //     value: 'blb',
            // },
            // {
            //     label: (
            //         <>
            //             释放已绑定的EIP
            //             <ExternalLink
            //                 className={c['link']}
            //                 href={
            //                     urls.external.eipDetail.fill({},
            //                         {eip: record.eipInfo.eipId, eipType: 'normal'}
            //                     )
            //                 }
            //                 value={record.eipInfo.eipId}
            //             />
            //         </>
            //     ),
            //     value: 'eip',
            // },
        ];
    }
}
