import {IconHelpLink} from '@baidu/lsqm-icon';
import urls from '@/urls';
import ExternalLink from '../ExternalLink';
import c from './index.less';

export default function ProductDocLink() {
    return (
        <div className={c['productDocLink']}>
            <ExternalLink
                className={c['link']}
                href={urls.external.csmDocProduct.fill()}
                value={
                    <>
                        <IconHelpLink />
                        <span className={c['title']}>产品文档</span>
                    </>
                }
            />
        </div>
    );
}
