import styled from 'styled-components';
import {ReactNode} from 'react';
import {PageHeader, PageHeaderProps} from '@osui/ui';
import {OutlinedLeft} from '@baidu/acud-icon';
import {COLOR_BRAND_6, FONT_SIZE_12, FONT_SIZE_14, GRAY_5, GRAY_8, R4} from '@/styles';


interface SiderProps {
    title?: string;
    children?: ReactNode;
}
interface ContentProps {
    children: ReactNode;
    backIcon?: ReactNode;
}
type HeaderProps = Omit<PageHeaderProps, 'ghost'> | ContentProps;
const MenuContainer = styled.div`
    width: 180px;
    background-color: white;
    box-shadow: 0 2px 8px 0 rgba(7,12,20,0.12);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 100;
`;
const MenuHeader = styled.div`
    padding: 13px 16px 0;
    color: ${GRAY_5};
    font-size: ${FONT_SIZE_14};
`;
const MenuHeaderContent = styled.div`
    border-bottom: 1px solid ${GRAY_8};
    padding-bottom: 13px;
    line-height: 22px;
    font-weight: 600;
`;
const Main = styled.div`
    margin-left: 180px;
    height: 100%;
    overflow: auto;
`;

export function Sider({title, children}: SiderProps) {
    return (
        <MenuContainer>
            <MenuHeader>
                <MenuHeaderContent>{title}</MenuHeaderContent>
            </MenuHeader>
            {children}
        </MenuContainer>
    );
}
export function Content({children}: ContentProps) {
    return (
        <Main>
            {children}
        </Main>
    );
}
// @todo 需要让外层的button display block
const BackStyled = styled.section`
    color:${GRAY_5};
    font-weight: initial;
    font-size: ${FONT_SIZE_12};
    display: flex;
    align-items: center;
    margin-top: 4px;
    :hover {
        color: ${COLOR_BRAND_6};
    }
`;
function Back() {
    return (
        <BackStyled>
            <OutlinedLeft />
            <span>返回</span>
        </BackStyled>
    );
}
export function Header(props: HeaderProps) {
    return (
        <PageHeader
            ghost={false}
            {...props}
            backIcon={props.backIcon ? <Back /> : true}
        />
    );
}
const MainContentStyled = styled.div`
    padding: 16px;
`;
const MainContentContainerStyled = styled.div`
    padding: 24px;
    background-color: white;
    border-radius: ${R4};
`;
export function MainContent({children}: ContentProps) {
    return (
        <MainContentStyled>
            <MainContentContainerStyled>
                {children}
            </MainContentContainerStyled>
        </MainContentStyled>
    );
}
