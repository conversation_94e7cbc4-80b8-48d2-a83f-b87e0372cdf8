import {ReactNode} from 'react';
import {Button, Tooltip} from '@osui/ui';
import {OutlinedPlus} from '@baidu/acud-icon';
import {ButtonType} from 'antd/lib/button';
import c from './index.less';

interface IProps {
    [key: string]: any;
    type?: ButtonType | 'strong' | 'icon';
    icon?: ReactNode;
    disabled?: boolean;
    children: ReactNode;
    placement?: string;
    title?: string;
    onClick?: () => void;
}
export default function AddButton(
    {
        children,
        type = 'primary',
        icon = <OutlinedPlus />,
        disabled,
        placement = 'right',
        title = '',
        onClick,
        ...props
    }: IProps
) {
    return (
        <Tooltip placement={placement} title={title}>
            <Button
                className={c['addButton']}
                type={type}
                icon={icon}
                disabled={disabled}
                onClick={onClick}
                {...props}
            >
                {children}
            </Button>
        </Tooltip>
    );
}
