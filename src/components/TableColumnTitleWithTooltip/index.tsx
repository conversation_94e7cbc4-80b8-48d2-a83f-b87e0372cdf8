import {OutlinedQuestionCircle} from '@baidu/acud-icon';
import {Tooltip} from '@osui/ui';
import {ReactNode} from 'react';
import c from './index.less';

interface IProps {
    title: ReactNode;
    tooltip?: ReactNode;
    placement?: 'top' | 'bottom' | 'left' | 'right';
}
export default function TableColumnTitleWithTooltip({
    title,
    tooltip = '',
    placement = 'top',
}: IProps) {
    return (
        <div className={c['tableColumnTitleWithTooltip']}>
            {title}
            <Tooltip title={tooltip} placement={placement}>
                <OutlinedQuestionCircle className={c['tooltipIcon']} />
            </Tooltip>
        </div>
    );
}
