import MonacoEditor, {loader} from '@monaco-editor/react';
import {useCallback} from 'react';

loader.config({paths: {vs: 'https://code.bdstatic.com/npm/monaco-editor@0.28.1/min/vs'}});

interface Props {
    language: string;
    height: string;
    width: string;
    value?: string;
    theme?: string;
    options?: object;
    onChange?: (value: string) => void;
    onMount?: (value: any) => void;
}
let editorOptions = {
    minimap: {enabled: false},
};
function MonacoEditorComponent({language, height, width, value, options, theme, onChange, onMount}: Props) {
    editorOptions = {...editorOptions, ...options};
    const onValueChange = useCallback(
        e => {
            onChange && onChange(e);
        },
        [onChange]
    );

    return (
        <div style={{height, width, border: '1px solid #eee'}}>
            <MonacoEditor
                value={value}
                options={editorOptions}
                language={language}
                theme={theme}
                onChange={onValueChange}
                onMount={onMount}
            />
        </div>

    );
}
export default MonacoEditorComponent;
