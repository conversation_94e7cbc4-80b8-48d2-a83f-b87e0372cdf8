import {ReactNode} from 'react';
import {Button, ButtonProps, Space, SpaceProps} from '@osui/ui';
import c from './index.less';
interface IProps {
    className?: string; // 根元素的类名
    button?: ButtonProps; // 两个按钮一起设置的属性 详见antd
    space?: SpaceProps; // Space 组件的属性 详见antd

    confirmButton?: ButtonProps; // 确认按钮的属性 详见antd
    confirmButtonText?: string; // 确认的文案
    showConfirmButton?: boolean; // 是否显示确认按钮

    cancelButton?: ButtonProps; // 取消按钮的属性 详见antd
    cancelButtonText?: string; // 取消按钮的文案
    showCancelButton?: boolean; // 是否显示取消按钮
    children?: ReactNode; // 其他的内容

    onConfirm?: (arg: any) => any; // 确认按钮事件
    onCancel?: (arg: any) => any; // 取消按钮事件
}
export default function PageFooter(
    {
        space, button, confirmButton, cancelButton, confirmButtonText = '确认', cancelButtonText = '取消',
        showConfirmButton = true, showCancelButton = true, onConfirm, onCancel,
        children, className: pageFooterClass = '',
    }: IProps) {
    const confirmButtonAttr = Object.assign({}, button, confirmButton);
    const cancelButtonAttr = Object.assign({}, button, cancelButton);
    return (
        <div className={`${c.pageFooter} ${pageFooterClass}`}>
            <Space {...space} key="csmPageFooterSpace">
                {
                    showConfirmButton ? (
                        <Button
                            type="primary"
                            size="large"
                            onClick={onConfirm}
                            {...confirmButtonAttr}
                        >
                            {confirmButtonText}
                        </Button>
                    ) : <></>
                }
                {
                    showCancelButton ? (
                        <Button
                            size="large"
                            onClick={onCancel}
                            {...cancelButtonAttr}
                        >
                            {cancelButtonText}
                        </Button>
                    ) : <></>
                }
            </Space>
            {children && children}
        </div>
    );
}
