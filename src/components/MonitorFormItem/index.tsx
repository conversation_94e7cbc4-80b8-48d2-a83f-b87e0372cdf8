import {Form, Popover, Switch} from '@osui/ui';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {monitorTip} from '@/modules/CreateServiceMesh/constants';
import {isStandaloneMesh} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/util';
import {TServiceMeshType} from '@/modules/CreateServiceMesh/type';
import MonitorInstancesCascader from './MonitorInstancesCascader';
import c from './index.less';

// 监控指标采集项分2种，网格实例和网关实例下的。
export type TMonitorFormItemType = 'mesh' | 'gateway';
export interface IProps{
    type: TMonitorFormItemType;
    meshType?: TServiceMeshType;
}
export default function MonitorFormItem({type, meshType}: IProps) {
    return (
        <Form.Item
            shouldUpdate
            noStyle
        >
            {({getFieldValue}) => (
                type === 'gateway'
                || meshType === 'standalone'
                || isStandaloneMesh(getFieldValue('type'))
            )
                && (
                    <>
                        <Form.Item
                            className={c['monitor-form-item-label']}
                            label={
                                <>
                                    监控指标采集：{
                                        <Popover content={monitorTip}>
                                            <QuestionCircleOutlined />
                                        </Popover>
                                    }
                                </>
                            }
                            colon={false}
                        >

                            <Form.Item
                                name={['monitor', 'enabled']}
                                valuePropName="checked"
                            >
                                <Switch />
                            </Form.Item>
                        </Form.Item>

                        <MonitorInstancesCascader type={type} />
                    </>
                )
            }
        </Form.Item>
    );
}
