import {<PERSON><PERSON>, Form, Spin} from '@osui/ui';
import {useBoolean} from 'huse';
import {useCallback, useEffect, useState} from 'react';
import {OutlinedRefresh} from '@baidu/acud-icon';
import {useFramework} from '@/hooks';
import {getMonitorInstanceListApi, IInstanceItem} from '@/api/createServiceMesh';
import {getMonitorInstanceOptions} from '@/api/util';
import {instancesValidate} from '@/modules/CreateServiceMesh/CreateServiceMeshForm/formValidate';
import {monitorHelp} from '@/modules/CreateServiceMesh/constants';
import ExternalLink from '@/components/ExternalLink';
import urls from '@/urls';
import {IProps} from '..';
import c from './index.less';
import {gatewayMonitorHelp} from './constant';

export default function MonitorInstancesCascader({type}: IProps) {
    const {region: {regionList, currentRegion}} = useFramework();
    const [
        monitorInstanceListLoading,
        {on: onMonitorInstanceListLoading, off: offMonitorInstanceListLoading},
    ] = useBoolean(false);
    const [monitorInstanceList, setMonitorInstanceList] = useState<IInstanceItem[]>();
    const getMonitorInstanceList = useCallback(
        async () => {
            onMonitorInstanceListLoading();
            try {
                const res = await getMonitorInstanceListApi({});
                setMonitorInstanceList(res.instances);
            } catch (error) {
                console.error(error);
            } finally {
                offMonitorInstanceListLoading();
            }
        },
        [offMonitorInstanceListLoading, onMonitorInstanceListLoading]
    );
    useEffect(
        () => {
            getMonitorInstanceList();
        },
        [getMonitorInstanceList]
    );

    return (
        <Form.Item
            shouldUpdate
            noStyle
        >
            {({getFieldValue}) => (getFieldValue(['monitor', 'enabled'])
                && (
                    monitorInstanceListLoading
                        ? <Spin />
                        : (
                            <div className={c['instances']}>
                                <Form.Item
                                    shouldUpdate
                                    noStyle
                                >
                                    {({getFieldValue}) => {
                                        const curSelectedRegion =
                                        type === 'mesh'
                                            ? getFieldValue(['region'])
                                            : currentRegion;
                                        const monitorInstanceOptions = getMonitorInstanceOptions(
                                            curSelectedRegion,
                                            monitorInstanceList || [],
                                            regionList,
                                            (v: IInstanceItem) => JSON.stringify({id: v.id, name: v.name})
                                        );

                                        return (
                                            <Form.Item
                                                required
                                                name={['monitor', 'instances']}
                                                rules={instancesValidate}
                                                help={
                                                    type === 'mesh'
                                                        ? monitorHelp
                                                        : gatewayMonitorHelp
                                                }
                                                label="选择实例"
                                            >
                                                <Cascader
                                                    className={c['instances-cascader']}
                                                    disabled={monitorInstanceListLoading}
                                                    loading={monitorInstanceListLoading}
                                                    options={monitorInstanceOptions}
                                                    placeholder="请选择"
                                                />
                                            </Form.Item>
                                        );
                                    }}
                                </Form.Item>

                                <span className={c['refresh-price']}>
                                    <OutlinedRefresh
                                        className={c['outlined-refresh']}
                                        onClick={getMonitorInstanceList}
                                    />
                                    <ExternalLink
                                        href={urls.external.cpromCostDetail.fill()}
                                        value="CProm费用详情"
                                    />
                                </span>
                            </div>
                        )
                ))
            }
        </Form.Item>
    );
}
