import {Button, Input, Modal} from '@osui/ui';
import React, {useState} from 'react';


export default function MFACheck() {
    const [visible, setVisible] = useState(true);
    const handleCancel = () => {
        setVisible(false);
    };
    return (
        <Modal
            visible={visible}
            title="安全验证"
            onCancel={handleCancel}
        >
            <div>为确保您的账户安全，请通过安全验证：</div>
            <div>
                <div>
                    绑定手机
                </div>
                <Button>更换手机号</Button>
            </div>
            <div>
                <div>
                    绑定手机
                </div>
                <div>
                    <Input />
                    <Button>发送验证码</Button>
                </div>
            </div>
        </Modal>
    );
}
