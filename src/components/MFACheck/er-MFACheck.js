/* eslint-disable no-unused-vars */
/**
 * @file MFA 校验组件，独立的浮层展现一个短信验证的界面
 *
 * <AUTHOR>
 */

import 'esui/Dialog';
import 'inf-ria/ui/SMSCodeBox';

import u from 'lodash';
import _ from 'inf-i18n';
import $ from 'jquery';
import Promise from 'promise';
import esui from 'esui';
import QRCode from 'qrcodejs';

import config from '../config';
import Base from './Base';

const kInvalidCode = new Error('Invalide MFA Code');
const kLimitReached = new Error('LimitReached');
const kInternalError = new Error('InternalError');
const api = config.api;

export default class MFACheck extends Base {

    initialize(options) {
        this.data = options.data || {};
        this.validator = options.validator;
        this.mfaDeviceStatus = false;
        let optionText = '';
        if (this.data.mobile) {
            optionText += '<option name="phone" value="phone">短信验证码</option>';
        }
        if (this.data.email) {
            optionText += '<option name="email" value="email">邮箱验证</option>';
        }
        if (this.data.totp) {
            optionText += '<option name="totp" value="totp">软件动态码</option>';
        }
        this.optionText = optionText;
        this.chooseOtherLink = this.data.mobile
            ? '<li class="totp-bind-choose-other hide"><a href="javascript:void(0);"><选择其他验证方式</a><li/>'
            : '';
        if (this.data.email != null || this.data.totp) {
            this.defaultAuthType = this.data.mobile ? 'phone' : (this.data.email ? 'email' : 'totp');
            this.createDialogWithMulti();
        }
        else {
            this.createDialog();
        }
        this.main = $(this.dialog.main);
        this.main.addClass('component-mfacheck');

        this.render();
    }

    /**
      * 生成 dialog，弹窗视图
      *
      * @return {esui.Dialog} 弹窗实例
      */
    createDialog() {
        this.dialog = esui.create('Dialog', {
            id: 'component-mfacheck',
            width: 455,
            closeButton: false,
            closeOnHide: true,
            mask: true,
            alwaysTop: true,
            title: _('安全验证'),
            content: `
                 <section class="component-dialog-content">
                     <header>
                         <p>${_('为了保护您的账户安全，请通过安全验证：')}</p>
                     </header>
                     <ul>
                         <li>
                             <label>${_('您绑定的手机：')}</label>
                             ${this.data.mobile}
                             <a href="/iam/${location.search}#/iam/authinfo" id="redirect-to-change-mobile"
                                 title="${_('更换手机')}" target="_blank">
                                 ${_('更换手机')}
                             </a>
                         </li>
                         <li>
                             <label>${_('短信验证码：')}</label>
                             <div data-ui-type="SMSCodeBox"
                                  data-ui-name="smsCodeBox"
                                  data-ui-id="smsCodeBox"
                                  data-ui-placeholder="请输入验证码"
                                  data-ui-child-name="smsCodeBox"
                                  data-ui-input-width="100"></div>
                             <span id="mfacheck-message"></span>
                         </li>
                     </ul>
                 </section>
             `,
        });

        this.dialog.appendTo(document.body);

        return this.dialog;
    }

    /**
      * 生成 dialog，弹窗视图
      *
      * @return {esui.Dialog} 弹窗实例
      */
    createDialogWithMulti() {
        this.dialog = esui.create('Dialog', {
            id: 'component-mfacheck',
            width: 455,
            closeButton: false,
            closeOnHide: true,
            mask: true,
            alwaysTop: true,
            title: _('安全验证'),
            content: `
                 <section class="component-dialog-content">
                     <header>
                         <p>${_('为了保护您的账户安全，请通过安全验证：')}</p>
                         <p class="totp-bind-section hide">
                             ${_('请使用Google身份验证器或其他TOTP App扫描二维码并绑定：')}
                         </p>
                         <div class="totp-bind-section hide qrcode-img" id="qrcode"></div>
                     </header>
                     <ul>
                         <li class="auth-type-select">
                             <label>${_('验证方式')}</label>
                             <select data-ui-type="Select"
                                  data-ui-id="authType"
                                  data-ui-value="phone"
                                  data-ui-child-name="authTypeSelect"
                                  data-ui-width="200"
                             >
                                 ${this.optionText}
                             </select>
                         </li>
                         <li class="sms-section">
                             <label>${_('您绑定的手机：')}</label>
                             ${this.data.mobile}
                             <a href="/iam/${location.search}#/iam/authinfo" id="redirect-to-change-mobile"
                                 title="${_('更换手机')}" target="_blank">
                                 ${_('更换手机')}
                             </a>
                         </li>
                         <li class="email-section hide">
                             <label>${_('您绑定的邮箱：')}</label>
                             ${this.data.email}
                             <a href="/iam/${location.search}#/iam/authinfo" id="redirect-to-change-email"
                                 title="${_('更换邮箱')}" target="_blank">
                                 ${_('更换邮箱')}
                             </a>
                         </li>
                         <li class="totp-bind-section hide">
                             <label>${_('验证码1：')}</label>
                             <div data-ui-type="TextBox"
                                 data-ui-name="code1"
                                 data-ui-child-name="code1"
                                 data-ui-id="code1"
                                 data-ui-placeholder="请输入6位验证码"
                                 data-ui-width="178"></div>
                         </li>
                         <li class="totp-bind-section hide">
                             <label>${_('验证码2：')}</label>
                             <div data-ui-type="TextBox"
                                 data-ui-name="code2"
                                 data-ui-child-name="code2"
                                 data-ui-id="code2"
                                 data-ui-placeholder="30s后，输入第二组6位验证码"
                                 data-ui-width="178"></div>
                         </li>
                         <li>
                             <label class="sms-section">${_('短信验证码：')}</label>
                             <label class="email-section hide">${_('邮箱验证码：')}</label>
                             <label class="totp-section hide">${_('验证码：')}</label>
                             <div data-ui-type="SMSCodeBox"
                                  data-ui-name="smsCodeBox"
                                  data-ui-id="smsCodeBox"
                                  data-ui-child-name="smsCodeBox"
                                  data-ui-placeholder="请输入验证码"
                                  data-ui-input-width="100"></div>
                             <span id="mfacheck-message"></span>
                         </li>
                         ${this.chooseOtherLink}
                     </ul>
                 </section>
             `,
        });

        this.dialog.appendTo(document.body);

        return this.dialog;
    }

    /**
      * 展现
      */
    show() {
        this.dialog.show();
        this.dialog.main.style.zIndex = '999999';
    }

    /**
      * 隐藏
      */
    hide() {
        this.dispose();
    }

    /**
      * 渲染处理
      */
    render() {

        if (this.data.errormsg) {
            // 后端接口没法对这个字段做国际化，且目前只会返回“您当前的操作需要进行二次验证”这个值
            // 所以先前端写死
            // this.showError(this.data.errormsg);
            this.showError(_('您当前的操作需要进行二次验证'));
        }

        if (this.data.totp) {
            this.getMfaDeviceStatus().then(() => {
                this.bindEvents();
            });
        }
        else {
            this.bindEvents();
        }

        this.dialog.getFoot().getChild('btnOk').disable();
    }

    /**
      * 事件绑定
      */
    bindEvents() {

        const dialog = this.dialog;

        // 事件处理
        const okBtn = dialog.getFoot().getChild('btnOk');
        const cancelBtn = dialog.getFoot().getChild('btnCancel');
        const smsCodeBox = dialog.getBody().getChild('smsCodeBox');
        const authTypeSelect = dialog.getBody().getChild('authTypeSelect');
        const $chooseOtherLink = $('.totp-bind-choose-other');

        const handleTotpSection = () => {
            if (this.mfaDeviceStatus) {
                $('.component-dialog-content .totp-section').addClass('hide');
                $('.component-dialog-content .ui-smscodebox .ui-button').removeClass('hide');
                smsCodeBox.setProperties({inputWidth: 100});
            }
            else {
                $('.component-dialog-content .totp-bind-section').addClass('hide');
                smsCodeBox.show();
                $('.auth-type-select').show();
                $('.totp-bind-choose-other').hide();
            }
        };

        const authTypeChange = e => {
            const authType = e.value || e.target.getValue();
            if (authType === 'email') {
                $('.component-dialog-content .email-section').removeClass('hide');
                $('.component-dialog-content .sms-section').addClass('hide');
                handleTotpSection();
            }
            else if (authType === 'phone') {
                $('.component-dialog-content .sms-section').removeClass('hide');
                $('.component-dialog-content .email-section').addClass('hide');
                handleTotpSection();
            }
            else if (authType === 'totp') {
                if (this.mfaDeviceStatus) {
                    $('.component-dialog-content .totp-section').removeClass('hide');
                    $('.component-dialog-content .ui-smscodebox .ui-button').addClass('hide');
                    smsCodeBox.setProperties({inputWidth: 178});
                }
                else {
                    $('.component-dialog-content .totp-bind-section').removeClass('hide');
                    smsCodeBox.hide();
                    $('.auth-type-select').hide();
                    $('.totp-bind-choose-other').show();
                }
                $('.component-dialog-content .sms-section').addClass('hide');
                $('.component-dialog-content .email-section').addClass('hide');
                const code1 = dialog.getBody().getChild('code1');
                const code2 = dialog.getBody().getChild('code2');
                code1.on('input', () => {
                    this.clearError();
                    if (okBtn.disabled) {
                        okBtn.enable();
                    }
                });
                code2.on('input', () => {
                    this.clearError();
                    if (okBtn.disabled) {
                        okBtn.enable();
                    }
                });
            }
        };

        // 获取验证码
        smsCodeBox.on('action', () => {
            const btn = smsCodeBox.getChild('btn');
            btn.disable();
            const input = smsCodeBox.getChild('input');
            // input.enable();
            input.getFocusTarget().focus();

            // this.getAuthCode()
            let authType = 'phone';
            if (dialog.getBody().getChild('authTypeSelect')) {
                authType = dialog.getBody().getChild('authTypeSelect').getValue();
            }
            api.sendAuthCode({authtype: authType}, {charset: 'UTF-8'})
                .then(() => {
                    this.showMessage(_('校验码已发送'));
                    smsCodeBox.startCountdown();
                })
                .fail(error => {
                    const key = Object.keys(error.field)[0];
                    this.showError(error.field[key]);
                    btn.enable();
                });
        });

        smsCodeBox.getChild('input').on('input', () => {
            this.clearError();
            if (okBtn.disabled) {
                okBtn.enable();
            }
        });

        // 点击确定
        okBtn.on('click', e => {
            let code = '';
            let cb = () => Promise.resolve();
            if (authTypeSelect && authTypeSelect.getValue() === 'totp' && !this.mfaDeviceStatus) {
                const code1 = dialog.getBody().getChild('code1').getValue();
                const code2 = dialog.getBody().getChild('code2').getValue();
                if (!code1 || !code2) {
                    this.showError(_('校验码不能为空'));
                    return;
                }
                code = `${code1};${code2}`;
                cb = result => api.bindDevice({type: 'totp', authCodeToken: result.token});
            }
            else {
                code = u.trim(smsCodeBox.getChild('input').getValue());
            }

            if (!code) {
                this.showError(_('校验码不能为空'));
                return;
            }

            // 重新发送请求，携带着信息
            this.verifyCode(code, cb)
                .then(data => this.fire('ok', {data}))
                .fail(e => {
                    if (e === kInvalidCode) {
                        this.showError(_('校验码不正确'));
                        return;
                    }
                    else if (e === kLimitReached) {
                        this.showError(_('验证次数超过小时上限，请一小时后重试'));
                        return;
                    }
                    else if (e === kInternalError) {
                        this.showError(_('服务器内部错误'));
                        return;
                    }

                    // 重试
                    this.fire('ok', {data: code});
                });
        });

        // 点击取消
        cancelBtn.on('click', e => {
            this.fire('cancel');
            this.hide();
        });

        // 切换验证方式
        if (authTypeSelect) {
            authTypeSelect.on('change', e => authTypeChange(e));
        }

        $chooseOtherLink.on('click', e => {
            authTypeSelect.setProperties({value: 'phone'});
        });

        if (this.defaultAuthType && authTypeSelect) {
            authTypeChange({value: this.defaultAuthType});
        }
    }

    verifyCode(code, cb) {
        return new Promise((resolve, reject) => {
            let authType = 'phone';
            if (this.dialog.getBody().getChild('authTypeSelect')) {
                authType = this.dialog.getBody().getChild('authTypeSelect').getValue();
            }

            $.ajax({
                method: 'POST',
                url: '/api/iam/authcode/verify',
                dataType: 'json',
                headers: {
                    'Content-Type': 'application/json; charset=UTF-8',
                    'X-Request-By': 'ERApplication',
                },
                data: JSON.stringify({code, authtype: authType}),
                success(data) {
                    if (data.success === false || data.success === 'false') {
                        if (data.code === 'BceValidationException') {
                            reject(kInvalidCode);
                        }
                        else if (data.code === 'LimitReached') {
                            reject(kLimitReached);
                        }
                        else if (data.code === 'InternalError') {
                            reject(kInternalError);
                        }
                        else {
                            // 继续验证一次
                            resolve(code);
                        }
                    }
                    else {
                        cb(data.result).then(() => resolve(null));
                    }
                },
                error: reject,
            });
        });
    }

    /**
      * 获取验证码
      *
      * @return {Promise} 获取验证码的 promise
      */
    getAuthCode() {
        let authType = 'phone';
        if (this.dialog.getBody().getChild('authTypeSelect')) {
            authType = this.dialog.getBody().getChild('authTypeSelect').getValue();
        }

        return new Promise((resolve, reject) => {
            // 发送ajax请求
            // 等搞完 inf-ajax 就不用 jquery 了
            $.ajax({
                method: 'POST',
                url: '/api/iam/authcode/send',
                dataType: 'json',
                headers: {
                    'Content-Type': 'application/json; charset=UTF-8',
                    'X-Request-By': 'ERApplication',
                },
                data: JSON.stringify({authtype: authType}),
                success: resolve,
                error: reject,
            });
        });
    }

    getMfaDeviceStatus() {
        return api.getMfaDeviceStatus({type: 'totp'}).then(result => {
            const {status} = result;
            this.mfaDeviceStatus = status;
            if (!status) {
                this.createDevice();
            }
        });
    }

    createDevice() {
        return api.createDevice({type: 'totp'}).then(device => {
            const qrcode = new QRCode(document.getElementById('qrcode'), {
                text: device.secretSeedUrl,
                width: 116,
                height: 116,
                colorDark: '#000000',
                colorLight: '#ffffff',
                correctLevel: QRCode.CorrectLevel.H,
            });
            // eslint-disable-next-line no-console
            console.log('qrcode', qrcode);
        });
    }

    /**
      * 显示错误信息
      *
      * @param {string} msg 错误信息
      */
    showError(msg) {
        // $('#mfacheck-message', this.main).addClass('error').html(msg);
    }

    /**
      * 清理错误信息
      */
    clearError() {
        this.showMessage('');
    }

    /**
      * 显示消息
      *
      * @param {string} msg 消息
      */
    showMessage(msg) {
        // $('#mfacheck-message', this.main).removeClass('error').html(msg);
    }

    /**
      * 销毁处理
      */
    dispose() {
        if (this.dialog) {
            this.dialog.dispose();
            this.dialog = null;
        }
    }
}
