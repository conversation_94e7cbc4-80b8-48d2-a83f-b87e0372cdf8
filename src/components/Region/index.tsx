import React from 'react';
import {useFramework} from '@/hooks/useFramework';
import {PROJECT_NAME} from '@/dicts';

interface RegionProps{
    value: any;
    projectName?: string;
}

export default function Region({value, projectName = PROJECT_NAME}: RegionProps) {
    const {region: {getRegionList}} = useFramework();
    const regionList = getRegionList(projectName);
    return <span>{regionList[value]}</span>;
}
